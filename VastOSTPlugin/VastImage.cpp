/*
 *************************************************************************
 * Vast Data Image Implementation
 * Represents a NetBackup backup image stored in Vast Data S3
 *************************************************************************
 */

#include "VastImage.h"
#include "VastLSU.h"
#include "VastS3Client.h"
#include <iostream>
#include <cstring>
#include <sstream>
#include <ctime>
#include <chrono>
#include <iomanip>

// Helper function to get current timestamp
std::string getCurrentTimestamp() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::gmtime(&time_t), "%Y-%m-%dT%H:%M:%SZ");
    return ss.str();
}

VastImage::VastImage(VastLSU* lsu, const sts_image_def_v10_t* image_def)
    : m_lsu(lsu)
    , m_size(0)
    , m_block_size(64 * 1024)  // 64KB default
    , m_current_size(0)
    , m_is_open(false)
    , m_is_pending(false)
    , m_is_complete(false)
    , m_mode(0)
    , m_last_error(STS_SUCCESS)
    , m_metadata_dirty(false)
{
    if (image_def) {
        memcpy(&m_image_def, image_def, sizeof(sts_image_def_v10_t));
        m_image_name = std::string(image_def->img_basename);
        // Note: img_size is not a member of sts_image_def_v10_t, size will be set during operations
        m_size = 0;
    }
    
    m_s3_key = generateS3Key();
    m_metadata_key = generateMetadataKey();
    
    clearError();
}

VastImage::~VastImage()
{
    if (m_is_open) {
        close(1, 0);  // Force close
    }
}

int VastImage::create(int flags)
{
    std::cout << "VastImage: Creating image " << m_image_name << std::endl;
    
    clearError();
    
    if (m_is_open) {
        setError(STS_ERR_INVALID_PARAMETER, "Image already open");
        return STS_ERR_INVALID_PARAMETER;
    }
    
    try {
        m_mode = STS_MODE_WRITE;
        m_is_pending = (flags & STS_IC_PENDING) != 0;
        m_is_open = true;
        m_is_complete = false;
        
        // Initialize multipart upload for large images
        if (m_size > 100 * 1024 * 1024) {  // > 100MB
            int result = initializeMultipartUpload();
            if (result != STS_SUCCESS) {
                m_is_open = false;
                return result;
            }
        }
        
        std::cout << "VastImage: Successfully created image " << m_image_name << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Image creation failed: ") + e.what());
        m_is_open = false;
        return STS_ERR_INTERNAL;
    }
}

int VastImage::open(int mode)
{
    std::cout << "VastImage: Opening image " << m_image_name << " in mode " << mode << std::endl;
    
    clearError();
    
    if (m_is_open) {
        setError(STS_ERR_INVALID_PARAMETER, "Image already open");
        return STS_ERR_INVALID_PARAMETER;
    }
    
    try {
        m_mode = mode;
        m_is_open = true;
        m_is_pending = false;
        m_is_complete = true;
        
        // Load metadata if reading
        if (mode == STS_MODE_READ) {
            int result = loadMetadata();
            if (result != STS_SUCCESS) {
                m_is_open = false;
                return result;
            }
        }
        
        std::cout << "VastImage: Successfully opened image " << m_image_name << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Image opening failed: ") + e.what());
        m_is_open = false;
        return STS_ERR_INTERNAL;
    }
}

int VastImage::close(int complete_flag, int force_flag)
{
    std::cout << "VastImage: Closing image " << m_image_name << std::endl;
    
    if (!m_is_open && !force_flag) {
        return STS_SUCCESS;
    }
    
    try {
        // Complete multipart upload if active
        if (m_multipart_upload.active) {
            if (complete_flag) {
                int result = completeMultipartUpload();
                if (result != STS_SUCCESS && !force_flag) {
                    return result;
                }
            } else {
                abortMultipartUpload();
            }
        }
        
        // Save metadata if dirty
        if (m_metadata_dirty && complete_flag) {
            saveMetadata();
        }
        
        m_is_open = false;
        m_is_complete = complete_flag != 0;
        
        std::cout << "VastImage: Successfully closed image " << m_image_name << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Image closing failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastImage::remove()
{
    std::cout << "VastImage: Removing image " << m_image_name << std::endl;
    
    try {
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_ERR_INTERNAL, "S3 client not available");
            return STS_ERR_INTERNAL;
        }
        
        std::string bucket_name = getBucketName();
        
        // Delete main object
        int result = s3_client->deleteObject(bucket_name, m_s3_key);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to delete S3 object");
            return result;
        }
        
        // Delete metadata object
        s3_client->deleteObject(bucket_name, m_metadata_key);
        
        std::cout << "VastImage: Successfully removed image " << m_image_name << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Image removal failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastImage::read(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_read)
{
    if (!buf || !bytes_read) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    *bytes_read = 0;
    
    if (!m_is_open || m_mode != STS_MODE_READ) {
        setError(STS_ERR_INVALID_PARAMETER, "Image not open for reading");
        return STS_ERR_INVALID_PARAMETER;
    }
    
    try {
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_ERR_INTERNAL, "S3 client not available");
            return STS_ERR_INTERNAL;
        }
        
        std::string bucket_name = getBucketName();
        
        int result = s3_client->getObjectRange(bucket_name, m_s3_key, buf, offset, length, bytes_read);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to read from S3 object");
            return result;
        }
        
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Read operation failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastImage::write(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_written)
{
    if (!buf || !bytes_written) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    *bytes_written = 0;
    
    if (!m_is_open || m_mode != STS_MODE_WRITE) {
        setError(STS_ERR_INVALID_PARAMETER, "Image not open for writing");
        return STS_ERR_INVALID_PARAMETER;
    }
    
    try {
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_ERR_INTERNAL, "S3 client not available");
            return STS_ERR_INTERNAL;
        }
        
        std::string bucket_name = getBucketName();
        
        // Use multipart upload for large writes
        if (m_multipart_upload.active) {
            std::string etag;
            int result = uploadPart(buf, length, m_multipart_upload.next_part_number);
            if (result != STS_SUCCESS) {
                return result;
            }
            m_multipart_upload.next_part_number++;
        } else {
            // Simple put for small writes
            int result = s3_client->putObjectRange(bucket_name, m_s3_key, buf, offset, length);
            if (result != STS_SUCCESS) {
                setError(result, "Failed to write to S3 object");
                return result;
            }
        }
        
        *bytes_written = length;
        m_current_size = std::max(m_current_size, offset + length);
        
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Write operation failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

std::string VastImage::getBucketName() const
{
    if (m_lsu) {
        return m_lsu->getBucketName();
    }
    return "";
}

VastS3Client* VastImage::getS3Client()
{
    if (m_lsu) {
        return m_lsu->getS3Client();
    }
    return nullptr;
}

void VastImage::setError(int error_code, const std::string& error_msg)
{
    m_last_error = error_code;
    m_last_error_msg = error_msg;
    std::cerr << "VastImage Error [" << error_code << "]: " << error_msg << std::endl;
}

void VastImage::clearError()
{
    m_last_error = STS_SUCCESS;
    m_last_error_msg.clear();
}

std::string VastImage::generateS3Key()
{
    return "netbackup/" + m_image_name + "/" + std::string(m_image_def.img_date) + "/data";
}

std::string VastImage::generateMetadataKey()
{
    return "netbackup/" + m_image_name + "/" + std::string(m_image_def.img_date) + "/metadata";
}

int VastImage::initializeMultipartUpload()
{
    try {
        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            setError(STS_ERR_INTERNAL, "S3 client not available");
            return STS_ERR_INTERNAL;
        }

        // Initialize multipart upload for large images
        std::string bucket_name = m_lsu->getBucketName();
        std::string object_key = generateS3Key();

        int result = s3_client->initiateMultipartUpload(bucket_name, object_key, m_multipart_upload.upload_id);
        if (result != 0) {
            setError(STS_ERR_INTERNAL, "Failed to initialize multipart upload");
            return STS_ERR_INTERNAL;
        }

        m_multipart_upload.active = true;
        m_multipart_upload.next_part_number = 1;

        std::cout << "VastImage: Multipart upload initialized: " << m_multipart_upload.upload_id << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Multipart upload init failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastImage::completeMultipartUpload()
{
    try {
        if (!m_multipart_upload.active) {
            return STS_SUCCESS; // Already completed or not started
        }

        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            setError(STS_ERR_INTERNAL, "S3 client not available");
            return STS_ERR_INTERNAL;
        }

        // Complete multipart upload
        std::string bucket_name = m_lsu->getBucketName();
        std::string object_key = generateS3Key();

        int result = s3_client->completeMultipartUpload(bucket_name, object_key,
                                                       m_multipart_upload.upload_id,
                                                       m_multipart_upload.parts);
        if (result != 0) {
            setError(STS_ERR_INTERNAL, "Failed to complete multipart upload");
            return STS_ERR_INTERNAL;
        }

        m_multipart_upload.active = false;
        m_multipart_upload.upload_id.clear();
        m_multipart_upload.parts.clear();

        std::cout << "VastImage: Multipart upload completed successfully" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Multipart upload completion failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastImage::abortMultipartUpload()
{
    try {
        if (!m_multipart_upload.active) {
            return STS_SUCCESS; // Already aborted or not started
        }

        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            setError(STS_ERR_INTERNAL, "S3 client not available");
            return STS_ERR_INTERNAL;
        }

        // Abort multipart upload
        std::string bucket_name = m_lsu->getBucketName();
        std::string object_key = generateS3Key();

        int result = s3_client->abortMultipartUpload(bucket_name, object_key, m_multipart_upload.upload_id);
        if (result != 0) {
            // Log warning but don't fail - cleanup is best effort
            std::cout << "VastImage: Warning - Failed to abort multipart upload: " << m_multipart_upload.upload_id << std::endl;
        }

        m_multipart_upload.active = false;
        m_multipart_upload.upload_id.clear();
        m_multipart_upload.parts.clear();

        std::cout << "VastImage: Multipart upload aborted" << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Multipart upload abort failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastImage::uploadPart(const void* data, sts_uint64_t size, int part_number)
{
    try {
        if (!m_multipart_upload.active) {
            setError(STS_ERR_INTERNAL, "Multipart upload not active");
            return STS_ERR_INTERNAL;
        }

        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            setError(STS_ERR_INTERNAL, "S3 client not available");
            return STS_ERR_INTERNAL;
        }

        // Upload part
        std::string bucket_name = m_lsu->getBucketName();
        std::string object_key = generateS3Key();

        std::string etag;
        int result = s3_client->uploadPart(bucket_name, object_key, m_multipart_upload.upload_id,
                                          part_number, data, size, etag);
        if (result != 0) {
            setError(STS_ERR_INTERNAL, "Failed to upload part " + std::to_string(part_number));
            return STS_ERR_INTERNAL;
        }

        // Store part info for completion
        VastS3PartInfo part_info;
        part_info.part_number = part_number;
        part_info.etag = etag;
        m_multipart_upload.parts.push_back(part_info);

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Upload part failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastImage::loadMetadata()
{
    try {
        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            return STS_SUCCESS; // Can't load without S3 client
        }

        // Generate metadata key
        std::string bucket_name = m_lsu->getBucketName();
        std::string metadata_key = generateMetadataKey();

        // Try to get metadata object
        VastS3ObjectInfo object_info;
        int result = s3_client->headObject(bucket_name, metadata_key, object_info);
        if (result != 0) {
            // No metadata object exists yet
            return STS_SUCCESS;
        }

        // In a real implementation, we would:
        // 1. Download the metadata object
        // 2. Parse the metadata (JSON/XML)
        // 3. Update internal metadata structures

        std::cout << "VastImage: Loaded metadata for image " << m_image_name << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Load metadata failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastImage::saveMetadata()
{
    try {
        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            m_metadata_dirty = false;
            return STS_SUCCESS; // Can't save without S3 client
        }

        // Generate metadata content (simplified JSON)
        std::string metadata_content = "{\n";
        metadata_content += "  \"image_name\": \"" + m_image_name + "\",\n";
        metadata_content += "  \"image_size\": " + std::to_string(m_current_size) + ",\n";
        metadata_content += "  \"image_date\": \"" + std::string(m_image_def.img_date) + "\",\n";
        metadata_content += "  \"image_flags\": " + std::to_string(m_image_def.img_flags) + ",\n";
        metadata_content += "  \"created_time\": \"" + std::to_string(time(nullptr)) + "\"\n";
        metadata_content += "}";

        // Upload metadata object
        std::string bucket_name = m_lsu->getBucketName();
        std::string metadata_key = generateMetadataKey();

        int result = s3_client->putObject(bucket_name, metadata_key,
                                         metadata_content.data(), metadata_content.size());
        if (result != 0) {
            setError(STS_ERR_INTERNAL, "Failed to save metadata");
            return STS_ERR_INTERNAL;
        }

        m_metadata_dirty = false;
        std::cout << "VastImage: Saved metadata for image " << m_image_name << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Save metadata failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastImage::readMetadata(void* buf, sts_uint64_t len, sts_uint64_t offset, sts_uint64_t* bytesread)
{
    if (!buf || !bytesread) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        *bytesread = 0;

        // Get S3 client from LSU
        VastS3Client* s3_client = m_lsu ? m_lsu->getS3Client() : nullptr;
        if (!s3_client) {
            return STS_SUCCESS; // Can't read without S3 client
        }

        // Generate metadata key
        std::string bucket_name = m_lsu->getBucketName();
        std::string metadata_key = generateMetadataKey();

        // Read metadata object with range
        int result = s3_client->getObjectRange(bucket_name, metadata_key, offset, len, buf, bytesread);
        if (result != 0) {
            // Metadata object doesn't exist or read failed
            return STS_SUCCESS;
        }

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Read metadata failed: ") + e.what());
        *bytesread = 0;
        return STS_ERR_INTERNAL;
    }
}
