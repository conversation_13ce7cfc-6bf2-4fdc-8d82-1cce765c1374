/*
 * OST SDK Type Wrapper for macOS compatibility
 * This file provides a compatibility layer for OST SDK types on macOS
 */

#ifndef _STS_TYPE_WRAPPER_H_
#define _STS_TYPE_WRAPPER_H_

#ifdef USE_MACOS_COMPAT_HEADERS
    // Use our custom macOS compatibility headers
    #include "macos_ststypes.h"  // Include types first
#else
    // Use the original OST SDK platform headers
    #include "platforms/linuxR_x64/ststype.h"
#endif

#endif /* _STS_TYPE_WRAPPER_H_ */
