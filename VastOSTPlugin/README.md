# Vast Data OpenStorage Plugin for NetBackup

This is an implementation of a NetBackup OpenStorage (OST) plugin for Vast Data storage systems. The plugin integrates NetBackup with Vast Data's unified storage platform using both the VMS REST API for management operations and S3 API for data operations.

## Architecture Overview

The plugin consists of several key components:

### Core Components

1. **vastplugin.h/cpp** - Main OST API implementation and entry points
2. **VastStorageServer** - Manages connections to Vast Data cluster
3. **VastRestClient** - Handles VMS REST API communication for management operations
4. **VastS3Client** - Handles S3 API communication for data operations

### Key Features

- **Dual API Integration**: Uses VMS REST API for control plane and S3 API for data plane
- **JWT Authentication**: Implements Vast Data's token-based authentication
- **S3 Compatibility**: Leverages Vast Data's S3-compatible interface for data storage
- **LSU Management**: Maps NetBackup LSUs to Vast Data buckets/volumes
- **Event Support**: Implements OST event handling for async operations
- **Multipart Uploads**: Supports efficient handling of large backup images

## Vast Data API Integration

Based on the included Vast Data API documentation, the plugin integrates with:

### VMS REST API Endpoints (Control Plane)
- `/api/token/` - Authentication and token management
- `/api/clusters/` - Cluster information and status (corrected from `/api/cluster/status/`)
- `/api/vms/` - VMS system information (corrected from `/api/system/info/`)
- `/api/tenants/` - Tenant management for multi-tenancy
- `/api/views/` - S3-enabled view discovery and configuration context
- `/api/s3keys/` - S3 access key management (creates access/secret keys)
- `/api/quotas/` - Storage quota management
- `/api/snapshots/` - Snapshot operations
- `/api/replication/` - Replication management

### S3 API Operations (Data Plane - using AWS S3 SDK)
- **LSU Operations**: Bucket creation, deletion, and listing via standard AWS S3 API
- **Image Operations**: Object storage and retrieval using S3 credentials from `/api/s3keys/`
- **Performance**: Multipart uploads for large backup images
- **Metadata**: Object metadata and lifecycle management
- **Authentication**: Uses access/secret keys obtained from VMS `/api/s3keys/` endpoint

### API Usage Flow for S3 Object Storage:
1. **Authenticate**: POST `/api/token/` to get VMS access token
2. **Discover Context**: GET `/api/views/` to find S3-enabled views and tenant info
3. **Get S3 Credentials**: POST `/api/s3keys/` to create S3 access/secret keys
4. **Enumerate LSUs**: Use AWS S3 SDK `ListBuckets()` to find existing LSUs
5. **Data Operations**: Use AWS S3 SDK for all backup I/O operations

## Prerequisites

### Dependencies
- **libcurl** - For HTTP/HTTPS operations
- **OpenSSL** - For SSL/TLS and cryptographic operations
- **JSON library** - For API response parsing (nlohmann/json or jsoncpp)
- **NetBackup OST SDK 11.1.1** - NetBackup OpenStorage SDK

### System Requirements
- Linux x86_64 (RHEL/CentOS 7+, Ubuntu 18.04+)
- NetBackup 8.1.2 or later
- Vast Data cluster with VMS and S3 endpoints accessible
- Network connectivity to Vast Data cluster

## Installation

### 1. Build the Plugin

```bash
# Ensure OST SDK is available
cd VastOSTPlugin
make clean
make

# For debug build
make debug
```

### 2. Install the Plugin

```bash
# Copy to NetBackup library directory
sudo make install

# Or manually copy
sudo cp libvastplugin.so /usr/openv/lib/
```

### 3. Configure the Plugin

```bash
# Copy configuration template
sudo cp vast_config.conf /usr/openv/etc/
sudo chown root:root /usr/openv/etc/vast_config.conf
sudo chmod 600 /usr/openv/etc/vast_config.conf

# Edit configuration
sudo vi /usr/openv/etc/vast_config.conf
```

### 4. Register with NetBackup

```bash
# Add storage server
/usr/openv/netbackup/bin/admincmd/nbdevconfig -creatests -storage_server vast://your-vast-cluster.com:443 -stype VastData -vendor "Vast Data" -media_server your_media_server

# Configure credentials
/usr/openv/netbackup/bin/admincmd/nbdevconfig -changestspassword -storage_server vast://your-vast-cluster.com:443 -user your_username -password your_password
```

## Configuration

Edit `/usr/openv/etc/vast_config.conf`:

### Basic Configuration
```ini
[VastData]
vms_endpoint = https://your-vast-cluster.com
s3_endpoint = https://your-vast-cluster.com
s3_region = us-east-1
verify_ssl = true
```

### Performance Tuning
```ini
multipart_threshold = 64MB
multipart_chunk_size = 16MB
max_concurrent_uploads = 4
buffer_size = 1MB
```

### Authentication
```ini
auth_method = token
token_refresh_interval = 3600
```

## Usage

### Server URL Format
Use the following format for storage server names:
```
vast://vms-endpoint:port
```

Example:
```
vast://vast-cluster.example.com:443
```

### LSU Mapping for S3 Object Storage

#### **Core Mapping: S3 Buckets = LSUs**
- **NetBackup LSUs map directly to Vast Data S3 buckets**
- Each S3 bucket becomes one OST Logical Storage Unit (LSU)
- Bucket names use configurable prefix (default: `netbackup-`)
- Each LSU corresponds to a dedicated bucket for backup images

#### **Views Provide S3 Context and Organization**
- **Views are NOT LSUs** - they provide configuration context for S3 operations
- Views define which tenants can create S3 buckets
- Views with `s3_enabled=true` indicate where S3 buckets can be created
- Views provide storage policies, quotas, and access control for S3 buckets

#### **Complete Architecture Flow:**
```
Vast Data Storage (S3 Object Storage Mode)
├── Tenant: "production"
│   ├── View: "netbackup-prod" (s3_enabled=true)
│   │   ├── S3 Bucket: "netbackup-prod-daily" ← LSU 1
│   │   ├── S3 Bucket: "netbackup-prod-weekly" ← LSU 2
│   │   └── S3 Bucket: "netbackup-prod-monthly" ← LSU 3
│   └── View: "netbackup-archive" (s3_enabled=true)
│       └── S3 Bucket: "netbackup-archive-longterm" ← LSU 4
├── Tenant: "development"
│   └── View: "netbackup-dev" (s3_enabled=true)
│       └── S3 Bucket: "netbackup-dev-testing" ← LSU 5
```

#### **Why Both Views and S3 Buckets are Needed:**
1. **Views provide tenant isolation** - Organize S3 buckets by tenant/department
2. **Views define storage policies** - QoS, retention, encryption settings
3. **Views enable quota management** - Capacity limits for S3 storage
4. **Views control access** - Who can create/access S3 buckets
5. **S3 Buckets store data** - Actual backup images and objects

#### **Implementation Strategy:**
1. **Discovery Phase**: Use `/api/views/` to find S3-enabled views
2. **Context Setup**: Get tenant and policy information from views
3. **LSU Enumeration**: Use AWS S3 SDK to list buckets within view context
4. **Data Operations**: All backup I/O uses S3 API on buckets (LSUs)

### Image Storage
- Backup images stored as S3 objects
- Large images use multipart uploads for efficiency
- Metadata stored as S3 object tags or separate metadata objects
- Supports server-side encryption if configured

## Development Status

### ✅ **IMPLEMENTED - Core Plugin Functions**
- **Plugin Lifecycle**: 
  - `stspi_init()` - Plugin initialization with version checking
  - `stspi_claim()` - Server name validation for Vast Data URLs
  - `stspi_terminate()` - Clean shutdown and resource cleanup

- **Server Management**:
  - `stspi_get_server_prop_byname()` - Server property retrieval
  - `stspi_open_server()` - Connection establishment with authentication
  - `stspi_get_server_prop()` - Connected server information
  - `stspi_close_server()` - Clean connection termination

- **LSU (Logical Storage Unit) Management**:
  - `stspi_open_lsu_list_v11()` - LSU enumeration
  - `stspi_open_lsu_list_v9()` - Legacy LSU enumeration  
  - `stspi_list_lsu()` - LSU iteration
  - `stspi_close_lsu_list()` - LSU list cleanup
  - `stspi_get_lsu_prop_byname_v9()` - LSU properties (v9)
  - `stspi_get_lsu_prop_byname_v11()` - LSU properties (v11)
  - `stspi_label_lsu()` - LSU labeling

- **Event Management**:
  - `stspi_open_evchannel_v11()` - Event channel establishment
  - `stspi_get_event_v11()` - Event retrieval
  - `stspi_close_evchannel_v9()` - Event channel cleanup

- **Plugin Framework**:
  - Complete header definitions for all OST API functions
  - VMS REST API client framework
  - S3 client framework
  - Configuration management
  - Build system (Makefile and CMake)
  - Server connection caching and management

### ✅ **IMPLEMENTED - Image Operations**
- **Image Management Functions**:
  - `stspi_get_image_prop_byname_v9()` - Image metadata retrieval (v9) ✅
  - `stspi_get_image_prop_byname_v10()` - Image metadata retrieval (v10) ✅
  - `stspi_create_image_v9()` - Image creation ✅
  - `stspi_open_image_v9()` - Image opening for I/O ✅
  - `stspi_delete_image_v9()` - Image deletion ✅
  - `stspi_write_image_meta()` - Metadata writing ✅
  - `stspi_read_image_meta()` - Metadata reading ✅
  - `stspi_flush_image()` - Image flush operations ✅
  - `stspi_get_image_info()` - Image information retrieval ✅

- **Advanced Operations**:
  - `stspi_async_copy_image_v11()` - Asynchronous image copying ✅
  - `stspi_copy_image_v9()` - Synchronous image copying ✅
  - Various other copy and async operations ✅

### ✅ **IMPLEMENTED - Backend Integration**
- **VastRestClient HTTP Implementation**:
  - Complete HTTP request/response handling using libcurl ✅
  - JWT token management and refresh ✅
  - VMS REST API endpoint communication ✅
  - JSON parsing and error handling using jsoncpp ✅
  - Authentication, views, buckets, quotas management ✅

- **VastS3Client Implementation**:
  - Complete S3 API HTTP operations ✅
  - Object creation, reading, writing, deletion ✅
  - Multipart upload handling ✅
  - AWS Signature V4 authentication ✅
  - Bucket operations and object metadata ✅

- **VastLSU and VastImage Classes**:
  - Complete LSU management (maps to Vast buckets/volumes) ✅
  - Image I/O operations with S3 backend ✅
  - Metadata storage and retrieval ✅
  - Error handling and retry logic ✅

### 📋 **Implementation Status - COMPLETE**
1. **Phase 1**: Complete VastRestClient HTTP implementation ✅
2. **Phase 2**: Complete VastS3Client S3 API implementation ✅
3. **Phase 3**: Implement image I/O operations (read/write/create/delete) ✅
4. **Phase 4**: Add advanced features (copy, async operations) ✅
5. **Phase 5**: Performance optimization and comprehensive testing 🔄

### 🔄 **Current Focus Areas**
1. **Testing and Validation**: Comprehensive testing with real Vast Data clusters
2. **Performance Optimization**: Fine-tuning for production workloads
3. **Error Handling**: Enhanced error recovery and retry mechanisms
4. **Documentation**: Deployment and configuration guides

## API Reference

### Key Classes

#### VastStorageServer
- ✅ Manages connection to Vast Data cluster
- ✅ Handles authentication and token refresh
- ✅ Provides access to REST and S3 clients
- ✅ Connection caching and management
- ✅ LSU enumeration and management

#### VastRestClient
- ✅ Complete VMS REST API communication using libcurl
- ✅ Authentication, views, buckets, quotas management
- ✅ JSON request/response processing using jsoncpp
- ✅ Error handling and retry logic
- ✅ Token management and refresh

#### VastS3Client
- ✅ Complete S3 API operations implementation
- ✅ Object storage and retrieval with AWS Signature V4
- ✅ Multipart upload support for large objects
- ✅ Bucket operations and metadata handling
- ✅ Error handling and retry mechanisms

#### VastLSU
- ✅ LSU (Logical Storage Unit) management
- ✅ Maps to Vast Data buckets/volumes
- ✅ Capacity and usage tracking
- ✅ Image creation and management

#### VastImage
- ✅ Individual backup image handling
- ✅ S3-based data operations (read/write)
- ✅ Metadata storage and retrieval
- ✅ Multipart upload for large images

### OST API Functions Status

#### ✅ Fully Implemented
- **Plugin lifecycle management**: init, claim, terminate
- **Server connection management**: open, close, properties
- **LSU enumeration and management**: list, properties, creation
- **Event channel management**: open, get events, close
- **Image operations**: create, open, read, write, delete, copy
- **Image metadata operations**: read/write metadata, properties
- **Advanced operations**: async copy, flush, image info
- **Authentication**: VMS token-based auth, S3 signature auth
- **Error handling and logging**: comprehensive error reporting

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Verify VMS endpoint accessibility
   - Check SSL certificates
   - Validate credentials

2. **Authentication Errors**
   - Ensure user has appropriate permissions
   - Check token refresh configuration
   - Verify username/password

3. **Build Issues**
   - Install required dependencies (libcurl, OpenSSL)
   - Verify OST SDK path in Makefile
   - Check compiler version (C++11 support required)

### Logging
- Plugin logs to `/var/log/netbackup/vast_plugin.log`
- Set `log_level = DEBUG` for detailed logging
- Enable `trace_api_calls = true` for API debugging

## Contributing

The plugin implementation is **COMPLETE** with all core OST functions fully implemented. Current focus areas:

1. **Testing and Validation** - Comprehensive testing with real Vast Data clusters
2. **Performance Optimization** - Fine-tuning for production workloads
3. **Enhanced Error Handling** - Advanced error recovery and retry mechanisms
4. **Production Deployment** - Installation guides and configuration documentation
5. **Monitoring and Logging** - Enhanced observability for production environments

## License

Copyright 2025 - Vast Data Inc., All Rights Reserved

## Support

For issues and questions:
- Review NetBackup OST SDK documentation
- Check Vast Data API documentation (included in workspace)
- Consult NetBackup administration guides