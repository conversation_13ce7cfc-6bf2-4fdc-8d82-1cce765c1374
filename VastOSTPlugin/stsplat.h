/*
 * Platform compatibility wrapper for OST SDK
 * Includes the appropriate platform headers based on build target
 */

#ifndef _STS_PLATFORM_WRAPPER_H_
#define _STS_PLATFORM_WRAPPER_H_

#ifdef USE_MACOS_COMPAT_HEADERS
    // Use our custom macOS compatibility headers
    #include "macos_ststypes.h"  // Include types first
    #include "macos_stsplat.h"
#else
    // Use the original OST SDK platform headers
    #include "platforms/linuxR_x64/stsplat.h"
#endif

#endif /* _STS_PLATFORM_WRAPPER_H_ */
