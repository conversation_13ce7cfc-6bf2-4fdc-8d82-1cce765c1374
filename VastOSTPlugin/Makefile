#
# Makefile for Vast Data OpenStorage Plugin
#

# Platform detection (following OST SDK sample pattern)
UNAME_S := $(shell uname -s)
UNAME_M := $(shell uname -m)

# Set platform based on OS and architecture
ifeq ($(UNAME_S),Linux)
    ifeq ($(UNAME_M),x86_64)
        PLATFORM = linuxR_x64
    endif
endif

# Default to linuxR_x64 if not detected
ifndef PLATFORM
    PLATFORM = linuxR_x64
endif

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++11 -fPIC -Wall -Wextra -O2 -DVAST_PGN_EXPORTS -DSTS_EXTERNAL_VENDOR
INCLUDES = -I../OST-SDK-11.1.1/src/include -I../OST-SDK-11.1.1/src/include/platforms/$(PLATFORM) -I.
LIBS = -lcurl -lssl -lcrypto -ljsoncpp

# Target settings
TARGET = libvastplugin.so
SOURCES = vastplugin.cpp VastStorageServer.cpp VastRestClient.cpp VastS3Client.cpp VastLSU.cpp VastImage.cpp
OBJECTS = $(SOURCES:.cpp=.o)

# Build rules
all: platform-info $(TARGET)

platform-info:
	@echo "Building for platform: $(PLATFORM)"
	@echo "Include paths: $(INCLUDES)"

$(TARGET): $(OBJECTS)
	$(CXX) -shared -o $@ $^ $(LIBS)

%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Dependencies
vastplugin.o: vastplugin.cpp vastplugin.h VastStorageServer.h VastRestClient.h VastS3Client.h VastLSU.h VastImage.h
VastStorageServer.o: VastStorageServer.cpp VastStorageServer.h VastRestClient.h VastS3Client.h VastLSU.h
VastRestClient.o: VastRestClient.cpp VastRestClient.h
VastS3Client.o: VastS3Client.cpp VastS3Client.h
VastLSU.o: VastLSU.cpp VastLSU.h VastStorageServer.h VastS3Client.h VastImage.h
VastImage.o: VastImage.cpp VastImage.h VastLSU.h VastS3Client.h

# Utility targets
clean:
	rm -f $(OBJECTS) $(TARGET)

install: $(TARGET)
	cp $(TARGET) /usr/openv/lib/

debug: CXXFLAGS += -g -DDEBUG
debug: $(TARGET)

.PHONY: all clean install debug platform-info

# Notes:
# - Requires libcurl for HTTP operations
# - Requires OpenSSL for HTTPS/encryption
# - Requires a JSON library (e.g., nlohmann/json or jsoncpp)
# - OST SDK headers are in ../OST-SDK-11.1.1/src/include