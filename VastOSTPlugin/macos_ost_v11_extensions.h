/*
 * macOS OST SDK v11 Extensions
 * Defines missing OST SDK v11 struct types that depend on base OST SDK types
 * This file must be included AFTER the main OST SDK headers
 */

#ifndef _MACOS_OST_V11_EXTENSIONS_H_
#define _MACOS_OST_V11_EXTENSIONS_H_

// Only define these if we're using macOS compatibility headers
#ifdef USE_MACOS_COMPAT_HEADERS

// Missing OST SDK v11 struct types - define as placeholders if not available
#ifndef STS_IMAGE_LIST_DEF_V10_T_DEFINED
typedef struct {
    sts_uint32_t count;
    sts_image_def_v10_t* images;
} sts_image_list_def_v10_t;
#define STS_IMAGE_LIST_DEF_V10_T_DEFINED
#endif

#ifndef STS_OPERATION_INFO_V11_T_DEFINED
typedef struct {
    sts_uint32_t operation_id;
    sts_uint32_t status;
    char description[256];
} sts_operation_info_v11_t;
#define STS_OPERATION_INFO_V11_T_DEFINED
#endif

#ifndef STS_PLUGIN_INFO_V11_T_DEFINED
typedef struct {
    char plugin_name[64];
    char plugin_version[32];
    sts_uint32_t capabilities;
} sts_plugin_info_v11_t;
#define STS_PLUGIN_INFO_V11_T_DEFINED
#endif

#ifndef STS_HEALTH_INFO_V11_T_DEFINED
typedef struct {
    sts_uint32_t health_status;
    char health_message[256];
} sts_health_info_v11_t;
#define STS_HEALTH_INFO_V11_T_DEFINED
#endif

#ifndef STS_STATISTICS_V11_T_DEFINED
typedef struct {
    sts_uint64_t bytes_written;
    sts_uint64_t bytes_read;
    sts_uint32_t operations_count;
} sts_statistics_v11_t;
#define STS_STATISTICS_V11_T_DEFINED
#endif

#endif /* USE_MACOS_COMPAT_HEADERS */

#endif /* _MACOS_OST_V11_EXTENSIONS_H_ */
