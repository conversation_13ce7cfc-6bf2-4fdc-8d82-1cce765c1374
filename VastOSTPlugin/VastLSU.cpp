/*
 *************************************************************************
 * Vast Data LSU (Logical Storage Unit) Implementation
 * Represents a Vast Data volume/bucket for NetBackup storage
 *************************************************************************
 */

#include "VastLSU.h"
#include "VastStorageServer.h"
#include "VastS3Client.h"
#include "VastImage.h"
#include <iostream>
#include <cstring>

VastLSU::VastLSU(const std::string& name, VastStorageServer* server)
    : m_name(name)
    , m_server(server)
    , m_total_capacity(1024ULL * 1024 * 1024 * 1024)  // 1TB default
    , m_used_capacity(0)
    , m_available_capacity(m_total_capacity)
    , m_image_count(0)
    , m_last_error(STS_SUCCESS)
    , m_max_transfer_size(64 * 1024 * 1024)  // 64MB
    , m_block_size(64 * 1024)  // 64KB
    , m_lsu_flags(0)
    , m_status("online")
{
    clearError();
}

VastLSU::~VastLSU()
{
    // Clean up image cache
    for (auto* image : m_image_cache) {
        delete image;
    }
    m_image_cache.clear();
}

int VastLSU::initialize(const std::string& bucket_name)
{
    std::cout << "VastLSU: Initializing LSU " << m_name << std::endl;
    
    clearError();
    
    if (bucket_name.empty()) {
        m_bucket_name = m_name;  // Use LSU name as bucket name
    } else {
        m_bucket_name = bucket_name;
    }
    
    m_path = "/" + m_bucket_name;

    // Verify bucket exists if we have S3 client access
    VastS3Client* s3_client = getS3Client();
    if (s3_client) {
        int result = s3_client->headBucket(m_bucket_name);
        if (result != 0) {
            std::cout << "VastLSU: Warning - Bucket " << m_bucket_name << " may not exist yet" << std::endl;
        }
    }

    std::cout << "VastLSU: Successfully initialized LSU " << m_name
              << " with bucket " << m_bucket_name << std::endl;
    
    return STS_SUCCESS;
}

int VastLSU::updateInfo()
{
    try {
        // Get S3 client to check bucket status and size
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            return STS_SUCCESS; // Can't update without S3 client
        }

        // Check if bucket exists
        int result = s3_client->headBucket(m_bucket_name);
        if (result != 0) {
            std::cout << "VastLSU: Bucket " << m_bucket_name << " not accessible" << std::endl;
            return STS_ERR_INTERNAL;
        }

        // In a real implementation, we would:
        // 1. List objects to calculate used space
        // 2. Get bucket policies and quotas from VMS
        // 3. Update capacity information

        std::cout << "VastLSU: Updated info for LSU " << m_name << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Update info failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastLSU::setLabel(const std::string& label)
{
    m_label = label;
    return STS_SUCCESS;
}

VastImage* VastLSU::createImage(const sts_image_def_v10_t* image_def, int flags)
{
    if (!image_def) {
        setError(STS_ERR_INVALID_PARAMETER, "Invalid image definition");
        return nullptr;
    }
    
    try {
        VastImage* image = new VastImage(this, image_def);
        int result = image->create(flags);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to create image: " + image->getLastErrorMessage());
            delete image;
            return nullptr;
        }
        
        m_image_cache.push_back(image);
        m_image_count++;
        
        return image;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Image creation failed: ") + e.what());
        return nullptr;
    }
}

VastImage* VastLSU::openImage(const sts_image_def_v10_t* image_def, int mode)
{
    if (!image_def) {
        setError(STS_ERR_INVALID_PARAMETER, "Invalid image definition");
        return nullptr;
    }
    
    try {
        VastImage* image = new VastImage(this, image_def);
        int result = image->open(mode);
        if (result != STS_SUCCESS) {
            setError(result, "Failed to open image: " + image->getLastErrorMessage());
            delete image;
            return nullptr;
        }
        
        m_image_cache.push_back(image);
        
        return image;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Image opening failed: ") + e.what());
        return nullptr;
    }
}

int VastLSU::deleteImage(const sts_image_def_v10_t* image_def, int async_flag)
{
    if (!image_def) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get S3 client
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_ERR_INTERNAL, "S3 client not available");
            return STS_ERR_INTERNAL;
        }

        // Generate S3 object key for the image
        std::string object_key = generateImageKey(image_def);

        // Delete the S3 object
        int result = s3_client->deleteObject(m_bucket_name, object_key);
        if (result != 0) {
            setError(STS_ERR_INTERNAL, "Failed to delete S3 object: " + object_key);
            return STS_ERR_INTERNAL;
        }

        // Also delete metadata object if it exists
        std::string metadata_key = object_key + ".metadata";
        s3_client->deleteObject(m_bucket_name, metadata_key); // Best effort, ignore errors

        std::cout << "VastLSU: Successfully deleted image " << image_def->img_basename << std::endl;
        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Image deletion failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastLSU::getImageList(std::vector<VastImage*>& image_list)
{
    image_list = m_image_cache;
    return STS_SUCCESS;
}

int VastLSU::getImageInfo(const sts_image_def_v10_t* image_def, sts_image_info_v10_t* image_info)
{
    if (!image_def || !image_info) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Get S3 client
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            setError(STS_ERR_INTERNAL, "S3 client not available");
            return STS_ERR_INTERNAL;
        }

        // Generate S3 object key for the image
        std::string object_key = generateImageKey(image_def);

        // Get object metadata from S3
        VastS3ObjectInfo object_info;
        int result = s3_client->headObject(m_bucket_name, object_key, object_info);
        if (result != 0) {
            setError(STS_ERR_INVALID_PARAMETER, "Image not found: " + std::string(image_def->img_basename));
            return STS_ERR_INVALID_PARAMETER;
        }

        // Fill in image info structure
        memset(image_info, 0, sizeof(sts_image_info_v10_t));
        image_info->version = 10;
        memcpy(&image_info->imo_def, image_def, sizeof(sts_image_def_v10_t));
        image_info->imo_size = object_info.size;
        image_info->imo_status = STS_II_IMAGE_CREATED | STS_II_FILES_CREATED;

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Get image info failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastLSU::getLSUInfo(sts_lsu_info_v11_t* lsu_info)
{
    if (!lsu_info) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    // Fill in LSU info structure
    lsu_info->version = 11;
    strncpy(lsu_info->lsu_server, m_server->getServerName().c_str(), 
            sizeof(lsu_info->lsu_server) - 1);
    lsu_info->lsu_server[sizeof(lsu_info->lsu_server) - 1] = '\0';
    
    // Fill in LSU definition
    strncpy(lsu_info->lsu_def.sld_name.sln_name, m_name.c_str(),
            sizeof(lsu_info->lsu_def.sld_name.sln_name) - 1);
    lsu_info->lsu_def.sld_name.sln_name[sizeof(lsu_info->lsu_def.sld_name.sln_name) - 1] = '\0';
    
    lsu_info->lsu_def.sld_max_transfer = m_max_transfer_size;
    lsu_info->lsu_def.sld_block_size = m_block_size;
    lsu_info->lsu_def.sld_flags = m_lsu_flags;
    
    // Capacity information
    lsu_info->lsu_capacity = m_total_capacity;
    lsu_info->lsu_capacity_phys = m_total_capacity;
    lsu_info->lsu_used = m_used_capacity;
    lsu_info->lsu_used_phys = m_used_capacity;
    lsu_info->lsu_images = m_image_count;
    
    return STS_SUCCESS;
}

int VastLSU::getLSUInfo(sts_lsu_info_v9_t* lsu_info)
{
    if (!lsu_info) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    // Fill in basic LSU info for v9
    strncpy(lsu_info->lsu_server, m_server->getServerName().c_str(), 
            sizeof(lsu_info->lsu_server) - 1);
    lsu_info->lsu_server[sizeof(lsu_info->lsu_server) - 1] = '\0';
    
    lsu_info->lsu_capacity = m_total_capacity;
    lsu_info->lsu_capacity_phys = m_total_capacity;
    lsu_info->lsu_used = m_used_capacity;
    lsu_info->lsu_used_phys = m_used_capacity;
    lsu_info->lsu_images = m_image_count;
    
    return STS_SUCCESS;
}

int VastLSU::getConfig(char* buf, sts_uint32_t buflen, sts_uint32_t* maxlen)
{
    if (!buf || !maxlen) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    std::string config = "{\n";
    config += "  \"name\": \"" + m_name + "\",\n";
    config += "  \"bucket\": \"" + m_bucket_name + "\",\n";
    config += "  \"path\": \"" + m_path + "\",\n";
    config += "  \"status\": \"" + m_status + "\"\n";
    config += "}";
    
    *maxlen = static_cast<sts_uint32_t>(config.length());
    
    if (buflen < *maxlen + 1) {
        return STS_ERR_BUFFER_TOO_SMALL;
    }
    
    strncpy(buf, config.c_str(), buflen - 1);
    buf[buflen - 1] = '\0';
    
    return STS_SUCCESS;
}

int VastLSU::setConfig(const char* buf)
{
    if (!buf) {
        return STS_ERR_INVALID_PARAMETER;
    }

    try {
        // Parse JSON configuration (simplified parsing)
        std::string config_str(buf);

        // Log the configuration request
        // LSU configuration should be done through VMS, not the plugin
        std::cout << "VastLSU: Configuration update requested: " << config_str << std::endl;

        // Most LSU configuration should be done through VMS, not the plugin
        return STS_ERR_NOT_SUPPORTED;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Config parsing failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

VastS3Client* VastLSU::getS3Client()
{
    if (m_server) {
        return m_server->getS3Client();
    }
    return nullptr;
}

void VastLSU::setError(int error_code, const std::string& error_msg)
{
    m_last_error = error_code;
    m_last_error_msg = error_msg;
    std::cerr << "VastLSU Error [" << error_code << "]: " << error_msg << std::endl;
}

void VastLSU::clearError()
{
    m_last_error = STS_SUCCESS;
    m_last_error_msg.clear();
}

std::string VastLSU::generateImageKey(const sts_image_def_v10_t* image_def)
{
    if (!image_def) {
        return "";
    }
    
    return std::string(image_def->img_basename) + "_" + std::string(image_def->img_date);
}

int VastLSU::refreshCapacityInfo()
{
    try {
        // Get S3 client to calculate bucket usage
        VastS3Client* s3_client = getS3Client();
        if (!s3_client) {
            return STS_SUCCESS; // Can't refresh without S3 client
        }

        // List objects in bucket to calculate total size
        std::vector<VastS3ObjectInfo> objects;
        int result = s3_client->listObjects(m_bucket_name, std::string(""), objects);
        if (result == 0) {
            uint64_t total_size = 0;
            for (const auto& obj : objects) {
                total_size += obj.size;
            }

            // Update capacity information
            // In a real implementation, we would also get quota information from VMS
            std::cout << "VastLSU: Bucket " << m_bucket_name << " contains "
                      << objects.size() << " objects, total size: " << total_size << " bytes" << std::endl;
        }

        return STS_SUCCESS;
    }
    catch (const std::exception& e) {
        setError(STS_ERR_INTERNAL, std::string("Refresh capacity failed: ") + e.what());
        return STS_ERR_INTERNAL;
    }
}

int VastLSU::validateImageDefinition(const sts_image_def_v10_t* image_def)
{
    if (!image_def) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    if (strlen(image_def->img_basename) == 0) {
        return STS_ERR_INVALID_PARAMETER;
    }
    
    return STS_SUCCESS;
}
