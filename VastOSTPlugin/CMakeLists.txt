cmake_minimum_required(VERSION 3.10)
project(VastOSTPlugin VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Set build type if not specified
if(NOT CMAKE_BUILD_TYPE)
    set(CMAKE_BUILD_TYPE Release)
endif()

# Compiler flags
set(CMAKE_CXX_FLAGS_DEBUG "-g -O0 -Wall -Wextra")
set(CMAKE_CXX_FLAGS_RELEASE "-O3 -DNDEBUG")

# OST SDK paths
set(OST_SDK_ROOT "${CMAKE_CURRENT_SOURCE_DIR}/../OST-SDK-11.1.1")
set(OST_SDK_INCLUDE_DIR "${OST_SDK_ROOT}/src/include")
set(OST_SDK_LIB_DIR "${OST_SDK_ROOT}/src/lib")

# Platform detection (following OST SDK pattern)
if(CMAKE_SYSTEM_NAME STREQUAL "Linux")
    if(CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64")
        set(OST_PLATFORM "linuxR_x64")
    elseif(CMAKE_SYSTEM_PROCESSOR STREQUAL "aarch64")
        set(OST_PLATFORM "linuxR_arm64")
    endif()
elseif(CMAKE_SYSTEM_NAME STREQUAL "Darwin")
    if(CMAKE_SYSTEM_PROCESSOR STREQUAL "arm64")
        set(OST_PLATFORM "macosR_arm64")
    elseif(CMAKE_SYSTEM_PROCESSOR STREQUAL "x86_64")
        set(OST_PLATFORM "macosR_x64")
    endif()
elseif(CMAKE_SYSTEM_NAME STREQUAL "Windows")
    if(CMAKE_SYSTEM_PROCESSOR STREQUAL "AMD64")
        set(OST_PLATFORM "winR_x64")
    endif()
endif()

# Default platform based on system
if(NOT OST_PLATFORM)
    if(APPLE)
        # OST SDK doesn't have macOS platform, use Linux x64 as fallback
        set(OST_PLATFORM "linuxR_x64")
        message(STATUS "Using linuxR_x64 platform headers for macOS (OST SDK limitation)")
    else()
        set(OST_PLATFORM "linuxR_x64")   # Default for other systems
    endif()
endif()

set(OST_PLATFORM_INCLUDE_DIR "${OST_SDK_INCLUDE_DIR}/platforms/${OST_PLATFORM}")

# Check if OST SDK exists
if(NOT EXISTS "${OST_SDK_INCLUDE_DIR}/stspi.h")
    message(FATAL_ERROR "OST SDK not found at ${OST_SDK_ROOT}. Please ensure the SDK is extracted.")
endif()

if(NOT EXISTS "${OST_PLATFORM_INCLUDE_DIR}")
    message(WARNING "Platform-specific headers not found at ${OST_PLATFORM_INCLUDE_DIR}")
    # For macOS, use our custom compatibility headers
    if(APPLE)
        set(OST_PLATFORM_INCLUDE_DIR "${CMAKE_CURRENT_SOURCE_DIR}")
        message(STATUS "Using custom macOS compatibility headers")
        # Add preprocessor definitions to use our custom headers
        add_compile_definitions(USE_MACOS_COMPAT_HEADERS=1)
        add_compile_definitions(STS_EXTERNAL_VENDOR=1)
    endif()
endif()

# Include directories
include_directories(
    ${OST_SDK_INCLUDE_DIR}
    ${OST_PLATFORM_INCLUDE_DIR}
    ${CMAKE_CURRENT_SOURCE_DIR}
)

# Find required libraries
find_package(PkgConfig REQUIRED)

# Find curl for REST API
find_package(CURL REQUIRED)

# Find OpenSSL for S3 operations
find_package(OpenSSL REQUIRED)

# Find JSON library (we'll use jsoncpp)
find_package(PkgConfig QUIET)
if(PkgConfig_FOUND)
    pkg_check_modules(JSONCPP jsoncpp)
endif()

if(NOT JSONCPP_FOUND)
    message(STATUS "jsoncpp not found via pkg-config, trying find_package")
    find_package(jsoncpp QUIET)
    if(jsoncpp_FOUND)
        set(JSONCPP_FOUND TRUE)
        set(JSONCPP_LIBRARIES jsoncpp_lib)
    endif()
endif()

if(NOT JSONCPP_FOUND)
    message(STATUS "jsoncpp not found, will fetch from source")
    include(FetchContent)
    FetchContent_Declare(
        jsoncpp
        GIT_REPOSITORY https://github.com/open-source-parsers/jsoncpp.git
        GIT_TAG 1.9.5
    )
    FetchContent_MakeAvailable(jsoncpp)
    set(JSONCPP_LIBRARIES jsoncpp_lib)
    set(JSONCPP_FOUND TRUE)
endif()

# Find AWS SDK for S3 operations
find_package(AWSSDK QUIET COMPONENTS s3 core)
if(NOT AWSSDK_FOUND)
    message(STATUS "AWS SDK not found, will use minimal S3 implementation")
    # You can add a fallback implementation or require manual installation
endif()

# Source files
set(VAST_PLUGIN_SOURCES
    vastplugin.cpp
    VastStorageServer.cpp
    VastRestClient.cpp
    VastS3Client.cpp
    VastLSU.cpp
    VastImage.cpp
)

# Header files
set(VAST_PLUGIN_HEADERS
    vastplugin.h
    VastStorageServer.h
    VastRestClient.h
    VastS3Client.h
    VastLSU.h
    VastImage.h
)

# Create shared library (OST plugin)
add_library(vastost SHARED ${VAST_PLUGIN_SOURCES})

# Set library properties
set_target_properties(vastost PROPERTIES
    VERSION ${PROJECT_VERSION}
    SOVERSION 1
    CXX_VISIBILITY_PRESET hidden
    VISIBILITY_INLINES_HIDDEN ON
)

# Link libraries
target_link_libraries(vastost
    CURL::libcurl
    OpenSSL::SSL
    OpenSSL::Crypto
)

# Link jsoncpp
if(JSONCPP_FOUND)
    if(JSONCPP_LIBRARIES)
        target_link_libraries(vastost ${JSONCPP_LIBRARIES})
    endif()
    if(JSONCPP_INCLUDE_DIRS)
        target_include_directories(vastost PRIVATE ${JSONCPP_INCLUDE_DIRS})
    endif()
endif()

# Link AWS SDK if available
if(AWSSDK_FOUND)
    target_link_libraries(vastost ${AWSSDK_LINK_LIBRARIES})
    target_compile_definitions(vastost PRIVATE HAVE_AWS_SDK)
endif()

# Platform-specific settings
if(WIN32)
    target_compile_definitions(vastost PRIVATE _WIN32_WINNT=0x0601)
    target_link_libraries(vastost ws2_32 wininet)
elseif(APPLE)
    target_link_libraries(vastost "-framework CoreFoundation" "-framework Security")
elseif(UNIX)
    target_link_libraries(vastost pthread dl)
endif()

# Install targets
install(TARGETS vastost
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${VAST_PLUGIN_HEADERS}
    DESTINATION include/vastost
)

# Install configuration file
install(FILES vast_config.conf
    DESTINATION etc/vastost
)

# Create package config
include(CMakePackageConfigHelpers)
write_basic_package_version_file(
    "${CMAKE_CURRENT_BINARY_DIR}/VastOSTPluginConfigVersion.cmake"
    VERSION ${PROJECT_VERSION}
    COMPATIBILITY AnyNewerVersion
)

# Export targets
export(TARGETS vastost
    FILE "${CMAKE_CURRENT_BINARY_DIR}/VastOSTPluginTargets.cmake"
)

# Testing (optional)
option(BUILD_TESTS "Build test programs" OFF)
if(BUILD_TESTS)
    enable_testing()
    add_subdirectory(tests)
endif()

# Documentation (optional)
option(BUILD_DOCS "Build documentation" OFF)
if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        configure_file(${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in
                      ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile @ONLY)
        add_custom_target(doc
            ${DOXYGEN_EXECUTABLE} ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "Generating API documentation with Doxygen" VERBATIM
        )
    endif()
endif()

# Print configuration summary
message(STATUS "")
message(STATUS "Vast Data OST Plugin Configuration Summary:")
message(STATUS "  Version: ${PROJECT_VERSION}")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  OST SDK path: ${OST_SDK_ROOT}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "  Platform: ${OST_PLATFORM}")
message(STATUS "  CURL found: ${CURL_FOUND}")
message(STATUS "  jsoncpp found: ${JSONCPP_FOUND}")
message(STATUS "  AWS SDK found: ${AWSSDK_FOUND}")
message(STATUS "")