/*
 * macOS compatibility header for OST SDK types
 * This replaces the Linux platform types for macOS builds
 */

#ifndef _MACOS_STSTYPES_H_
#define _MACOS_STSTYPES_H_

#include "macos_stsplat.h"
#include <stdint.h>
#include <sys/types.h>

// Basic integer types (already defined in stsplat.h, but ensure consistency)
#ifndef __u8
typedef uint8_t  __u8;
typedef uint16_t __u16;
typedef uint32_t __u32;
typedef uint64_t __u64;

typedef int8_t   __s8;
typedef int16_t  __s16;
typedef int32_t  __s32;
typedef int64_t  __s64;
#endif

// OST-specific types
typedef __u32 STS_UINT32;
typedef __u64 STS_UINT64;
typedef __s32 STS_INT32;
typedef __s64 STS_INT64;

// Additional OST SDK required types - only define if not already defined
#ifndef sts_uint8_t
typedef uint8_t   sts_uint8_t;
#endif
#ifndef sts_uint16_t
typedef uint16_t  sts_uint16_t;
#endif
#ifndef sts_uint32_t
typedef uint32_t  sts_uint32_t;
#endif
#ifndef sts_uint64_t
typedef uint64_t  sts_uint64_t;
#endif

#ifndef sts_int8_t
typedef int8_t    sts_int8_t;
#endif
#ifndef sts_int16_t
typedef int16_t   sts_int16_t;
#endif
#ifndef sts_int32_t
typedef int32_t   sts_int32_t;
#endif
#ifndef sts_int64_t
typedef int64_t   sts_int64_t;
#endif

typedef __u8  STS_UCHAR;
typedef __u16 STS_USHORT;
typedef __u32 STS_ULONG;

typedef char  STS_CHAR;
typedef short STS_SHORT;
typedef int   STS_INT;
typedef long  STS_LONG;

// Boolean type
typedef int STS_BOOL;
#define STS_TRUE  1
#define STS_FALSE 0

// Handle types
typedef void* STS_HANDLE;
typedef STS_HANDLE STS_FILE_HANDLE;
typedef STS_HANDLE STS_THREAD_HANDLE;
typedef STS_HANDLE STS_MUTEX_HANDLE;
typedef STS_HANDLE STS_EVENT_HANDLE;

// Time types
typedef time_t STS_TIME;
typedef struct timespec STS_TIMESPEC;

// Network address types
struct STS_SOCKADDR {
    __u16 family;
    char  data[14];
};

typedef struct STS_SOCKADDR STS_SOCKADDR;

// File system types
typedef struct stat STS_STAT;
typedef mode_t STS_MODE;
typedef uid_t  STS_UID;
typedef gid_t  STS_GID;

// Memory types
typedef void* STS_PVOID;
typedef const void* STS_PCVOID;

// String types
typedef char* STS_PSTR;
typedef const char* STS_PCSTR;
typedef wchar_t* STS_PWSTR;
typedef const wchar_t* STS_PCWSTR;

// Size and offset types
typedef size_t STS_SIZE_T;
typedef ssize_t STS_SSIZE_T;
typedef off_t STS_OFF_T;

// Process and thread IDs
typedef pid_t STS_PID;
typedef pthread_t STS_TID;

// Return codes
typedef int STS_RESULT;
#define STS_SUCCESS 0
#define STS_FAILURE (-1)

// Null values
#define STS_NULL ((void*)0)
#define STS_INVALID_HANDLE ((STS_HANDLE)(-1))

// Note: OST SDK v11 struct types are defined in macos_ost_v11_extensions.h
// which must be included after the main OST SDK headers

#endif /* _MACOS_STSTYPES_H_ */
