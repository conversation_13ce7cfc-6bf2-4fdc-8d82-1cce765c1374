#
# Vast Data OpenStorage Plugin Configuration
# This file configures the connection to Vast Data cluster
#

[VastData]
# VMS (Vast Management Service) endpoint
vms_endpoint = https://vast-cluster.example.com

# S3 endpoint for data operations
s3_endpoint = https://vast-cluster.example.com

# Default region for S3 operations
s3_region = us-east-1

# SSL/TLS settings
verify_ssl = true
ssl_cert_path = /etc/ssl/certs/vast-cluster.pem

# Connection timeouts (in seconds)
connection_timeout = 30
read_timeout = 300
write_timeout = 300

# Authentication settings
auth_method = token  # Options: token, basic
token_refresh_interval = 3600  # seconds

# S3 settings
multipart_threshold = 64MB
multipart_chunk_size = 16MB
max_concurrent_uploads = 4

# Retry settings
max_retries = 3
retry_delay = 1  # seconds

# Logging
log_level = INFO  # Options: DEBUG, INFO, WARN, ERROR
log_file = /var/log/netbackup/vast_plugin.log

# Performance tuning
buffer_size = 1MB
prefetch_enabled = true
prefetch_size = 8MB

# LSU (Logical Storage Unit) settings
default_bucket_prefix = netbackup-
bucket_lifecycle_days = 365
enable_versioning = true

# Backup image settings
image_compression = false
image_encryption = false
metadata_storage = s3_tags  # Options: s3_tags, separate_object

[Advanced]
# Advanced configuration options
enable_deduplication = false
enable_compression = false
parallel_streams = 4
chunk_verification = true

# Event handling
enable_events = true
event_poll_interval = 5  # seconds

# Debug options (for development)
debug_mode = false
trace_api_calls = false
dump_http_headers = false