# This is the CMakeCache file.
# For build in directory: /Users/<USER>/Documents/projects/Vast/OneDrive_1_5-20-2025/VastOSTPlugin/build
# It was generated by CMake: /opt/homebrew/bin/cmake
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//The directory containing a CMake configuration file for AWSSDK.
AWSSDK_DIR:PATH=AWSSDK_DIR-NOTFOUND

//Build documentation
BUILD_DOCS:BOOL=OFF

//Build test programs
BUILD_TESTS:BOOL=OFF

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=/usr/bin/ar

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//Enable/Disable color output during build.
CMAKE_COLOR_MAKEFILE:BOOL=ON

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=/usr/bin/c++

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=-L/opt/homebrew/opt/curl/lib -L/opt/homebrew/opt/openssl/lib

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=/Users/<USER>/Documents/projects/Vast/OneDrive_1_5-20-2025/VastOSTPlugin/build/CMakeFiles/pkgRedirects

//Path to a program.
CMAKE_INSTALL_NAME_TOOL:FILEPATH=/usr/bin/install_name_tool

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=/usr/local

//Path to a program.
CMAKE_LINKER:FILEPATH=/usr/bin/ld

//Path to a program.
CMAKE_MAKE_PROGRAM:FILEPATH=/usr/bin/make

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=-L/opt/homebrew/opt/curl/lib -L/opt/homebrew/opt/openssl/lib

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=/usr/bin/nm

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=CMAKE_OBJCOPY-NOTFOUND

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=/usr/bin/objdump

//Build architectures for OSX
CMAKE_OSX_ARCHITECTURES:STRING=

//Minimum OS X version to target for deployment (at runtime); newer
// APIs weak linked. Set to empty string for default value.
CMAKE_OSX_DEPLOYMENT_TARGET:STRING=

//The product will be built against the headers and libraries located
// inside the indicated SDK.
CMAKE_OSX_SYSROOT:STRING=

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=VastOSTPlugin

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=1.0.0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=1

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=/usr/bin/ranlib

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=-L/opt/homebrew/opt/curl/lib -L/opt/homebrew/opt/openssl/lib

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=/usr/bin/strip

//Path to a program.
CMAKE_TAPI:FILEPATH=/Library/Developer/CommandLineTools/usr/bin/tapi

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//The directory containing a CMake configuration file for CURL.
CURL_DIR:PATH=CURL_DIR-NOTFOUND

//Path to a file.
CURL_INCLUDE_DIR:PATH=/opt/homebrew/Cellar/curl/8.14.0/include

//Path to a library.
CURL_LIBRARY_DEBUG:FILEPATH=CURL_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
CURL_LIBRARY_RELEASE:FILEPATH=/opt/homebrew/Cellar/curl/8.14.0/lib/libcurl.dylib

//Path to a library.
OPENSSL_CRYPTO_LIBRARY:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.5.0/lib/libcrypto.dylib

//Path to a file.
OPENSSL_INCLUDE_DIR:PATH=/opt/homebrew/Cellar/openssl@3/3.5.0/include

//Path to a library.
OPENSSL_SSL_LIBRARY:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.5.0/lib/libssl.dylib

//No help, variable specified on the command line.
OST_SDK_ROOT:UNINITIALIZED=../OST-SDK-11.1.1

//Arguments to supply to pkg-config
PKG_CONFIG_ARGN:STRING=

//pkg-config executable
PKG_CONFIG_EXECUTABLE:FILEPATH=/opt/homebrew/bin/pkg-config

//Value Computed by CMake
VastOSTPlugin_BINARY_DIR:STATIC=/Users/<USER>/Documents/projects/Vast/OneDrive_1_5-20-2025/VastOSTPlugin/build

//Value Computed by CMake
VastOSTPlugin_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
VastOSTPlugin_SOURCE_DIR:STATIC=/Users/<USER>/Documents/projects/Vast/OneDrive_1_5-20-2025/VastOSTPlugin

//Path to a library.
pkgcfg_lib_JSONCPP_jsoncpp:FILEPATH=/opt/homebrew/Cellar/jsoncpp/1.9.6/lib/libjsoncpp.dylib

//Path to a library.
pkgcfg_lib_PC_CURL_curl:FILEPATH=/opt/homebrew/Cellar/curl/8.14.0/lib/libcurl.dylib

//Path to a library.
pkgcfg_lib__OPENSSL_crypto:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.5.0/lib/libcrypto.dylib

//Path to a library.
pkgcfg_lib__OPENSSL_ssl:FILEPATH=/opt/homebrew/Cellar/openssl@3/3.5.0/lib/libssl.dylib

//Dependencies for the target
vastost_LIB_DEPENDS:STATIC=general;CURL::libcurl;general;OpenSSL::SSL;general;OpenSSL::Crypto;general;jsoncpp;general;-framework CoreFoundation;general;-framework Security;


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=/Users/<USER>/Documents/projects/Vast/OneDrive_1_5-20-2025/VastOSTPlugin/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=4
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=0
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=2
//ADVANCED property for variable: CMAKE_COLOR_MAKEFILE
CMAKE_COLOR_MAKEFILE-ADVANCED:INTERNAL=1
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=/opt/homebrew/bin/cmake
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=/opt/homebrew/bin/cpack
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=/opt/homebrew/bin/ctest
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=/opt/homebrew/bin/ccmake
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=MACHO
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Unix Makefiles
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=/Users/<USER>/Documents/projects/Vast/OneDrive_1_5-20-2025/VastOSTPlugin
//ADVANCED property for variable: CMAKE_INSTALL_NAME_TOOL
CMAKE_INSTALL_NAME_TOOL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//Name of CMakeLists files to read
CMAKE_LIST_FILE_NAME:INTERNAL=CMakeLists.txt
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=/opt/homebrew/share/cmake
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_TAPI
CMAKE_TAPI-ADVANCED:INTERNAL=1
//uname command
CMAKE_UNAME:INTERNAL=/usr/bin/uname
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_DIR
CURL_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_INCLUDE_DIR
CURL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_LIBRARY_DEBUG
CURL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CURL_LIBRARY_RELEASE
CURL_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//Details about finding CURL
FIND_PACKAGE_MESSAGE_DETAILS_CURL:INTERNAL=[/opt/homebrew/Cellar/curl/8.14.0/lib/libcurl.dylib][/opt/homebrew/Cellar/curl/8.14.0/include][ ][v8.14.0()]
//Details about finding OpenSSL
FIND_PACKAGE_MESSAGE_DETAILS_OpenSSL:INTERNAL=[/opt/homebrew/Cellar/openssl@3/3.5.0/lib/libcrypto.dylib][/opt/homebrew/Cellar/openssl@3/3.5.0/include][ ][v3.5.0()]
//Details about finding PkgConfig
FIND_PACKAGE_MESSAGE_DETAILS_PkgConfig:INTERNAL=[/opt/homebrew/bin/pkg-config][v2.4.3()]
JSONCPP_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/jsoncpp/1.9.6/include
JSONCPP_CFLAGS_I:INTERNAL=
JSONCPP_CFLAGS_OTHER:INTERNAL=
JSONCPP_FOUND:INTERNAL=1
JSONCPP_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/jsoncpp/1.9.6/include
JSONCPP_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/jsoncpp/1.9.6/include
JSONCPP_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/jsoncpp/1.9.6/lib;-ljsoncpp
JSONCPP_LDFLAGS_OTHER:INTERNAL=
JSONCPP_LIBDIR:INTERNAL=/opt/homebrew/Cellar/jsoncpp/1.9.6/lib
JSONCPP_LIBRARIES:INTERNAL=jsoncpp
JSONCPP_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/jsoncpp/1.9.6/lib
JSONCPP_LIBS:INTERNAL=
JSONCPP_LIBS_L:INTERNAL=
JSONCPP_LIBS_OTHER:INTERNAL=
JSONCPP_LIBS_PATHS:INTERNAL=
JSONCPP_MODULE_NAME:INTERNAL=jsoncpp
JSONCPP_PREFIX:INTERNAL=/opt/homebrew/Cellar/jsoncpp/1.9.6
JSONCPP_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/jsoncpp/1.9.6/include
JSONCPP_STATIC_CFLAGS_I:INTERNAL=
JSONCPP_STATIC_CFLAGS_OTHER:INTERNAL=
JSONCPP_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/jsoncpp/1.9.6/include
JSONCPP_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/jsoncpp/1.9.6/lib;-ljsoncpp
JSONCPP_STATIC_LDFLAGS_OTHER:INTERNAL=
JSONCPP_STATIC_LIBDIR:INTERNAL=
JSONCPP_STATIC_LIBRARIES:INTERNAL=jsoncpp
JSONCPP_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/jsoncpp/1.9.6/lib
JSONCPP_STATIC_LIBS:INTERNAL=
JSONCPP_STATIC_LIBS_L:INTERNAL=
JSONCPP_STATIC_LIBS_OTHER:INTERNAL=
JSONCPP_STATIC_LIBS_PATHS:INTERNAL=
JSONCPP_VERSION:INTERNAL=1.9.6
JSONCPP_jsoncpp_INCLUDEDIR:INTERNAL=
JSONCPP_jsoncpp_LIBDIR:INTERNAL=
JSONCPP_jsoncpp_PREFIX:INTERNAL=
JSONCPP_jsoncpp_VERSION:INTERNAL=
//ADVANCED property for variable: OPENSSL_CRYPTO_LIBRARY
OPENSSL_CRYPTO_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_INCLUDE_DIR
OPENSSL_INCLUDE_DIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENSSL_SSL_LIBRARY
OPENSSL_SSL_LIBRARY-ADVANCED:INTERNAL=1
PC_CURL_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/curl/8.14.0/include;-I/opt/homebrew/Cellar/brotli/1.1.0/include;-I/opt/homebrew/opt/zstd/include;-I/opt/homebrew/Cellar/libssh2/1.11.1/include;-I/opt/homebrew/Cellar/rtmpdump/2.4-20151223_3/include;-I/opt/homebrew/Cellar/openssl@3/3.5.0/include;-I/opt/homebrew/Cellar/libnghttp2/1.65.0/include
PC_CURL_CFLAGS_I:INTERNAL=
PC_CURL_CFLAGS_OTHER:INTERNAL=
PC_CURL_FOUND:INTERNAL=1
PC_CURL_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/curl/8.14.0/include
PC_CURL_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/curl/8.14.0/include;/opt/homebrew/Cellar/brotli/1.1.0/include;/opt/homebrew/opt/zstd/include;/opt/homebrew/Cellar/libssh2/1.11.1/include;/opt/homebrew/Cellar/rtmpdump/2.4-20151223_3/include;/opt/homebrew/Cellar/openssl@3/3.5.0/include;/opt/homebrew/Cellar/libnghttp2/1.65.0/include
PC_CURL_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/curl/8.14.0/lib;-lcurl
PC_CURL_LDFLAGS_OTHER:INTERNAL=
PC_CURL_LIBDIR:INTERNAL=/opt/homebrew/Cellar/curl/8.14.0/lib
PC_CURL_LIBRARIES:INTERNAL=curl
PC_CURL_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/curl/8.14.0/lib
PC_CURL_LIBS:INTERNAL=
PC_CURL_LIBS_L:INTERNAL=
PC_CURL_LIBS_OTHER:INTERNAL=
PC_CURL_LIBS_PATHS:INTERNAL=
PC_CURL_MODULE_NAME:INTERNAL=libcurl
PC_CURL_PREFIX:INTERNAL=/opt/homebrew/Cellar/curl/8.14.0
PC_CURL_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/curl/8.14.0/include;-I/opt/homebrew/Cellar/brotli/1.1.0/include;-I/opt/homebrew/opt/zstd/include;-I/opt/homebrew/Cellar/libssh2/1.11.1/include;-I/opt/homebrew/Cellar/rtmpdump/2.4-20151223_3/include;-I/opt/homebrew/Cellar/openssl@3/3.5.0/include;-I/opt/homebrew/Cellar/libnghttp2/1.65.0/include;-DCURL_STATICLIB
PC_CURL_STATIC_CFLAGS_I:INTERNAL=
PC_CURL_STATIC_CFLAGS_OTHER:INTERNAL=-DCURL_STATICLIB
PC_CURL_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/curl/8.14.0/include;/opt/homebrew/Cellar/brotli/1.1.0/include;/opt/homebrew/opt/zstd/include;/opt/homebrew/Cellar/libssh2/1.11.1/include;/opt/homebrew/Cellar/rtmpdump/2.4-20151223_3/include;/opt/homebrew/Cellar/openssl@3/3.5.0/include;/opt/homebrew/Cellar/libnghttp2/1.65.0/include
PC_CURL_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/curl/8.14.0/lib;-lcurl;-framework;CoreFoundation;-framework;CoreServices;-framework;Security;-L/opt/homebrew/Cellar/openssl@3/3.5.0/lib;-L/opt/homebrew/Cellar/libssh2/1.11.1/lib;-L/opt/homebrew/Cellar/rtmpdump/2.4-20151223_3/lib;-L/opt/homebrew/Cellar/openssl@3/3.5.0/lib;-L/opt/homebrew/Cellar/libnghttp2/1.65.0/lib;-framework;CoreFoundation;-framework;CoreServices;-framework;SystemConfiguration;-lnghttp2;-licucore;-liconv;-lrtmp;-lz;-lssl;-lcrypto;-lssh2;-lssh2;-lssl;-lcrypto;-lssl;-lcrypto;-lgssapi_krb5;-lresolv;-lldap;-lzstd;-lbrotlidec;-lz;-L/opt/homebrew/Cellar/brotli/1.1.0/lib;-lbrotlidec;-lbrotlicommon;-L/opt/homebrew/opt/zstd/lib;-lzstd;-L/opt/homebrew/Cellar/libssh2/1.11.1/lib;-lssh2;-L/opt/homebrew/opt/openssl@3/lib;-L/opt/homebrew/opt/openssl@3/lib;-R/opt/homebrew/opt/openssl@3/lib;-lz;-L/usr/lib;-L/opt/homebrew/Cellar/rtmpdump/2.4-20151223_3/lib;-lrtmp;-lz;-L/opt/homebrew/Cellar/openssl@3/3.5.0/lib;-lssl;-lcrypto;-L/opt/homebrew/Cellar/libnghttp2/1.65.0/lib;-lnghttp2
PC_CURL_STATIC_LDFLAGS_OTHER:INTERNAL=-framework;CoreFoundation;-framework;CoreServices;-framework;Security;-L/opt/homebrew/Cellar/openssl@3/3.5.0/lib;-L/opt/homebrew/Cellar/libssh2/1.11.1/lib;-L/opt/homebrew/Cellar/rtmpdump/2.4-20151223_3/lib;-L/opt/homebrew/Cellar/openssl@3/3.5.0/lib;-L/opt/homebrew/Cellar/libnghttp2/1.65.0/lib;-framework;CoreFoundation;-framework;CoreServices;-framework;SystemConfiguration;-lnghttp2;-licucore;-liconv;-lrtmp;-lz;-lssl;-lcrypto;-lssh2;-lssh2;-lssl;-lcrypto;-lssl;-lcrypto;-lgssapi_krb5;-lresolv;-lldap;-lzstd;-lbrotlidec;-lz;-R/opt/homebrew/opt/openssl@3/lib
PC_CURL_STATIC_LIBDIR:INTERNAL=
PC_CURL_STATIC_LIBRARIES:INTERNAL=curl;brotlidec;brotlicommon;zstd;ssh2;z;rtmp;z;ssl;crypto;nghttp2
PC_CURL_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/curl/8.14.0/lib;/opt/homebrew/Cellar/brotli/1.1.0/lib;/opt/homebrew/opt/zstd/lib;/opt/homebrew/Cellar/libssh2/1.11.1/lib;/opt/homebrew/opt/openssl@3/lib;/opt/homebrew/opt/openssl@3/lib;/usr/lib;/opt/homebrew/Cellar/rtmpdump/2.4-20151223_3/lib;/opt/homebrew/Cellar/openssl@3/3.5.0/lib;/opt/homebrew/Cellar/libnghttp2/1.65.0/lib
PC_CURL_STATIC_LIBS:INTERNAL=
PC_CURL_STATIC_LIBS_L:INTERNAL=
PC_CURL_STATIC_LIBS_OTHER:INTERNAL=
PC_CURL_STATIC_LIBS_PATHS:INTERNAL=
PC_CURL_VERSION:INTERNAL=8.14.0
PC_CURL_libcurl_INCLUDEDIR:INTERNAL=
PC_CURL_libcurl_LIBDIR:INTERNAL=
PC_CURL_libcurl_PREFIX:INTERNAL=
PC_CURL_libcurl_VERSION:INTERNAL=
//ADVANCED property for variable: PKG_CONFIG_ARGN
PKG_CONFIG_ARGN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: PKG_CONFIG_EXECUTABLE
PKG_CONFIG_EXECUTABLE-ADVANCED:INTERNAL=1
_OPENSSL_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/openssl@3/3.5.0/include
_OPENSSL_CFLAGS_I:INTERNAL=
_OPENSSL_CFLAGS_OTHER:INTERNAL=
_OPENSSL_FOUND:INTERNAL=1
_OPENSSL_INCLUDEDIR:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/include
_OPENSSL_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/include
_OPENSSL_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/openssl@3/3.5.0/lib;-lssl;-lcrypto
_OPENSSL_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_LIBDIR:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/lib
_OPENSSL_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/lib
_OPENSSL_LIBS:INTERNAL=
_OPENSSL_LIBS_L:INTERNAL=
_OPENSSL_LIBS_OTHER:INTERNAL=
_OPENSSL_LIBS_PATHS:INTERNAL=
_OPENSSL_MODULE_NAME:INTERNAL=openssl
_OPENSSL_PREFIX:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0
_OPENSSL_STATIC_CFLAGS:INTERNAL=-I/opt/homebrew/Cellar/openssl@3/3.5.0/include
_OPENSSL_STATIC_CFLAGS_I:INTERNAL=
_OPENSSL_STATIC_CFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_INCLUDE_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/include
_OPENSSL_STATIC_LDFLAGS:INTERNAL=-L/opt/homebrew/Cellar/openssl@3/3.5.0/lib;-lssl;-lcrypto
_OPENSSL_STATIC_LDFLAGS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBDIR:INTERNAL=
_OPENSSL_STATIC_LIBRARIES:INTERNAL=ssl;crypto
_OPENSSL_STATIC_LIBRARY_DIRS:INTERNAL=/opt/homebrew/Cellar/openssl@3/3.5.0/lib
_OPENSSL_STATIC_LIBS:INTERNAL=
_OPENSSL_STATIC_LIBS_L:INTERNAL=
_OPENSSL_STATIC_LIBS_OTHER:INTERNAL=
_OPENSSL_STATIC_LIBS_PATHS:INTERNAL=
_OPENSSL_VERSION:INTERNAL=3.5.0
_OPENSSL_openssl_INCLUDEDIR:INTERNAL=
_OPENSSL_openssl_LIBDIR:INTERNAL=
_OPENSSL_openssl_PREFIX:INTERNAL=
_OPENSSL_openssl_VERSION:INTERNAL=
__pkg_config_arguments_JSONCPP:INTERNAL=jsoncpp
__pkg_config_arguments_PC_CURL:INTERNAL=QUIET;libcurl
__pkg_config_arguments__OPENSSL:INTERNAL=QUIET;openssl
__pkg_config_checked_JSONCPP:INTERNAL=1
__pkg_config_checked_PC_CURL:INTERNAL=1
__pkg_config_checked__OPENSSL:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_JSONCPP_jsoncpp
pkgcfg_lib_JSONCPP_jsoncpp-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib_PC_CURL_curl
pkgcfg_lib_PC_CURL_curl-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_crypto
pkgcfg_lib__OPENSSL_crypto-ADVANCED:INTERNAL=1
//ADVANCED property for variable: pkgcfg_lib__OPENSSL_ssl
pkgcfg_lib__OPENSSL_ssl-ADVANCED:INTERNAL=1
prefix_result:INTERNAL=alt-svc;AsynchDNS;brotli;GSS-API;HSTS;HTTP2;HTTPS-proxy;IDN;IPv6;Kerberos;Largefile;libz;MultiSSL;NTLM;SPNEGO;SSL;threadsafe;TLS-SRP;UnixSockets;zstd

