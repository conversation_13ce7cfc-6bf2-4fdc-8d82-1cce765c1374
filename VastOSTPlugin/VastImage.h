/*
 *************************************************************************
 * Vast Data Image Class
 * Represents a NetBackup backup image stored in Vast Data S3
 *************************************************************************
 */

#ifndef _VAST_IMAGE_H_
#define _VAST_IMAGE_H_

#include "../OST-SDK-11.1.1/src/include/stsi.h"
#include "VastS3Client.h"  // For VastS3PartInfo
#include <string>
#include <memory>
#include <map>

// Forward declarations
class VastLSU;
class VastS3Client;

class VastImage {
public:
    VastImage(VastLSU* lsu, const sts_image_def_v10_t* image_def);
    ~VastImage();

    // Image properties
    std::string getImageName() const { return m_image_name; }
    std::string getS3Key() const { return m_s3_key; }
    std::string getBucketName() const;
    sts_uint64_t getSize() const { return m_size; }
    sts_uint64_t getBlockSize() const { return m_block_size; }
    
    // Image operations
    int create(int flags);
    int open(int mode);
    int close(int complete_flag, int force_flag);
    int remove();

    // I/O operations
    int read(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_read);
    int write(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_written);
    
    // Metadata operations
    int readMetadata(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_read);
    int writeMetadata(void* buf, sts_uint64_t length, sts_uint64_t offset, sts_uint64_t* bytes_written);
    
    // Image information
    int getImageInfo(sts_image_info_v10_t* image_info);
    int getImageInfo(sts_image_info_v7_t* image_info);
    
    // Copy operations
    int copyFrom(VastImage* source_image, const std::string& operation_name, int event_flag);
    int copyTo(VastImage* target_image, const std::string& operation_name, int event_flag);
    
    // Status and state
    bool isOpen() const { return m_is_open; }
    bool isPending() const { return m_is_pending; }
    bool isComplete() const { return m_is_complete; }
    int getMode() const { return m_mode; }
    
    // Error handling
    int getLastError() const { return m_last_error; }
    std::string getLastErrorMessage() const { return m_last_error_msg; }

    // Internal methods
    VastLSU* getLSU() { return m_lsu; }
    const sts_image_def_v10_t* getImageDefinition() const { return &m_image_def; }

private:
    // Image definition and properties
    sts_image_def_v10_t m_image_def;
    std::string m_image_name;
    std::string m_s3_key;
    std::string m_metadata_key;
    
    // Size and block information
    sts_uint64_t m_size;
    sts_uint64_t m_block_size;
    sts_uint64_t m_current_size;
    
    // State information
    bool m_is_open;
    bool m_is_pending;
    bool m_is_complete;
    int m_mode;  // STS_MODE_READ, STS_MODE_WRITE, etc.
    
    // LSU reference
    VastLSU* m_lsu;
    
    // Error tracking
    int m_last_error;
    std::string m_last_error_msg;
    
    // S3 multipart upload state
    struct MultipartUpload {
        std::string upload_id;
        std::vector<std::string> part_etags;
        std::vector<VastS3PartInfo> parts;  // Add parts vector for compatibility
        int next_part_number;
        bool active;

        MultipartUpload() : next_part_number(1), active(false) {}
    };
    MultipartUpload m_multipart_upload;
    
    // Metadata storage
    std::map<std::string, std::string> m_metadata;
    bool m_metadata_dirty;
    
    // Helper methods
    void setError(int error_code, const std::string& error_msg);
    void clearError();
    std::string generateS3Key();
    std::string generateMetadataKey();
    int initializeMultipartUpload();
    int completeMultipartUpload();
    int abortMultipartUpload();
    int uploadPart(const void* data, sts_uint64_t size, int part_number);
    int loadMetadata();
    int saveMetadata();
    VastS3Client* getS3Client();
    int validateOperation(int mode);
    sts_uint64_t calculateOptimalPartSize(sts_uint64_t total_size);
};

#endif /* _VAST_IMAGE_H_ */
