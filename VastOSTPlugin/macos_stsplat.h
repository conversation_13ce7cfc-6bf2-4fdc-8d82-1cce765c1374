/*
 * macOS compatibility header for OST SDK
 * This replaces the Linux platform headers for macOS builds
 */

#ifndef _MACOS_STSPLAT_H_
#define _MACOS_STSPLAT_H_

#include <stdint.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <string.h>
#include <unistd.h>

// macOS equivalents for Linux types
typedef uint8_t  __u8;
typedef uint16_t __u16;
typedef uint32_t __u32;
typedef uint64_t __u64;

typedef int8_t   __s8;
typedef int16_t  __s16;
typedef int32_t  __s32;
typedef int64_t  __s64;

// Platform-specific definitions for macOS
#define STS_PLATFORM_MACOS 1

// Thread and synchronization primitives
#include <pthread.h>
typedef pthread_mutex_t STS_MUTEX;
typedef pthread_cond_t  STS_COND;
typedef pthread_t       STS_THREAD;

// File system types
typedef off_t STS_OFFSET;
typedef size_t STS_SIZE;

// Network types
typedef int STS_SOCKET;
#define STS_INVALID_SOCKET (-1)

// Memory alignment
#define STS_ALIGN(x) __attribute__((aligned(x)))

// Compiler attributes
#define STS_INLINE inline
#define STS_FORCE_INLINE __attribute__((always_inline)) inline

// Export/Import macros for shared libraries
#ifdef __cplusplus
#define STS_EXTERN extern "C"
#else
#define STS_EXTERN extern
#endif

#ifdef __cplusplus
#define STS_EXPORT extern "C" __attribute__((visibility("default")))
#else
#define STS_EXPORT __attribute__((visibility("default")))
#endif

#define STS_IMPORT

// Platform-specific constants
#define STS_MAX_PATH 1024
#define STS_PATH_SEPARATOR '/'

// Platform-specific socket functions
#define stsp_plat_read(sock, buf, len)  recv(sock, buf, len, 0)
#define stsp_plat_write(sock, buf, cnt) send(sock, buf, cnt, 0)

// String functions compatibility - only define if not in C++ mode
#ifndef __cplusplus
#define index(s,r)      strchr(s,r)
#define rindex(s,r)     strrchr(s,r)
#endif

#endif /* _MACOS_STSPLAT_H_ */
