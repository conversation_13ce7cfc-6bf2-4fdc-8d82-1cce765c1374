

/********************************/
/* start of styles in block.xsl */

.formalpara-title {
  font-weight: bold;
}

div.blockquote-title {
  font-weight: bold;
  margin-top: 1em;
  margin-bottom: 1em;
}

span.msgmain-title {
  font-weight: bold;
}

span.msgsub-title {
  font-weight: bold;
}

span.msgrel-title {
  font-weight: bold;
}

div.msglevel, div.msgorig, div.msgaud {
  margin-top: 1em;
  margin-bottom: 1em;
}

span.msglevel-title, span.msgorig-title, span.msgaud-title {
  font-weight: bold;
}

div.msgexplan {
  margin-top: 1em;
  margin-bottom: 1em;
}

span.msgexplan-title {
  font-weight: bold;
}

/* end of styles in block.xsl */
/********************************/

/********************************/
/* start of styles in autotoc.xsl */


/* end of styles in autotoc.xsl */
/********************************/

/********************************/
/* start of styles in formal.xsl */

div.figure-title {
  font-weight: bold;
}

div.example-title {
  font-weight: bold;
}

div.equation-title {
  font-weight: bold;
}

div.table-title {
  font-weight: bold;
}

div.sidebar-title {
  font-weight: bold;
}


/* end of styles in formal.xsl */
/********************************/

/********************************/
/* start of styles in verbatim.xsl */

div.programlisting {
  white-space: pre;
  font-family: monospace;
}

div.screen {
  white-space: pre;
  font-family: monospace;
}

div.synopsis {
  white-space: pre;
  font-family: monospace;
}

/* end of styles in verbatim.xsl */
/********************************/

/* footnote rule */
hr.footnote-hr {
  width: 100;
}

/* Mike: adding style for guilabel */
span.guilabel {
  font-weight: bold;
}

