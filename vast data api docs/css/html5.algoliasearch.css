@media screen and (max-width: 767px) {
}@media (min-width: 768px) {
}@media (min-width: 768px) {
}@media (max-width: 767px) {
}@media (min-width: 768px) {
}@media (min-width: 768px) {
}@media (max-width: 767px) {
}@media (max-width: 767px) {
}.search-page .portal-body-row:before,
.search-page .portal-body-row:after {
  display: table;content: " ";
}
.search-page .portal-body-row:after {
  clear: both;
}
/**
 * Dropdown search 
 */
.aa-dropdown-menu {
  background-color: #fff;
  border: 1px solid rgba(228, 228, 228, 0.6);
  min-width: 100%;
  margin-top: 1px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  z-index: 200;
  -webkit-box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.07), 0 7px 17px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 5px 0 rgba(0, 0, 0, 0.07), 0 7px 17px 0 rgba(0, 0, 0, 0.1);
}
.aa-suggestion {
  padding: 12px;
  cursor: pointer;
  text-align: left;
}
.aa-search-body {
  font-weight: 300;
  color: #444;
  font-size: 0.875em;
}
.aa-search-title {
  font-weight: bold;
}
.aa-suggestion .aa-search-body em {
  font-weight: normal;
}
.aa-suggestion .aa-search-title em {
  color: #000;
  font-style: normal;
}
.aa-suggestion + .aa-suggestion {
  border-top: 1px solid rgba(228, 228, 228, 0.6);
}
.aa-suggestion:hover,
.aa-suggestion.aa-cursor {
  background-color: rgba(241, 241, 241, 0.35);
  background-color: #e9f3fc;
}
.overlay {
  position: fixed;
  display: none;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  opacity: 0.8;
  z-index: 100;
  height: 100%;
}
.ais-Breadcrumb-link,
.ais-HierarchicalMenu-link,
.ais-Menu-link,
.ais-Pagination-link,
.ais-RatingMenu-link {
  color: #000;
}
.ais-Breadcrumb,
.ais-ClearRefinements,
.ais-CurrentRefinements,
.ais-GeoSearch,
.ais-HierarchicalMenu,
.ais-Hits,
.ais-HitsPerPage,
.ais-InfiniteHits,
.ais-InfiniteResults,
.ais-Menu,
.ais-MenuSelect,
.ais-NumericMenu,
.ais-NumericSelector,
.ais-Pagination,
.ais-Panel,
.ais-PoweredBy,
.ais-RangeInput,
.ais-RangeSlider,
.ais-RatingMenu,
.ais-RefinementList,
.ais-Results,
.ais-ResultsPerPage,
.ais-SearchBox,
.ais-SortBy,
.ais-Stats,
.ais-ToggleRefinement {
  color: #000;
}
.ais-Breadcrumb-link:focus,
.ais-Breadcrumb-link:hover,
.ais-HierarchicalMenu-link:focus,
.ais-HierarchicalMenu-link:hover,
.ais-Menu-link:focus,
.ais-Menu-link:hover,
.ais-Pagination-link:focus,
.ais-Pagination-link:hover,
.ais-RatingMenu-link:focus,
.ais-RatingMenu-link:hover {
  color: #777;
}
/**
 * Search Page
 */
[class^=ais-] {
  font-size: inherit;
}
.search-page .portal-body-row {
  margin-right: -15px;
  margin-left: -15px;
}
.search-page .portal-header {
  height: auto;
}
.search-page .portal-search {
  margin-top: 10px;
  padding-bottom: 5px;
}
.ais-SearchBox {
  margin: 1em 0;
}
.ais-SearchBox-submit {
  position: static;
  left: auto;
  height: 46px;
  right: auto;
  top: auto;
  -webkit-transform: translateY(0);
  -ms-transform: translateY(0);
  transform: translateY(0);
}
.ais-SearchBox-submitIcon {
  width: 16px;
  height: 16px;
}
.ais-SearchBox-resetIcon path,
.ais-SearchBox-submitIcon path {
  fill: #fff;
}
.portal-header .portal-search [hidden] {
  display: none;
}
.search-page .filters-panel {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}
@media (min-width: 768px) {
  .search-page .filters-panel {
    float: left;
    width: 24%;
  }
}
.search-page .filters-panel h4 {
  margin: 1.5em 0 0.5em;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}
.search-page .search-results-panel {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}
@media (min-width: 768px) {
  .search-page .search-results-panel {
    float: left;
    width: 66%;
  }
}
.search-page .search-results-panel a:hover {
  text-decoration: none;
}
.ais-Hits-list,
.ais-InfiniteHits-list,
.ais-InfiniteResults-list,
.ais-Results-list {
  margin: 0;
}
.ais-Hits-item,
.ais-InfiniteHits-item,
.ais-InfiniteResults-item,
.ais-Results-item {
  width: 100%;
  border: 0;
  border-bottom: 1px solid #eee;
  padding: 1.6rem 0;
  -webkit-box-shadow: none;
  box-shadow: none;
  margin: 0;
  font-size: 14px;
}
.ais-Hits-item:first-child,
.ais-InfiniteHits-item:first-child,
.ais-InfiniteResults-item:first-child,
.ais-Results-item:first-child {
  padding-top: 0;
}

.ais-HierarchicalMenu-count {
    margin-left: 5px;
}

.hit-title {
  margin-top: 5px;
  margin-bottom: 5px;
}
.hit-url {
  font-size: 14px;
}
a .hit-url {
  color: #aaa;
}
.hit-body {
  margin-bottom: 5px;
  overflow: hidden;
}
a .hit-body {
  color: #333;
}
a:hover .hit-body {
  color: #777;
}
.ais-Pagination {
  margin-top: 1em;
}
.ais-Pagination-item--selected .ais-Pagination-link {
  background-color: #666;
  border-color: #666;
}
/**
 * Theme fixes 
 */
.theme1 .site-sidebar-search .algolia-autocomplete {
  width: 100%;
}
.theme2 .toolbar .algolia-autocomplete {
  position: static !important;
  display: inline !important;
}
.theme2 .toolbar .aa-dropdown-menu {
  max-height: 85vh;
  overflow-y: auto;
}
.theme2 .toolbar #aa-search-input, .theme2 .toolbar .aa-search-input {
  position: absolute !important;
}

/*# sourceMappingURL=html5.algoliasearch.css.map */