/**
 * Light grey: #fafcfc
 * Light grey (darker): #f0f4f7
 */
 
body{
    padding-top: 0px;
    position: relative;
}
.container-fluid.background-container {
    position: fixed;
    height: 100%;
    width: 100%;
    z-index:1;
    top:0;
}
.background-container * {
    height: 100%;
    padding-right: 0;
}
.background-container header {
    padding-right: 15px;
}
.background-toc {
    background: #fafcfc;
    border-right: 1px solid #f0f4f7;
}
.background-code {
    background: pink;
    margin-right: -15px;
    background: #2d3134;
}

h1,
h2,
h3,
h4,
.h1,
.h2,
.h3,
.h4{
    font-weight: 300;
    line-height: 1.25;
}
.entry-code table.table,
.entry-code .table>thead>tr>th,
.entry-code .table>tbody>tr>th,
.entry-code .table>tfoot>tr>th,
.entry-code .table>thead>tr>td,
.entry-code .table>tbody>tr>td,
.entry-code .table>tfoot>tr>td {
    border-color: #595959;
    background-color: inherit!important;
}

table.table,
.table>thead>tr>th,
.table>tbody>tr>th,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>tbody>tr>td,
.table>tfoot>tr>td {
    border-color: #ddd;
}

#toc-container .affix {
    max-height: 100%;
    overflow: auto;
}
#logotype-container.brand{
    border-bottom: 1px solid #f0f4f7;
    border-right: 1px solid #f0f4f7;
    padding-bottom: 20px;
    padding-top: 20px;
    padding-left: 15px;
}

#logotype-container img{
    margin-left: 10px;
    max-width: 100px;
}

#content-wrapper section, .content-wrapper section{
    padding-top: 20px;
}

/*pre, code.code{
    border: none;
    word-break: normal;
    white-space: pre-wrap;
}

code.code{
    white-space: pre-line;
}*/

header {
    margin-left: -15px;
}
.nav-pills > li{
    padding: 5px;
}

.dropdown-content > li{
    padding: 5px;
    float: none;
}
.brand a{
    text-decoration: none;
}
.nav-sidebar{
    padding-top: 20px;
    padding-bottom: 30px;
    padding-left: 0px;
    border-right: 1px solid #f0f4f7;
}
.nav-sidebar > .close {
    display: none;
}
.nav-sidebar .nav li a{
    padding: 6px 15px;
    color: #444;
}
.nav-sidebar .nav li.active > a{
    color: #337ab7
}
.nav-sidebar .nav .nav{
    display: none;
}
.nav-sidebar .nav .active .nav{
    display: block;
}
.nav-sidebar .nav .nav > li > a{
    padding: 5px 10px 5px 30px;
    font-size: .9em;
}
.nav-sidebar li:hover > a,
.nav-sidebar li.active > a {
    background-color: transparent !important;
}
.nav-code-selection{
    z-index: 100;
    background: #24272a;
    padding: 10px;
    margin-left: 7.5px;
    position: fixed;
    width: 100%;
    border-bottom: 1px solid #24272a;
    /*  Added for language dropdownmenu */
    flex: 1;
    height: 60px;
    overflow: hidden;
}

/*ASN: In case there's just one programming language used:*/
.nav-code-selection-hidden{
    visibility: hidden;
}
.nav-code-selection > li > a {
    color: #fff;
}
.nav-code-selection > li > a:hover {
    color: #666;
}

/**
 * Main page left/right content
 */
.entry{
    margin-top: 40px;
}
.entry-row{
    padding-bottom: 0em;
}
.entry-code{
    z-index: 2;
    padding: 60px 5px 20px 45px;
    color: #ddd;
}
/*Not necessary, done in js*/
/*.entry-code .code{
    display: none;
}*/
.entry-code > div:first-child{
    display: block;
}
.entry-text{
    background: #fff;
    padding: 0 30px;
}
[data-code-view] pre {
    padding: 30px;
    background-color:#24272a;
}
.container-fluid{
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
    position: relative;
    z-index: 10;
}

/**
 * Elements style
 */
.warning, .note, .important, .caution, .tip {
    background-color: #fafcfc;
}

/* ASN: Special for callout icons */
/*.calloutlist img, .co img {
	width: 1em;
	/\*margin-top: 0.2em;*\/
}

.co img {
	/\*margin-bottom: 0.5em;*\/
}

.calloutlist img {
	margin-left: 25px;
}

/\* ASN: If not using images for callouts, make sure the color of the callout number is not changed by the highlight plugin *\/
.co span{
    color:#54a1e5!important;
}   

.co .fa-stack .text-primary span, .calloutlist .fa-stack .text-primary {
    color: #fff!important;
}

strong.fa-stack-1x.text-primary {
    color: #fff!important;
}

.co .fa-3x, .calloutlist .fa-3x{
   font-size: 0.8em; 
}

a.co {
    font-size: 0.9em;
}

a.co span.unicode-callout {
    font-size: 1.8em!important;
    line-height: 1em;
}


a.co, .calloutlist a{
    text-decoration: none;
} 

/\*Variation on this: https://stackoverflow.com/questions/4861224/how-to-use-css-to-surround-a-number-with-a-circle*\/
a.co:focus{
    border-radius: 50%;
    width: 20px;
    height: 20px;
    padding: 8px;
    margin-right: -20px;
    border: 2px solid #666;
    border: 2px solid rgba(81, 203, 238, 1);
    box-shadow: 0 0 5px rgba(81, 203, 238, 1);
    color: #666;
    text-align: center;
    text-decoration: none;
    outline:none;
}



.calloutlist span.unicode-callout {
    font-size: 1.3em;
    line-height: 1em;
    margin-left: 25px;
}

span.plain-text-callout:before {
    content: '(';
}

span.plain-text-callout:after {
    content: ')';
}

/\*Too  much space before the parenthesis if not reducing by negative margin*\/
.co span.plain-text-callout {
    margin-left: -0.1em;
}

.calloutlist span.plain-text-callout {
    margin-left: 25px;
}



/\*Remove borders for calloutlist, and adjust padding:*\/
.calloutlist td:first-child {
    border: none!important;
    /\*padding: 0px 25px!important;*\/
    padding: 0px!important;
}

.calloutlist td:first-child a {
    /\*margin-left: 25px;*\/
}

.calloutlist td {
    border: none!important;
    padding: 0px 20px!important;
}*/

/* .flex-container {
    display: -webkit-flex;
    display: flex;
    -webkit-flex-wrap: wrap;
    flex-wrap: wrap;
    width: 100%;
}

 .flex-item {
    /\*width: 200px;*\/
    margin: 10px;
}*/

/*
 * Video containers
 */
.video-container .content {
    position: relative;
    padding-bottom: 56.25%;
    padding-top: 30px; height: 0; overflow: hidden;
    margin-bottom: 1em;
}
 
.video-container iframe,
.video-container object,
.video-container embed {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.videoobject {
    position: relative;
    padding-bottom: 56.25%;
    padding-top: 35px;
    height: 0;
    overflow: hidden;
    margin-top: 2em;
}

.videoobject iframe {
    position: absolute;
    top:0;
    left: 0;
    width: 100%;
    height: 100%;
}

pre{
    border:none;
}

/* OpenAPI imports:*/
.openapi-responses .code {
    display: block !important;
}

.openapi-responses .programlisting, .schema-sample .programlisting {
    background-color: #2d3e53;
}

.informalexample.openapi-responses, .informalexample.schema-sample {
    background-color: #2d3e53;
    border-radius: 4px;
}

/* This should really have class schema language */
.informalexample .response-language{
    background-color: #2d3e53;
    padding: 10px;
    padding-top: 20px;
}

.informalexample.openapi-samples .code {
    background-color: #24272a;
    border-radius: 4px;
}

.responses-title {
    padding: 9.5px;
    background-color: #476385;
    border-radius: 4px 4px 0px 0px;
}

.response-language{
    margin: 10px;
    margin-top: 20px;
}

.response-language > span{
    padding: 5px;
    background-color: #243142;
    border-radius: 4px;
}

.openapi-responses .programlisting {
padding-top: 5px;
}

.samples-title {
    padding: 9.5px;
    background-color: #5e666e;
    border-radius: 4px 4px 0px 0px;
}
/*End OpenAPI imports:*/

#content-wrapper .article, .content-wrapper .article {
    margin-top: 70px;
}

#toc-placeholder ul ul ul a{
    padding-left: 50px;
}

/* Because some titles in API docs may be very long without a space */
.title {
    overflow-wrap: break-word;
}

/* Light highlight style for inline code samples */

.inline-example .hljs {
  display: block;
  overflow-x: auto;
  padding: 0.5em;
  color: #333!important;
  background: #f8f8f8!important;
}

.inline-example .hljs-comment,
.inline-example .hljs-quote {
  color: #998!important;
  font-style: italic;
}

.inline-example .hljs-keyword,
.inline-example .hljs-selector-tag,
.inline-example .hljs-subst {
  color: #333!important;
  font-weight: bold;
}

.inline-example .hljs-number,
.inline-example .hljs-literal,
.inline-example .hljs-variable,
.inline-example .hljs-template-variable,
.inline-example .hljs-tag .hljs-attr {
  color: #008080!important;
}

.inline-example .hljs-string,
.inline-example .hljs-doctag {
  color: #d14!important;
}

.inline-example .hljs-title,
.inline-example .hljs-section,
.inline-example .hljs-selector-id {
  color: #900!important;
  font-weight: bold;
}

.inline-example .hljs-subst {
  font-weight: normal;
}

.inline-example .hljs-type,
.inline-example .hljs-class .hljs-title {
  color: #458!important;
  font-weight: bold;
}

.inline-example .hljs-tag,
.inline-example .hljs-name,
.inline-example .hljs-attr {
  color: #000080!important;
  font-weight: normal;
}

.inline-example .hljs-regexp,
.inline-example .hljs-link {
  color: #009926!important;
}

.inline-example .hljs-symbol,
.inline-example .hljs-bullet {
  color: #990073!important;
}

.inline-example .hljs-built_in,
.inline-example .hljs-builtin-name {
  color: #0086b3!important;
}

.inline-example .hljs-meta {
  color: #999!important;
  font-weight: bold;
}

.inline-example .hljs-deletion {
  background: #fdd!important;
}

.inline-example .hljs-addition {
  background: #dfd!important;
}

.inline-example .hljs-emphasis {
  font-style: italic;
}

.inline-example .hljs-strong {
  font-weight: bold;
}

/* End Light highlight style for inline code samples */


.nav>li.no-code-switcher{
    display:none;
}

/* Styling for API style output langauge dropdown menu */
.link-bar a {
  color: #FFF
}
.item-link a{
  display: inline-block;
  margin: 0;
  padding: 10px 15px;
  text-align: center;
  color: #FFF;
  text-decoration: none;
  border-radius: 4px;
}
.grouped-link {
  display: inline-block;
  margin: 0;
  padding: 10px;
  margin-right:15px;
  text-align: center;
  color: #FFF;
  text-decoration: none;
  border-radius: 4px;
  font-weight: bold;
}
.item-link.active a,
.item-link a.active {
  background-color: #337ab7;
  text-decoration: none;
}
.grouped-link.active {
  background-color: #337ab7;
  text-decoration: none;
}
.grouped-link:hover {
  background-color: #f1f1f1;
  color: #888;
  text-decoration: none;
}

.dropdown {
  visibility: hidden;
  position: fixed;
  padding-top: 0px;
  margin-top: 15px;
  right: 10px;
  color: #FFF;
  z-index: 100;
  background-color: #24272a;
}
.dropdown-content {
  display: none;
  position: absolute;
  right: 10px;
  padding: 10px;
  background-color: #f9f9f9;
  min-width: 80px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  max-height: 200px;
  overflow-y: scroll;
  border-radius: 4px;
}
.dropdown-content li {
  padding: 0px;
  margin: 0px;
  line-height: 1;
  box-shadow: 0px 4px 8 px 0px rgba(0, 0, 0, 0.2);
}
.dropdown-content a {
  color: black;
  padding: 0px;
  margin: 0px;
  text-decoration: none;
  display: block;
  text-align: left
}
.dropdown-content li:hover {
  background-color: #f1f1f1
}
.dropselect {
  position: absolute;
  right: 1px;
  font-size: 10px;
  color: white;
  border-radius: 1px;
  margin-right: 1px;
  margin-top: -5px;
  white-space: nowrap;
}
/* End: Styling for API style output langauge dropdown menu */