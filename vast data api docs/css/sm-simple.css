@media (min-width : 768px) {
    .sm.sm-simple {
        /*To make this work with version menu and top nav together, if used*/
        float: left;
    }

    .sm-simple ul {
        position: absolute;
        width: 12em;
    }

    .sm-simple li {
        float: left;
    }

    .sm-simple.sm-rtl li {
        float: right;
    }

    .sm-simple ul li,
    .sm-simple.sm-rtl ul li,
    .sm-simple.sm-vertical li {
        float: none;
    }

    .sm-simple a {
        /*    white-space: nowrap;*/
        white-space: normal;
    }

    .sm-simple ul a,
    .sm-simple.sm-vertical a {
        white-space: normal;
    }

    .sm-simple .sm-nowrap > li > a,
    .sm-simple .sm-nowrap > li > :not(ul) a {
        white-space: nowrap;
    }

    .theme2 .toolbar.top-nav-on {
        padding: 11px 25px;
    }
    .toolbar.top-nav-on .breadcrumb-container {
        padding: 11px 20px;
    }


    .portal-header-navbar .sm-simple {
        margin-top: 10px;
        margin-right: 15px;
    }
    .sm-simple a,
    .sm-simple a:hover,
    .sm-simple a:focus,
    .sm-simple a:active,
    .sm-simple a:visited,
    .sm-simple a.highlighted {
        padding: 11px 20px;
        color: #555;
    }

    .sm-simple > li > a,
    .sm-simple > li > a:hover,
    .sm-simple > li > a:focus,
    .sm-simple > li > a:active,
    .sm-simple > li > a.highlighted {
        /*padding-right: 30px !important;*/
        color: inherit !important;
        background: transparent !important;
    }

    .sm-simple a:hover,
    .sm-simple a:focus,
    .sm-simple a:active,
    .sm-simple a.highlighted {
        background: #eeeeee;
    }
    
    .sm-simple ul li a,
    .sm-simple ul li a:hover,
    .sm-simple ul li a:focus,
    .sm-simple ul li a:active,
    .sm-simple ul li a.highlighted
    {
        /* For glyph not to overlap when child topics in sub menu */
        padding-right: 30px;
    }

    .sm-simple .caret {
        margin-right: 8px;
    }

    .sm-simple .glyphicon {
        position: absolute;
        top: 0;
        right: 10px;
        margin: 4px;
        margin-top: 16px;
        display: inline-block;
        font-family: 'Glyphicons Halflings';
        -webkit-font-smoothing: antialiased;
        font-style: normal;
        font-weight: 300;
        line-height: 1;
        font-size: 10px;
    }

    .sm-simple .glyphicon:before {
        content: "\e258";
    }

    .sm-simple ul {
        background: #fff;
        box-shadow: 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12), 0 5px 5px -3px rgba(0, 0, 0, 0.2);
    }

    .sm-simple.sm-rtl a.has-submenu {
        padding-right: 20px;
        padding-left: 32px;
    }

    .sm-simple.sm-rtl.sm-vertical a.has-submenu {
        padding: 10px 20px;
    }

    .sm-simple.sm-rtl > li:first-child {
        border-left: 1px solid #eeeeee;
    }
    .sm-simple.sm-rtl > li:last-child {
        border-left: 0;
    }
    .sm-simple.sm-rtl ul a.has-submenu {
        padding: 11px 20px;
    }

    .sm-simple.sm-vertical li {
        border-left: 0;
        border-top: 1px solid #eeeeee;
    }
    .sm-simple.sm-vertical > li:first-child {
        border-top: 0;
    }

    .sm-simple a,
    .sm-simple a {
        font-size: 14px;
    }

    .theme3 .sm-simple,
    .theme3b .sm-simple {
        float: left;
        margin: 0;
    }

    .theme3 .sm-simple > li > a,
    .theme3b .sm-simple > li > a {
        color: inherit;
        padding-top: 15px;
        padding-bottom: 15px;
        font-weight: 400;
        text-transform: uppercase;
    }
}
