/*
 *bcpyrght
 ***************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $ *
 ***************************************************************************
 *ecpyrght
 */


/* $Id: stsnbu.h,v 1.2.44.3 2010/01/29 19:18:11 $ */

/************************************************************************
* THIS FILE DEFINES AN API BETWEEN NETBACKUP AND PLUGINS BELONGING TO   *
* EXTERNAL VENDORS. DO NOT, REPEAT, DO NOT, INSERT ANY NBU-SPECIFIC     *
* SEMANTICS IN THIS FILE.						*
************************************************************************/

/************************************************************************
* MAKE SURE THAT ALL STRUCTS PASSED THROUGH THE PROXY RPC ARE CORRECTLY *
* BYTE-SWAPPED THROUGH THE SWAPPING FUNCTIONS IN THE PROXY SERVER	*
************************************************************************/

#ifndef _STSNBU_H_ 
#define _STSNBU_H_

#include "stsi.h"

#if STS_VERSION >= 10

#define NBU_DPAID "NetBackup"
#define NBU_65    "NBU_65"
#define NBU_70    "NBU_70"
#define NBU_701   "NBU_701000"	/* numerical portion derived from RELEASE_NUM as found in NBU cmnversion.h */

#define NBU_DPAVER NBU_701

typedef enum {
	/* DON'T CHANGE VALUES */
	STS_IMGTYP_DATA	 = 0,
	STS_IMGTYP_TIR	 = 1,
	STS_IMGTYP_HDR	 = 2,
	STS_IMGTYP_IM    = 3,	/* AIR import fragment */
	STS_IMGTYP_RA    = 4,
	STS_IMGTYP_MAX	 = 5
} stsnbu_imgtype_v65_t;

typedef stsnbu_imgtype_v65_t stsnbu_imgtype_t;

/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES>*
*********************************************************/
typedef struct {
	sts_host_name_v9_t	bi_master_server;	/* the master server */
	sts_uint32_t		bi_time;		/* backup time */
	sts_uint32_t		bi_copy_number;		/* copy number */
} stsnbu_bckpid_v65_t;

typedef stsnbu_bckpid_v65_t stsnbu_bckpid_t;

/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES>*
*********************************************************/
typedef struct
{
	stsnbu_bckpid_v65_t	isi_bckpid;		/* backup identifier */
	sts_uint32_t		isi_strm_num;		/* backup stream */
	stsnbu_imgtype_v65_t	isi_img_type;		/* image type */
	sts_uint32_t		isi_frag_num;		/* fragment number */
	sts_uint32_t		isi_instance_num;	/* instance number */
	sts_host_name_v9_t	isi_client;		/* the data source host */
} stsnbu_isinfo_v65_t;


typedef char stsnbu_slpname_v701_t[1024];	/* size derived from EMM_MAX_HOSTNAME_LEN as found in Emmidlconst.idl */
typedef stsnbu_slpname_v701_t stsnbu_slpname_t;

typedef struct
{
	stsnbu_bckpid_v65_t	isi_bckpid;		/* backup identifier */
	sts_uint32_t		isi_strm_num;		/* backup stream */
	stsnbu_imgtype_v65_t	isi_img_type;		/* image type */
	sts_uint32_t		isi_frag_num;		/* fragment number */
	sts_uint32_t		isi_instance_num;	/* instance number */
	sts_host_name_v9_t	isi_client;		/* the data source host */
	stsnbu_slpname_v701_t	isi_slpname;	/* storage lifecycle policy name  */
} stsnbu_isinfo_v701_t;

typedef stsnbu_isinfo_v701_t stsnbu_isinfo_t;

#endif /* STS_VERSION */
#endif /* !_STSNBU_H_ */
