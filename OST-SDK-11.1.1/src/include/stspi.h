
/*
 *bcpyrght
 ***************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $ *
 ***************************************************************************
 *ecpyrght
 */

/* API Version 11.1 */

/* $Id: stspi.h,v 1.23.42.6 2010/03/03 12:41:04 $ */

#ifndef _STSPI_H_ 
#define _STSPI_H_

/*
 * This file includes definitions used by STS-P-I plugins. It is the only 
 * header file an external vendor's plugin needs to include explicity.
 */

#include "stsi.h"

/* for an explanation of the peculiar SWAP_* macros, see stsi.h */

typedef struct stsp_image_handle_s *stsp_image_handle_t;
#define SWAP_STSP_IMAGE_HANDLE_T(h) SWAP_STSP_IMAGE_HANDLE_S(h)

typedef struct stsp_image_list_handle_s *stsp_image_list_handle_t;
#define SWAP_STSP_IMAGE_LIST_HANDLE_T(h) SWAP_STSP_IMAGE_LIST_HANDLE_S(h)

typedef struct stsp_lsu_list_handle_s *stsp_lsu_list_handle_t;
#define SWAP_STSP_LSU_LIST_HANDLE_T(h) SWAP_STSP_LSU_LIST_HANDLE_S(h)

typedef struct stsp_server_handle_s *stsp_server_handle_t;
#define SWAP_STSP_SERVER_HANDLE_T(h) SWAP_STSP_SERVER_HANDLE_S(h)

#if STS_VERSION >= 9
typedef struct stsp_evc_handle_s *stsp_evc_handle_t;
#define SWAP_STSP_EVC_HANDLE_T(h) SWAP_STSP_EVC_HANDLE_S(h)
#endif

typedef struct {
	union {
		stsp_server_handle_t	u_server_handle;
		sts_uint64_t		u_filler;
	} sl_u;
	sts_lsu_name_v7_t sl_lsu_name;
} stsp_lsu_v7_t;
#define sl_server_handle sl_u.u_server_handle
#define SWAP_STSP_LSU_V7_T(lsu) { \
	SWAP_STSP_SERVER_HANDLE_T(&(lsu)->sl_server_handle); \
	SWAP_STS_LSU_NAME_V7_T(&(lsu)->sl_lsu_name); \
}
#ifndef MASTER_SHARED_LIB
typedef stsp_lsu_v7_t stsp_lsu_t;
#define SWAP_STSP_LSU_T(lsu) SWAP_STSP_LSU_V7_T(lsu)
#endif /* MASTER_SHARED_LIB */

#if STS_VERSION >= 9
typedef struct {
	stsp_server_handle_t	ptl_server_handle;
	sts_target_v9_t		ptl_target;
} stsp_target_lsu_v9_t;
#define SWAP_STSP_TARGET_LSU_V9_T(lsu) { \
	SWAP_STSP_SERVER_HANDLE_T(&(lsu)->ptl_server_handle); \
	SWAP_STS_TARGET_V9_T(&(lsu)->ptl_server_handle); \
}
#ifndef MASTER_SHARED_LIB
typedef stsp_target_lsu_v9_t stsp_target_lsu_t;
#define SWAP_STSP_TARGET_LSU_T(lsu) SWAP_STSP_TARGET_LSU_V9_T(lsu)
#endif /* MASTER_SHARED_LIB */
#endif /* STS_VERSION >= 9 */

#if STS_VERSION >= 11
typedef struct stsp_opid_s *stsp_opid_t;
#endif

/* exported plugin entry points */

#ifdef __cplusplus
extern "C" {
#endif

typedef int (*stsp_async_flush_v7_t)(stsp_image_handle_t image_handle);

#if STS_VERSION >= 11 
typedef int (*stsp_async_read_image_v11_t)(stsp_image_handle_t image_handle,
	void *buf, sts_uint64_t len, sts_uint64_t offset, stsp_opid_t *opid);
#endif

#if STS_VERSION >= 9
typedef int (*stsp_async_read_image_v9_t)(stsp_image_handle_t image_handle,
	void *buf, sts_uint64_t len, sts_uint64_t offset, sts_aioresult_v9_t *result);
#endif

typedef int (*stsp_async_read_image_v7_t)(stsp_image_handle_t image_handle,
	void *buf, sts_uint64_t len, sts_uint64_t offset, sts_aioresult_v7_t *result);

#if STS_VERSION >= 11
typedef int (*stsp_async_wait_v11_t)(const sts_session_def_v7_t *sd, 
		stsp_opid_t opid, int blockflag, sts_aioresult_v11_t *result);
typedef int (*stsp_async_cancel_v11_t)(const sts_session_def_v7_t *sd, 
		stsp_opid_t opid);
#endif
#if STS_VERSION >= 9
typedef int (*stsp_async_wait_v9_t)(stsp_image_handle_t image_handle,
	sts_aioresult_v9_t *result);
#endif

typedef int (*stsp_async_wait_v7_t)(stsp_image_handle_t image_handle,
	sts_aioresult_v7_t *result);

#if STS_VERSION >= 11 
typedef int (*stsp_async_write_image_v11_t)(stsp_image_handle_t image_handle,
		sts_stat_v7_t *stat, void *buf, sts_uint64_t len, sts_uint64_t offset, 
		stsp_opid_t *opid);
#endif

#if STS_VERSION >= 9
typedef int (*stsp_async_write_image_v9_t)(stsp_image_handle_t image_handle,
	sts_stat_v7_t *stat, void *buf, sts_uint64_t len, sts_uint64_t offset,
	sts_aioresult_v9_t *result);
#endif

typedef int (*stsp_async_write_image_v7_t)(stsp_image_handle_t image_handle,
	sts_stat_v7_t *stat, void *buf, sts_uint64_t len, sts_uint64_t offset,
	sts_aioresult_v7_t *result);

typedef int (*stsp_claim_v7_t)(const sts_server_name_v7_t server);

#if STS_VERSION >= 9
typedef int (*stsp_close_evchannel_v9_t)(stsp_evc_handle_t evc_handle);
#endif

typedef int (*stsp_close_image_v7_t)(stsp_image_handle_t image_handle,
	int flag, int reserved);

typedef int (*stsp_close_image_list_v7_t)(stsp_image_list_handle_t image_list_handle);

typedef int (*stsp_close_lsu_list_v7_t)(stsp_lsu_list_handle_t lsu_list_handle);

typedef int (*stsp_close_server_v7_t)(stsp_server_handle_t server_handle);

#if STS_VERSION >= 9
typedef int (*stsp_copy_extent_v9_t)(stsp_image_handle_t to_image,
	sts_uint64_t to_offset, stsp_image_handle_t from_image, sts_uint64_t from_offset,
	sts_uint64_t len, int flags, sts_uint64_t *bytescopied);
#endif

typedef int (*stsp_copy_image_v7_t)(const stsp_lsu_v7_t *to_lsu,
	const sts_image_def_v7_t *to_img, const stsp_lsu_v7_t *from_lsu,
	const sts_image_def_v7_t *from_img, int asyncflag, int pendingflag);

#if STS_VERSION >= 10
typedef int (*stsp_create_image_v10_t)(const stsp_lsu_v7_t *lsu, const sts_image_def_v10_t *img,
	int flags, stsp_image_handle_t *image_handle);
#endif

#if STS_VERSION >= 11
typedef int (*stsp_copy_image_v11_t)(
		const stsp_lsu_v7_t *to_lsu, const sts_image_def_v10_t *to_img,
		const stsp_lsu_v7_t *from_lsu, const sts_image_def_v10_t *from_img,
		const sts_opname_v11_t imageset, int eventflag);
typedef int (*stsp_async_copy_image_v11_t)(
		const stsp_lsu_v7_t *to_lsu, const sts_image_def_v10_t *to_img,
		const stsp_lsu_v7_t *from_lsu, const sts_image_def_v10_t *from_img, 
		stsp_opid_t *opid, const sts_opname_v11_t imageset, int eventflag);
typedef int (*stsp_begin_copy_image_v11_t)(const stsp_lsu_v7_t *target_lsu, 
		const sts_image_def_v10_t *target_image, 
		stsp_image_handle_t image_handle, sts_opname_v11_t imageset,  
		int eventflag);
typedef int (*stsp_end_copy_image_v11_t)(stsp_image_handle_t image_handle);
typedef int (*stsp_async_end_copy_image_v11_t)(stsp_image_handle_t image_handle,
		stsp_opid_t *opid);
typedef int (*stsp_named_async_end_copy_image_v11_t)(
		stsp_image_handle_t image_handle, const sts_opname_v11_t opname);

#endif /* STS_VERSION >= 11 */


#if STS_VERSION >= 9
typedef int (*stsp_create_image_v9_t)(const stsp_lsu_v7_t *lsu, const sts_image_def_v7_t *img,
	int flags, stsp_image_handle_t *image_handle);
#endif

typedef int (*stsp_create_image_v7_t)(const stsp_lsu_v7_t *lsu, const sts_image_def_v7_t *img,
	int pendingflag, stsp_image_handle_t *image_handle);

#if STS_VERSION >= 11
typedef int (*stsp_delete_event_v11_t)(stsp_server_handle_t svh, sts_event_v11_t *event);
#endif
#if STS_VERSION >= 9
typedef int (*stsp_delete_event_v9_t)(const sts_session_def_v7_t *sd, sts_event_v9_t *event);
#endif

typedef int (*stsp_delete_file_v7_t)(stsp_image_handle_t image_handle,
	const char **path, int npath);

#if STS_VERSION >= 8
typedef int (*stsp_delete_files_v8_t)(stsp_image_handle_t image_handle,
	const char **path, sts_uint32_t npath);
#endif

#if STS_VERSION >= 10
typedef int (*stsp_delete_image_v10_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v10_t *img, int asyncflag);
#endif

#if STS_VERSION >= 7
typedef int (*stsp_delete_image_v7_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v7_t *img, int allincr, int asyncflag);
#endif

#if STS_VERSION >= 9
typedef int (*stsp_delete_image_v9_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v7_t *img, int asyncflag);

typedef int (*stsp_find_lsu_v9_t)(stsp_server_handle_t server_handle,
	const sts_lsu_name_v7_t *wanted_lsu, sts_lsu_name_v7_t *found_lsu);

typedef int (*stsp_flush_v9_t)(stsp_image_handle_t image_handle);

typedef int (*stsp_get_event_v9_t)(stsp_evc_handle_t evc_handle,
	sts_event_v9_t *event);
#endif

#if STS_VERSION >= 11
typedef int (*stsp_get_event_v11_t)(stsp_evc_handle_t evc_handle,
	sts_event_v11_t *event);
typedef int (*stsp_get_event_payload_v11_t)(stsp_server_handle_t svh, 
		void *plbuf, const sts_event_v11_t *event);
#endif /* STS_VERSION >= 11 */ 

#if STS_VERSION >= 10
typedef int (*stsp_get_image_group_byname_v10_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v10_t *img, sts_uint32_t *groups);
#endif

#if STS_VERSION >= 8
typedef int (*stsp_get_image_group_v8_t)(stsp_image_handle_t image_handle,
	sts_uint32_t *groups);

typedef int (*stsp_get_image_group_byname_v8_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v7_t *img, sts_uint32_t *groups);
#endif

typedef int (*stsp_get_image_prop_v7_t)(stsp_image_handle_t image_handle,
	sts_image_info_v7_t *info);

#if STS_VERSION >= 10
typedef int (*stsp_get_image_prop_v10_t)(stsp_image_handle_t image_handle,
	sts_image_info_v10_t *info);
#endif

#if STS_VERSION >= 10
typedef int (*stsp_get_image_prop_byname_v10_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v10_t *img, sts_image_info_v10_t *info);
#endif

typedef int (*stsp_get_image_prop_byname_v7_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v7_t *img, sts_image_info_v7_t *info);

#if STS_VERSION >= 11 
typedef int (*stsp_get_lsu_prop_byname_v11_t)(const stsp_lsu_v7_t *lsu,
	sts_lsu_info_v11_t *info);
typedef int (*stsp_get_lsu_replication_prop_v11_t)(const stsp_lsu_v7_t *lsu, 
		sts_uint32_t nsource, sts_lsu_spec_v11_t *source,
		sts_uint32_t ntarget, sts_lsu_spec_v11_t *target);
typedef int (*stsp_iocontrol_v11_t)(stsp_server_handle_t server_handle, 
		int cmd, void *args, int ioflag, sts_uint32_t len);
#endif

#if STS_VERSION >= 9
typedef int (*stsp_get_lsu_prop_byname_v9_t)(const stsp_lsu_v7_t *lsu,
	sts_lsu_info_v9_t *info);
#endif

#if STS_VERSION >= 8
typedef int (*stsp_get_lsu_prop_byname_v8_t)(const stsp_lsu_v7_t *lsu,
	sts_lsu_info_v8_t *info);
#endif

typedef int (*stsp_get_lsu_prop_byname_v7_t)(const stsp_lsu_v7_t *lsu,
	sts_lsu_info_v7_t *info);

#if STS_VERSION >= 11
typedef int (*stsp_get_server_config_v11_t)(stsp_server_handle_t server_handle,
	char *buf, sts_uint32_t buflen, sts_uint32_t *maxlen);
#endif

#if STS_VERSION >= 8
typedef int (*stsp_get_server_prop_v8_t)(stsp_server_handle_t server_handle,
	sts_server_info_v8_t *info);
#endif

typedef int (*stsp_get_server_prop_v7_t)(stsp_server_handle_t server_handle,
	sts_server_info_v7_t *info);

#if STS_VERSION >= 8
typedef int (*stsp_get_server_prop_byname_v8_t)(const sts_session_def_v7_t *sd,
	const sts_server_name_v7_t server, sts_server_info_v8_t *info);
#endif

typedef int (*stsp_get_server_prop_byname_v7_t)(const sts_session_def_v7_t *sd,
	const sts_server_name_v7_t server, sts_server_info_v7_t *info);

typedef int (*stsp_include_in_image_v7_t)(stsp_image_handle_t image_to_handle,
	sts_uint64_t offset_to, stsp_image_handle_t image_from_handle,
	sts_uint64_t offset_from, sts_uint64_t len);

#if STS_VERSION >= 10
typedef int (*stsp_include_in_image_v10_t)(stsp_image_handle_t image_to_handle,
	sts_uint64_t offset_to, stsp_image_handle_t image_from_handle,
	sts_uint64_t offset_from, sts_uint64_t len, sts_uint64_t *bytesincluded);
#endif 


#if STS_VERSION >= 9
typedef int (*stsp_ioctl_v9_t)(stsp_server_handle_t server_handle, int cmd,
	void *args);

typedef int (*stsp_label_lsu_v9_t)(const stsp_lsu_v7_t *lsu, sts_lsu_label_v9_t lsu_label);
#endif

#if STS_VERSION >= 10
typedef int (*stsp_list_image_v10_t)(stsp_image_list_handle_t image_list_handle,
	sts_image_def_v10_t *img);
#endif

typedef int (*stsp_list_image_v7_t)(stsp_image_list_handle_t image_list_handle,
	sts_image_def_v7_t *img);

typedef int (*stsp_list_lsu_v7_t)(stsp_lsu_list_handle_t lsu_list_handle,
	sts_lsu_name_v7_t *lsu);

#if STS_VERSION >= 11 
typedef int (*stsp_named_async_wait_v11_t)(stsp_server_handle_t server_handle, 
		const sts_opname_v11_t opname, int blockflag, 
		sts_aioresult_v11_t *result);
typedef int (*stsp_named_async_cancel_v11_t)(
		stsp_server_handle_t server_handle,  const sts_opname_v11_t opname);
typedef int (*stsp_named_async_copy_image_v11_t)(
		const stsp_lsu_v7_t *target_lsu, 
		const sts_image_def_v10_t *target_image, 
		const stsp_lsu_v7_t *source_lsu, 
		const sts_image_def_v10_t *source_image, 
		const sts_opname_v11_t opname,
		const sts_opname_v11_t imageset, int eventflag);
typedef int (*stsp_named_async_status_v11_t)(stsp_server_handle_t server_handle,
		sts_opname_v11_t opname);
#endif

#if STS_VERSION >= 9
typedef int (*stsp_open_evchannel_v9_t)(const sts_session_def_v7_t *sd,
	const sts_server_name_v7_t server, const sts_cred_v7_t *cred,
	const sts_interface_v7_t iface, sts_evhandler_v9_t evhandler,
	sts_handle_t evc_handle, sts_event_v9_t *event, void *tag,
	int flags, stsp_evc_handle_t *pevc_handle);
#endif
#if STS_VERSION >= 11 
typedef int (*stsp_open_evchannel_v11_t)(const sts_session_def_v7_t *sd,
	const sts_server_name_v7_t server, const sts_cred_v7_t *cred,
	const sts_interface_v7_t iface, sts_evhandler_v11_t handler,
	sts_event_v11_t *event, int flags, 
	sts_evseqno_v11_t evseqno, stsp_evc_handle_t *pevc_handle);
#endif 

#if STS_VERSION >= 10
typedef int (*stsp_open_image_v10_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v10_t *img, int mode, stsp_image_handle_t *image_handle);
#endif

typedef int (*stsp_open_image_v7_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v7_t *img, int mode, stsp_image_handle_t *image_handle);

#if STS_VERSION >= 10
typedef int (*stsp_open_image_group_list_v10_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v10_t *img, int type,
	stsp_image_list_handle_t *image_list_handle);
#endif

#if STS_VERSION >= 8
typedef int (*stsp_open_image_group_list_v8_t)(const stsp_lsu_v7_t *lsu,
	const sts_image_def_v7_t *img, int type,
	stsp_image_list_handle_t *image_list_handle);
#endif

#if STS_VERSION >= 10
typedef int (*stsp_open_image_list_v10_t)(const stsp_lsu_v7_t *lsu, int type,
	stsp_image_list_handle_t *image_list_handle);
#endif

#if STS_VERSION >= 9
typedef int (*stsp_open_image_list_v7_t)(const stsp_lsu_v7_t *lsu, int type,
	stsp_image_list_handle_t *image_list_handle);
#endif

#if STS_VERSION >= 11 
typedef int (*stsp_open_lsu_list_v11_t)(stsp_server_handle_t server_handle,
	const sts_lsu_def_v11_t *lsudef, stsp_lsu_list_handle_t *lsu_list_handle);
#endif
#if STS_VERSION >= 9
typedef int (*stsp_open_lsu_list_v9_t)(stsp_server_handle_t server_handle,
	const sts_lsu_def_v9_t *lsudef, stsp_lsu_list_handle_t *lsu_list_handle);
#endif

typedef int (*stsp_open_lsu_list_v7_t)(stsp_server_handle_t server_handle,
	stsp_lsu_list_handle_t *lsu_list_handle);

typedef int (*stsp_open_server_v7_t)(const sts_session_def_v7_t *sd,
	const sts_server_name_v7_t server, const sts_cred_v7_t *cred,
	const sts_interface_v7_t iface, stsp_server_handle_t *server_handle);

#if STS_VERSION >= 9
typedef int (*stsp_open_target_server_v9_t)(stsp_server_handle_t server_handle,
	const sts_server_name_v7_t server, const sts_cred_v7_t *cred,
	const sts_interface_v7_t iface, stsp_server_handle_t *target_server_handle);
#endif

typedef int (*stsp_read_image_v7_t)(stsp_image_handle_t image_handle, void *buf,
	sts_uint64_t len, sts_uint64_t offset, sts_uint64_t *bytesread);

#if STS_VERSION >= 9
typedef int (*stsp_read_image_meta_v9_t)(stsp_image_handle_t image_handle, void *buf,
	sts_uint64_t len, sts_uint64_t offset, sts_uint64_t *bytesread);
#endif

#if STS_VERSION >= 11
typedef int (*stsp_set_server_config_v11_t)(stsp_server_handle_t server_handle,
	const char *buf, char *msgbuf, sts_uint32_t msgbuflen);
#endif

typedef int (*stsp_write_image_v7_t)(stsp_image_handle_t image_handle,
	sts_stat_v7_t *stat, void *buf, sts_uint64_t len, sts_uint64_t offset,
	sts_uint64_t *byteswritten);

#if STS_VERSION >= 9
typedef int (*stsp_write_image_meta_v9_t)(stsp_image_handle_t image_handle,
	void *buf, sts_uint64_t len, sts_uint64_t offset, sts_uint64_t *byteswritten);
#endif

typedef int (*stsp_terminate_v7_t)(void); /* same for all versions */

#ifdef __cplusplus
}
#endif
#if STS_VERSION >= 11
typedef struct {
	stsp_flush_v9_t				v11_flush;
	stsp_async_read_image_v11_t		v11_async_read_image;
	stsp_async_wait_v11_t			v11_async_wait;
	stsp_async_write_image_v11_t		v11_async_write_image;
	stsp_claim_v7_t				v11_claim;
	stsp_close_evchannel_v9_t		v11_close_evchannel;
	stsp_close_image_v7_t			v11_close_image;
	stsp_close_image_list_v7_t		v11_close_image_list;
	stsp_close_lsu_list_v7_t		v11_close_lsu_list;
	stsp_close_server_v7_t			v11_close_server;
	stsp_copy_extent_v9_t			v11_copy_extent;
	stsp_create_image_v10_t			v11_create_image;
	stsp_delete_event_v11_t			v11_delete_event;
	stsp_delete_files_v8_t			v11_delete_files;
	stsp_delete_image_v10_t			v11_delete_image;
	stsp_get_event_v11_t			v11_get_event;
	stsp_get_image_group_v8_t		v11_get_image_group;
	stsp_get_image_group_byname_v10_t	v11_get_image_group_byname;
	stsp_get_image_prop_v10_t		v11_get_image_prop;
	stsp_get_image_prop_byname_v10_t	v11_get_image_prop_byname;
	stsp_get_lsu_prop_byname_v11_t		v11_get_lsu_prop_byname;
	stsp_get_server_prop_v8_t		v11_get_server_prop;
	stsp_get_server_prop_byname_v8_t	v11_get_server_prop_byname;
	stsp_include_in_image_v10_t		v11_include_in_image;
	stsp_ioctl_v9_t				v11_ioctl;
	stsp_list_image_v10_t			v11_list_image;
	stsp_list_lsu_v7_t			v11_list_lsu;
	stsp_open_evchannel_v11_t		v11_open_evchannel;
	stsp_open_lsu_list_v11_t			v11_open_lsu_list;
	stsp_open_server_v7_t			v11_open_server;
	stsp_open_target_server_v9_t		v11_open_target_server;
	stsp_open_image_v10_t			v11_open_image;
	stsp_open_image_group_list_v10_t	v11_open_image_group_list;
	stsp_open_image_list_v7_t		v11_open_image_list;
	stsp_read_image_v7_t			v11_read_image;
	stsp_terminate_v7_t			v11_terminate;
	stsp_write_image_v7_t			v11_write_image;
	/* add at end to avoid changing plugins that don't support these */
	stsp_find_lsu_v9_t			v11_find_lsu;
	stsp_label_lsu_v9_t			v11_label_lsu;
	stsp_read_image_meta_v9_t		v11_read_image_meta;
	stsp_write_image_meta_v9_t		v11_write_image_meta;
	stsp_async_cancel_v11_t			v11_async_cancel;
	stsp_async_copy_image_v11_t		v11_async_copy_image;
	stsp_copy_image_v11_t			v11_copy_image;
	stsp_get_event_payload_v11_t 		v11_get_event_payload;
	stsp_named_async_cancel_v11_t 		v11_named_async_cancel;
	stsp_named_async_copy_image_v11_t 	v11_named_async_copy_image;
	stsp_named_async_status_v11_t 		v11_named_async_status;
	stsp_named_async_wait_v11_t 		v11_named_async_wait;
	stsp_get_server_config_v11_t		v11_get_server_config;
	stsp_set_server_config_v11_t		v11_set_server_config;
	stsp_begin_copy_image_v11_t					v11_begin_copy_image;
	stsp_end_copy_image_v11_t					v11_end_copy_image;
	stsp_async_end_copy_image_v11_t				v11_async_end_copy_image;
	stsp_named_async_end_copy_image_v11_t		v11_named_async_end_copy_image;
	stsp_get_lsu_replication_prop_v11_t	v11_get_lsu_replication_prop;
	stsp_iocontrol_v11_t	v11_iocontrol;
} stspi_ep_v11_t;
#endif /* STS_VERSION >= 11 */
#if STS_VERSION >= 10
typedef struct {
	stsp_flush_v9_t				v10_flush;
	stsp_async_read_image_v9_t		v10_async_read_image;
	stsp_async_wait_v9_t			v10_async_wait;
	stsp_async_write_image_v9_t		v10_async_write_image;
	stsp_claim_v7_t				v10_claim;
	stsp_close_evchannel_v9_t		v10_close_evchannel;
	stsp_close_image_v7_t			v10_close_image;
	stsp_close_image_list_v7_t		v10_close_image_list;
	stsp_close_lsu_list_v7_t		v10_close_lsu_list;
	stsp_close_server_v7_t			v10_close_server;
	stsp_copy_extent_v9_t			v10_copy_extent;
	stsp_create_image_v10_t			v10_create_image;
	stsp_delete_event_v9_t			v10_delete_event;
	stsp_delete_files_v8_t			v10_delete_files;
	stsp_delete_image_v10_t			v10_delete_image;
	stsp_get_event_v9_t			v10_get_event;
	stsp_get_image_group_v8_t		v10_get_image_group;
	stsp_get_image_group_byname_v10_t	v10_get_image_group_byname;
	stsp_get_image_prop_v10_t		v10_get_image_prop;
	stsp_get_image_prop_byname_v10_t	v10_get_image_prop_byname;
	stsp_get_lsu_prop_byname_v9_t		v10_get_lsu_prop_byname;
	stsp_get_server_prop_v8_t		v10_get_server_prop;
	stsp_get_server_prop_byname_v8_t	v10_get_server_prop_byname;
	stsp_include_in_image_v10_t		v10_include_in_image;
	stsp_ioctl_v9_t				v10_ioctl;
	stsp_list_image_v10_t			v10_list_image;
	stsp_list_lsu_v7_t			v10_list_lsu;
	stsp_open_evchannel_v9_t		v10_open_evchannel;
	stsp_open_lsu_list_v9_t			v10_open_lsu_list;
	stsp_open_server_v7_t			v10_open_server;
	stsp_open_target_server_v9_t		v10_open_target_server;
	stsp_open_image_v10_t			v10_open_image;
	stsp_open_image_group_list_v10_t	v10_open_image_group_list;
	stsp_open_image_list_v7_t		v10_open_image_list;
	stsp_read_image_v7_t			v10_read_image;
	stsp_terminate_v7_t			v10_terminate;
	stsp_write_image_v7_t			v10_write_image;
	/* add at end to avoid changing plugins that don't support these */
	stsp_find_lsu_v9_t			v10_find_lsu;
	stsp_label_lsu_v9_t			v10_label_lsu;
	stsp_read_image_meta_v9_t		v10_read_image_meta;
	stsp_write_image_meta_v9_t		v10_write_image_meta;
} stspi_ep_v10_t;
#endif /* STS_VERSION >= 10 */

#if STS_VERSION >= 9
typedef struct {
	stsp_flush_v9_t				v9_flush;
	stsp_async_read_image_v9_t		v9_async_read_image;
	stsp_async_wait_v9_t			v9_async_wait;
	stsp_async_write_image_v9_t		v9_async_write_image;
	stsp_claim_v7_t				v9_claim;
	stsp_close_evchannel_v9_t		v9_close_evchannel;
	stsp_close_image_v7_t			v9_close_image;
	stsp_close_image_list_v7_t		v9_close_image_list;
	stsp_close_lsu_list_v7_t		v9_close_lsu_list;
	stsp_close_server_v7_t			v9_close_server;
	stsp_copy_extent_v9_t			v9_copy_extent;
	stsp_create_image_v9_t			v9_create_image;
	stsp_delete_event_v9_t			v9_delete_event;
	stsp_delete_files_v8_t			v9_delete_files;
	stsp_delete_image_v9_t			v9_delete_image;
	stsp_get_event_v9_t			v9_get_event;
	stsp_get_image_group_v8_t		v9_get_image_group;
	stsp_get_image_group_byname_v8_t	v9_get_image_group_byname;
	stsp_get_image_prop_v7_t		v9_get_image_prop;
	stsp_get_image_prop_byname_v7_t		v9_get_image_prop_byname;
	stsp_get_lsu_prop_byname_v9_t		v9_get_lsu_prop_byname;
	stsp_get_server_prop_v8_t		v9_get_server_prop;
	stsp_get_server_prop_byname_v8_t	v9_get_server_prop_byname;
	stsp_include_in_image_v7_t		v9_include_in_image;
	stsp_ioctl_v9_t				v9_ioctl;
	stsp_list_image_v7_t			v9_list_image;
	stsp_list_lsu_v7_t			v9_list_lsu;
	stsp_open_evchannel_v9_t		v9_open_evchannel;
	stsp_open_lsu_list_v9_t			v9_open_lsu_list;
	stsp_open_server_v7_t			v9_open_server;
	stsp_open_target_server_v9_t		v9_open_target_server;
	stsp_open_image_v7_t			v9_open_image;
	stsp_open_image_group_list_v8_t		v9_open_image_group_list;
	stsp_open_image_list_v7_t		v9_open_image_list;
	stsp_read_image_v7_t			v9_read_image;
	stsp_terminate_v7_t			v9_terminate;
	stsp_write_image_v7_t			v9_write_image;
	/* add at end to avoid changing plugins that don't support these */
	stsp_find_lsu_v9_t			v9_find_lsu;
	stsp_label_lsu_v9_t			v9_label_lsu;
	stsp_read_image_meta_v9_t		v9_read_image_meta;
	stsp_write_image_meta_v9_t		v9_write_image_meta;
} stspi_ep_v9_t;
#endif /* STS_VERSION >= 9 */

#if STS_VERSION >= 8

typedef struct {
	stsp_async_flush_v7_t			v8_async_flush;
	stsp_async_read_image_v7_t		v8_async_read_image;
	stsp_async_wait_v7_t			v8_async_wait;
	stsp_async_write_image_v7_t		v8_async_write_image;
	stsp_claim_v7_t				v8_claim;
	stsp_close_image_v7_t			v8_close_image;
	stsp_close_image_list_v7_t		v8_close_image_list;
	stsp_close_lsu_list_v7_t		v8_close_lsu_list;
	stsp_close_server_v7_t			v8_close_server;
	stsp_copy_image_v7_t			v8_copy_image;
	stsp_create_image_v7_t			v8_create_image;
	stsp_delete_files_v8_t			v8_delete_files;
	stsp_delete_image_v7_t			v8_delete_image;
	stsp_get_image_group_v8_t		v8_get_image_group;
	stsp_get_image_group_byname_v8_t	v8_get_image_group_byname;
	stsp_get_image_prop_v7_t		v8_get_image_prop;
	stsp_get_image_prop_byname_v7_t		v8_get_image_prop_byname;
	stsp_get_lsu_prop_byname_v8_t		v8_get_lsu_prop_byname;
	stsp_get_server_prop_v8_t		v8_get_server_prop;
	stsp_get_server_prop_byname_v8_t	v8_get_server_prop_byname;
	stsp_include_in_image_v7_t		v8_include_in_image;
	stsp_list_image_v7_t			v8_list_image;
	stsp_list_lsu_v7_t			v8_list_lsu;
	stsp_open_lsu_list_v7_t			v8_open_lsu_list;
	stsp_open_server_v7_t			v8_open_server;
	stsp_open_image_v7_t			v8_open_image;
	stsp_open_image_group_list_v8_t		v8_open_image_group_list;
	stsp_open_image_list_v7_t		v8_open_image_list;
	stsp_read_image_v7_t			v8_read_image;
	stsp_terminate_v7_t			v8_terminate;
	stsp_write_image_v7_t			v8_write_image;
} stspi_ep_v8_t;

#endif /* STS_VERSION >= 8 */

typedef struct {
	stsp_async_flush_v7_t			v7_async_flush;
	stsp_async_read_image_v7_t		v7_async_read_image;
	stsp_async_wait_v7_t			v7_async_wait;
	stsp_async_write_image_v7_t		v7_async_write_image;
	stsp_claim_v7_t				v7_claim;
	stsp_close_image_v7_t			v7_close_image;
	stsp_close_image_list_v7_t		v7_close_image_list;
	stsp_close_lsu_list_v7_t		v7_close_lsu_list;
	stsp_close_server_v7_t			v7_close_server;
	stsp_copy_image_v7_t			v7_copy_image;
	stsp_create_image_v7_t			v7_create_image;
	stsp_delete_file_v7_t			v7_delete_file;
	stsp_delete_image_v7_t			v7_delete_image;
	stsp_get_image_prop_v7_t		v7_get_image_prop;
	stsp_get_image_prop_byname_v7_t		v7_get_image_prop_byname;
	stsp_get_lsu_prop_byname_v7_t		v7_get_lsu_prop_byname;
	stsp_get_server_prop_v7_t		v7_get_server_prop;
	stsp_get_server_prop_byname_v7_t	v7_get_server_prop_byname;
	stsp_include_in_image_v7_t		v7_include_in_image;
	stsp_list_image_v7_t			v7_list_image;
	stsp_list_lsu_v7_t			v7_list_lsu;
	stsp_open_lsu_list_v7_t			v7_open_lsu_list;
	stsp_open_server_v7_t			v7_open_server;
	stsp_open_image_v7_t			v7_open_image;
	stsp_open_image_list_v7_t		v7_open_image_list;
	stsp_read_image_v7_t			v7_read_image;
	stsp_terminate_v7_t			v7_terminate;
	stsp_write_image_v7_t			v7_write_image;
} stspi_ep_v7_t;

typedef struct {
	sts_uint64_t spx_version; /* set by plugin - e.g. 7 */
	union {
		stspi_ep_v7_t *v7_ep;
#if STS_VERSION >= 8
		stspi_ep_v8_t *v8_ep;
#endif
#if STS_VERSION >= 9
		stspi_ep_v9_t *v9_ep;
#endif
#if STS_VERSION >= 10
		stspi_ep_v10_t *v10_ep;
#endif
#if STS_VERSION >= 11
		stspi_ep_v11_t *v11_ep;
#endif
	} spx_ep;
	sts_pgn_def_v9_t spx_def;
} stspi_api_t;

#ifdef __cplusplus
extern "C" {
#endif
typedef int (*stsp_init_t)(sts_uint64_t core_version, const char *path,
	stspi_api_t *api);
#ifdef __cplusplus
}
#endif

#define STSPI_INIT "stspi_init"

#define STSPI_ISMT "stspi_ismt" /* existence means plugin is MT */

/*
 * An STS-P-I plugin must export an entry point stsp_init() of type stsp_init_t.
 * The interface to this function will never (?) change. The core library will
 * extract this entry point with dlsym or GetProcAddress(); the plugin
 * will return the plugin API through argument api. Any entry
 * point not implemented can be a null function pointer, or can return
 * STS_ENOTSUP.
 * 
 * In stspi_api_t.spx_version, the plugin must return the API version
 * core_version passed to stsp_init(), if that version is supported
 * by the plugin (otherwise error).
 *
 * The core library will subseqently call the plugin through the returned
 * API.
 */
 
#endif /* ! _STSPI_H_ */

