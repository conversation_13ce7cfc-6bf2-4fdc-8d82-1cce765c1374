/*bcpyrght
 ***************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $ *
 ***************************************************************************
 *ecpyrght
 */

/* $Id: stsplat.h,v 1.13 2008/07/14 19:09:48 $ */

#ifndef _STSPLAT_H_
#define _STSPLAT_H_

#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>

#include <sys/types.h>
#include <pthread.h>
#include <dirent.h>
#include <dlfcn.h>
#include <stdarg.h>
#include <netdb.h>
#include <string.h>

#include "ststypes.h"

#define stsp_plat_read(sock, buf, len) read( sock, buf, len);
#define stsp_plat_write(sock, buf, cnt) write(sock, buf, cnt);
#define min(a,b) ((a) < (b)? (a):(b))

#define STS_SO_SFX   "so"

#ifdef __cplusplus
#define STS_EXTERN extern "C"
#else
#define STS_EXTERN extern
#endif

#ifdef __cplusplus
#define STS_EXPORT extern "C"
#else
#define STS_EXPORT
#endif

#ifdef __cplusplus
#define STS_IMPORT extern "C"
#else
#define STS_IMPORT extern
#endif

#endif /* !_STSPLAT_H_ */

