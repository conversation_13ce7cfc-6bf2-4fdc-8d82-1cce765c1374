/*bcpyrght
 ***************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $ *
 ***************************************************************************
 *ecpyrght
 */

/* $Id: ststypes.h,v 1.1 2005/12/14 02:07:49 $ */

#ifndef _STSTYPES_H_
#define _STSTYPES_H_

#include <sys/types.h>

typedef int16_t sts_int16_t;
typedef int32_t sts_int32_t;
typedef int64_t sts_int64_t;

typedef uint16_t sts_uint16_t;
typedef uint32_t sts_uint32_t;
typedef uint64_t sts_uint64_t;

typedef int sts_socket;
#define STS_SOCKET_ERROR (-1)

#ifdef LATER
typedef long long longlong_t;
typedef unsigned long long u_longlong_t;
#endif

#endif /* !_STHPLAT_H_ */
