/*bcpyrght
 ***************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $ *
 ***************************************************************************
 *ecpyrght
 */

/* $Id: stsplat.h,v 1.3 2006/08/16 17:50:57 $ */

#ifndef _STSPLAT_H_
#define _STSPLAT_H_

#include <windows.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <sys/timeb.h>
#include <time.h>

#include "ststypes.h"

#ifdef index
#undef index
#endif
#ifdef rindex
#undef rindex
#endif

#define index(s,r)      strchr(s,r)
#define rindex(s,r)     strrchr(s,r)

#define stsp_plat_read(sock, buf, len)	recv(sock, buf, len, 0);
#define stsp_plat_write(sock, buf, cnt) send(sock, buf, cnt, 0);

#ifdef __cplusplus
#define STS_EXTERN extern "C"
#else
#define STS_EXTERN extern
#endif

#ifdef __cplusplus
#define STS_EXPORT extern "C" __declspec(dllexport)
#else
#define STS_EXPORT __declspec(dllexport)
#endif

#ifdef __cplusplus
#define STS_IMPORT extern "C" __declspec(dllimport)
#else
#define STS_IMPORT __declspec(dllimport)
#endif

#endif /* !_STSPLAT_H_ */

