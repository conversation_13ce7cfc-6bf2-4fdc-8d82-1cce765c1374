/*bcpyrght
 ***************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $ *
 ***************************************************************************
 *ecpyrght
 */

/* $Id: ststypes.h,v 1.2 2006/07/25 18:39:09 $ */

#ifndef _STSTYPES_H_
#define _STSTYPES_H_

#include <wtypes.h>

typedef short		sts_int16_t;
typedef INT32		sts_int32_t;
typedef INT64		sts_int64_t;

typedef unsigned short	sts_uint16_t;
typedef UINT32		sts_uint32_t;
typedef UINT64		sts_uint64_t;

typedef LONGLONG longlong_t;
typedef ULONGLONG u_longlong_t;

typedef SOCKET sts_socket;

typedef int socklen_t;

#define STS_INVALID_SOCKET (-1)
#define STS_SOCKET_ERROR (-1)

#endif /* !_STSTYPES_H_ */

