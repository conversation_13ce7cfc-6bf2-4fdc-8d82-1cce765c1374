/*
 *bcpyrght
 ***************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $ *
 ***************************************************************************
 *ecpyrght
 */

/* API Version 11.1 */

/* $Id: stsi.h,v 1.73.14.12 2010/03/03 12:41:04 $ */

/************************************************************************
* THIS FILE DEFINES AN API BETWEEN NETBACKUP AND PL<PERSON>GINS BELONGING TO   *
* EXTERNAL VENDORS. DO NOT, REPEAT, DO NOT, INSERT ANY NBU-SPECIFIC     *
* SEMANTICS IN THIS FILE.						*
************************************************************************/

/************************************************************************
* MAKE SURE THAT ALL STRUCTS PASSED THROUGH THE PROXY RPC ARE CORRECTLY *
* BYTE-SWAPPED THROUGH THE SWAPPING FUNCTIONS IN THE PROXY SERVER	*
************************************************************************/

#ifndef _STSI_H_ 
#define _STSI_H_

/*
 * This file includes definitions used by clients of STS-I,
 * the STS-I core library, and STS-I plugins.
 */

/*
 * KEEP THIS AT THE TOP OF THE FILE.
 * Versions 7..11 supported in the .h files.
 */
#ifndef STS_VERSION
#define STS_VERSION 11
#endif

#ifndef STS_MINOR_VERSION
#define STS_MINOR_VERSION 1 
#endif

#include "stsplat.h"

#ifdef STS_EXTERNAL_VENDOR

#define STS_MIN_VERSION 9

/* these definitions replace sthsts.h which is not part of the SDK */
typedef struct {
	sts_uint64_t reserved;
} sts_stat_v7_t;
#ifndef MASTER_SHARED_LIB
typedef sts_stat_v7_t sts_stat_t;
#endif /* MASTER_SHARED_LIB */

/* for sld_saveas and img_saveas */
#define STS_SA_OPTIMIZED_IMAGE	0x2	/* space-opaque optimized image */
#define STS_SA_IMAGE            0x4     /* opaque image */

#define STS_MAX_STREAM_FORMAT   16
typedef char sts_stream_format_v7_t[STS_MAX_STREAM_FORMAT];
#define SWAP_STS_STREAM_FORMAT_V7_T(f)
#ifndef MASTER_SHARED_LIB
typedef sts_stream_format_v7_t sts_stream_format_t;
#define SWAP_STS_STREAM_FORMAT_T(f) SWAP_STS_STREAM_FORMAT_V7_T(f)
#endif /* MASTER_SHARED_LIB */

#else /* !STS_EXTERNAL_VENDOR */

#define STS_MIN_VERSION 9

#include "sthsts.h"
#if STS_V7_COMPAT || STS_V8_COMPAT
#define STS_COMPAT
#endif

#endif /* !STS_EXTERNAL_VENDOR */

#if STS_VERSION >= 9
#define STS_MAX_VENDOR_VERSION 32
typedef char sts_vendor_version_v9_t[STS_MAX_VENDOR_VERSION];
#ifndef MASTER_SHARED_LIB
typedef sts_vendor_version_v9_t sts_vendor_version_t;
#endif /* MASTER_SHARED_LIB */
#endif

/* required name format for STS-I plugins */
#define STS_PL_PFX		"libstspi" /* e.g. libstspi{vendor}.dll */
#define STS_PL_SFX		"MT"	/* e.g. libstspi{vendor}MT.dll */

/* all image read/write is a multiple of this block size */
#define STS_BLOCK_SIZE		512

/* keep all these n*8 for convenient network transmission */
#define STS_MAX_CERT		1024	/* certificate length */
#define STS_MAX_DATE		32	/* date string for images */
#define STS_MAX_IF		32	/* count of storage server interfaces */
#define STS_MAX_LSUATTNMSG	1024	/* LSU attention message */
#define STS_MAX_LSUDESCRIPTION	1024
#define STS_MAX_POLICY		1024	/* policy description */
#define STS_MAX_PFX		8	/* prefixes per plugin */
#define STS_MAX_PFXLEN		32	/* plugin prefix, including null */
#define STS_MAX_PFX_LABEL	32	/* description per prefix */
#define STS_MAX_PGN_NAME	32	/* name within directory */

#if STS_VERSION >= 9
#define STS_MAX_IFNAME		512	/* interface name */
#define STS_MAX_IFNAME_V7       64
#define STS_MAX_IMAGENAME	1024
#define STS_MAX_IMAGENAME_V7    256
#define STS_MAX_LSUNAME		1024
#define STS_MAX_LSUNAME_V7      256
#define STS_MAX_PGN		64	/* installed plugins */
#define STS_MAX_STSNAME		512
#define STS_MAX_STSNAME_V7      64
#else
#define STS_MAX_IFNAME		64	/* interface name */
#define STS_MAX_IMAGENAME	256
#define STS_MAX_LSUNAME		256
#define STS_MAX_PGN		32	/* installed plugins */
#define STS_MAX_STSNAME		64
#endif

#define	STS_MAX_ISNAME		STS_MAX_STSNAME
#define	STS_MAX_ISINFO_LEN	4096
#define STS_MAX_DPAID		64
#define STS_MAX_DPAVER		32

/* these are not relevant to external vendors */
#define STS_MAX_MOUNTNAME	1024	/* f/s mount point */
#define STS_MAX_STH		32	/* stream handlers */
#define GETHOSTBYNAME_DATASIZE	1024

#if STS_VERSION >= 9
#define STS_MAX_CONFIG		2048	/* server or LSU configuration */
#define STS_MAX_EVPL		2048	/* event payload */
#define STS_MAX_LSU_LABEL	64	/* LSU label */
#endif

/*
 * The ugly SWAP_* macros will eventually disappear from this file. Their
 * purpose is to byte-swap the corresponding type for endianness. They
 * are kept here in the (possibly vain) hope that changes to the struct
 * will be reflected in the corresponding swap macro. For completeness
 * every type that might be transmitted across a network has a swap macro,
 * even if the macro is null. 
 */

/* credentials */
typedef char sts_cert_v7_t[STS_MAX_CERT];
#define SWAP_STS_CERT_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_cert_v7_t sts_cert_t;
#define SWAP_STS_CERT_T(name) SWAP_STS_CERT_V7_T(name)
#endif /* MASTER_SHARED_LIB */

/* image creation date */
typedef char sts_date_v7_t[STS_MAX_DATE];
#define SWAP_STS_DATE_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_date_v7_t sts_date_t;
#define SWAP_STS_DATE_T(name) SWAP_STS_DATE_V7_T(name)
#endif /* MASTER_SHARED_LIB */

/* exported file system mount */
typedef char sts_mount_v7_t[STS_MAX_MOUNTNAME];
#define SWAP_STS_MOUNT_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_mount_v7_t sts_mount_t;
#define SWAP_STS_MOUNT_T(name) SWAP_STS_MOUNT_V7_T(name)
#endif /* MASTER_SHARED_LIB */

typedef char sts_image_name_v7_t[STS_MAX_IMAGENAME];
#define SWAP_STS_IMAGE_NAME_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_image_name_v7_t sts_image_name_t;
#define SWAP_STS_IMAGE_NAME_T(name) SWAP_STS_IMAGE_NAME_V7_T(name)
#endif /* MASTER_SHARED_LIB */

typedef char sts_imageset_entry_v10_t[STS_MAX_ISNAME];
#define SWAP_STS_IMAGE_ISENTRY_V10_T(entry)
#ifndef MASTER_SHARED_LIB
typedef sts_imageset_entry_v10_t sts_imageset_entry_t;
#define SWAP_STS_IMAGE_ISENTRY_T(entry) SWAP_STS_IMAGE_ISENTRY_V10_T(entry)
#endif /* MASTER_SHARED_LIB */

typedef char sts_dpaid_v10_t[STS_MAX_DPAID];
#define SWAP_STS_DPAID_V10_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_dpaid_v10_t sts_dpaid_t;
#define SWAP_STS_DPAID_T(name) SWAP_STS_DPAID_V10_T(name)
#endif /* MASTER_SHARED_LIB */

typedef char sts_dpaver_v10_t[STS_MAX_DPAVER];
#define SWAP_STS_DPAVER_V10_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_dpaver_v10_t sts_dpaver_t;
#define SWAP_STS_DPAVER_T(name) SWAP_STS_DPAVER_V10_T(name)
#endif /* MASTER_SHARED_LIB */

typedef char sts_isinfo_v10_t[STS_MAX_ISINFO_LEN];
#define SWAP_STS_ISINFO_V10_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_isinfo_v10_t sts_isinfo_t;
#define SWAP_STS_ISINFO_T(name) SWAP_STS_ISINFO_V10_T(name)
#endif /* MASTER_SHARED_LIB */

/* storage server interface */
typedef char sts_interface_v7_t[STS_MAX_IFNAME];
#define SWAP_STS_INTERFACE_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_interface_v7_t sts_interface_t;
#define SWAP_STS_INTERFACE_T(name) SWAP_STS_INTERFACE_V7_T(name)
#endif /* MASTER_SHARED_LIB */

typedef	char sts_lsu_attn_msg_v7_t[STS_MAX_LSUATTNMSG];
#define SWAP_STS_LSU_ATTN_MSG_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_lsu_attn_msg_v7_t sts_lsu_attn_msg_t;
#define SWAP_STS_LSU_ATTN_MSG_T(name) SWAP_STS_LSU_ATTN_MSG_V7_T(name)
#endif /* MASTER_SHARED_LIB */

typedef char sts_lsu_description_v7_t[STS_MAX_LSUDESCRIPTION];
#define SWAP_STS_LSU_DESCRIPTION_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_lsu_description_v7_t sts_lsu_description_t;
#define SWAP_STS_LSU_DESCRIPTION_T(name) SWAP_STS_LSU_DESCRIPTION_V7_T(name)
#endif /* MASTER_SHARED_LIB */

typedef char sts_pgn_pfx_v7_t[STS_MAX_PFXLEN];
#define SWAP_STS_PGN_PFX_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_pgn_pfx_v7_t sts_pgn_pfx_t;
#define SWAP_STS_PGN_PFX_T(name) SWAP_STS_PGN_PFX_V7_T(name)
#endif /* MASTER_SHARED_LIB */

/* polite form of plugin prefix */
typedef char sts_pgn_pfx_label_v7_t[STS_MAX_PFX_LABEL];
#define SWAP_STS_PGN_PFX_LABEL_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_pgn_pfx_label_v7_t sts_pgn_pfx_label_t;
#define SWAP_STS_PGN_PFX_LABEL_T(name) SWAP_STS_PGN_PFX_LABEL_V7_T(name)
#endif /* MASTER_SHARED_LIB */

typedef char sts_pgn_name_v7_t[STS_MAX_PGN_NAME];
#define SWAP_STS_PGN_NAME_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_pgn_name_v7_t sts_pgn_name_t;
#define SWAP_STS_PGN_NAME_T(name) SWAP_STS_PGN_NAME_V7_T(name)
#endif /* MASTER_SHARED_LIB */

typedef char sts_policy_v7_t[STS_MAX_POLICY];
#define SWAP_STS_POLICY_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_policy_v7_t sts_policy_t;
#define SWAP_STS_POLICY_T(name) SWAP_STS_POLICY_V7_T(name)
#endif /* MASTER_SHARED_LIB */

typedef char sts_server_name_v7_t[STS_MAX_STSNAME];
#define SWAP_STS_SERVER_NAME_V7_T(name)
#ifndef MASTER_SHARED_LIB
typedef sts_server_name_v7_t sts_server_name_t;
#define SWAP_STS_SERVER_NAME_T(name) SWAP_STS_SERVER_NAME_V7_T(name)
#endif /* MASTER_SHARED_LIB */

#define STS_EVF_DELETE_ON_READ  0x1
#if STS_VERSION >= 9

/* part of LSU description */
typedef char sts_config_v9_t[STS_MAX_CONFIG];
#define SWAP_STS_CONFIG_V9_T(evpl)
#ifndef MASTER_SHARED_LIB
typedef sts_config_v9_t sts_config_t;
#define SWAP_STS_CONFIG_T(evpl) SWAP_STS_CONFIG_V9_T(evpl)
#endif /* MASTER_SHARED_LIB */

/* event payload */
typedef char sts_evpl_v9_t[STS_MAX_EVPL];
#define SWAP_STS_EVPL_V9_T(evpl)
#ifndef MASTER_SHARED_LIB
typedef sts_evpl_v9_t sts_evpl_t;
#define SWAP_STS_EVPL_T(evpl) SWAP_STS_EVPL_V9_T(evpl)
#endif /* MASTER_SHARED_LIB */

typedef char sts_lsu_label_v9_t[STS_MAX_LSU_LABEL];
#define SWAP_STS_LSU_LABEL_V9_T(label)
#ifndef MASTER_SHARED_LIB
typedef sts_lsu_label_v9_t sts_lsu_label_t;
#define SWAP_STS_LSU_LABEL_T(label) SWAP_STS_LSU_LABEL_V9_T(label)
#endif /* MASTER_SHARED_LIB */

#endif /* STS_VERSION >= 9 */

#if STS_VERSION >= 9

/*
 * Beginning in version 9, all STS-I handles are represented
 * by one type.
 */
typedef struct sts_handle_s *sts_handle_t;
typedef sts_handle_t sts_image_handle_t;
typedef sts_handle_t sts_image_list_handle_t;
typedef sts_handle_t sts_lsu_list_handle_t;
typedef sts_handle_t sts_server_handle_t;
typedef sts_handle_t sts_session_t;

#else /* STS_VERSION < 9 */

typedef struct sts_image_handle_s *sts_image_handle_t;

typedef struct sts_image_list_handle_s *sts_image_list_handle_t;

typedef struct sts_lsu_list_handle_s *sts_lsu_list_handle_t;

typedef struct sts_server_handle_s *sts_server_handle_t;

typedef struct sts_session_s *sts_session_t;

#endif /* STS_VERSION < 9 */

/* severity code passed to log handler */
typedef enum {
	/* DON'T CHANGE VALUES */
	STS_ESNONE	= 0,
	STS_ESINFO	= 1,
	STS_ESWARN	= 2,
	STS_ESDEBUG	= 3,
	STS_ESERROR	= 4
} sts_severity_v7_t;
#define SWAP_STS_SEVERITY_V7_T(sev) SWAP_STS_ENUM_T(sev)
#ifndef MASTER_SHARED_LIB
typedef sts_severity_v7_t sts_severity_t;
#define SWAP_STS_SEVERITY_T(sev) SWAP_STS_SEVERITY_V7_T(sev)
#endif /* MASTER_SHARED_LIB */

/* log handler specified by STS-I client - no internationalization */
typedef void (*sts_log_v7_t)(const char *session_id, const char *message,
	sts_severity_v7_t severity);
#ifndef MASTER_SHARED_LIB
typedef sts_log_v7_t sts_log_t;
#endif /* MASTER_SHARED_LIB */

/* session definition */
typedef struct {
	sts_server_name_v7_t sd_proxy;		/* STS-I proxy server */
	/* the following is a blatant NBUism and must be removed */
	int		  *sd_connectFailedPTR;	/* connect to bpcd failed */
	const char	  *sd_id;		/* session identifier */
	sts_log_v7_t	  sd_log;		/* log handler */
	sts_uint32_t	  sd_flags;
#define STS_SD_SRVLOGDEBUG	0x01
#define STS_SD_SRVLOGINFO	0x02
#define STS_SD_SRVLOGWARN	0x04
#define STS_SD_SRVLOGERROR	0x08
#define STS_SD_THROW		0x10
#define STS_SD_ALWAYS_USE_PROXY	0x20 /* even if plugin installed on local host */
#define STS_SD_RETURNCONNSTATUS	0x40 /* another NBUism - remove it */
#define STS_SD_NOBLOCK		0x80
#define	STS_SD_NOSGNBIN		0x100 /* don't check plugin signatures */
#define STS_SD_FLAGMAX      STS_SD_NOSGNBIN
} sts_session_def_v7_t;
#ifndef MASTER_SHARED_LIB
typedef sts_session_def_v7_t sts_session_def_t;
#endif /* MASTER_SHARED_LIB */

/* argument to sts_open_image_list() */
#define STS_FULL_ONLY	0x1
#define STS_INCR_ONLY	0x2
#define STS_LGCY_ONLY	0x4

/* mode flags for sts_open_image() */
#define STS_O_READ	0x1
#define STS_O_WRITE	0x2
#define STS_SERVER_CAP_STRING_MAX_LEN	256	/* max length for a sts service name */

#if STS_VERSION >= 8
/*
 * Image group types returned through sts_get_image_group()/
 * sts_get_image_group_byname(). A snap group comprises all images within
 * the same snapshot. A reuse group indicates that storage will be freed
 * if all images in the group are deleted.
 */
#define STS_IGT_SNAP	0x1	/* member of a snap group */
#define STS_IGT_REUSE	0x2	/* member of a reuse group */
#define STS_IGT_IOCTL   0x4
#endif

/*
 * MAKE SURE STS_EMAX = LARGEST ERROR CODE. When adding a code here, add an entry to
 * sts_messages[] in libsts.c.
 */

#define STS_EBASE		2060000 /* don't ask why  - it just is */
#define STS_EABS(code)		((code) - STS_EBASE)

/* the NetBackup I18N message id's should be moved elsewhere */

#define STS_EOK			0
#define STS_EOK_MSG             "SmeSTS1500N"
#  define STS_MOK		  "OK"
#define STS_EINVAL		(STS_EBASE+1)
#define STS_EINVAL_MSG          "SmeSTS1501N"
#  define STS_MINVAL		  "one or more invalid arguments"
#define STS_EMALLOC_MSG         "SmeSTS1502N"
#define STS_EMALLOC		(STS_EBASE+2)
#  define STS_MMALLOC		  "memory allocation"
#define STS_EASYNCOPS_MSG       "SmeSTS1503N"
#define STS_EASYNCOPS		(STS_EBASE+3)
#  define STS_MASYNCOPS		  "too many async reads/writes queued"
#define STS_EASYNCDATA_MSG      "SmeSTS1504N"
#define STS_EASYNCDATA		(STS_EBASE+4)
#  define STS_MASYNCDATA	  "too much async read/write data queued"
#define STS_EBUSY_MSG          "SmeSTS1505N"
#define STS_EBUSY		(STS_EBASE+5)
#  define STS_MBUSY		  "object is busy, cannot be closed"
#define STS_EEXIST_MSG          "SmeSTS1506N"
#define STS_EEXIST		(STS_EBASE+6)
#  define STS_MEXIST		  "object already exists"
#define STS_ECLIENTVERSION_MSG  "SmeSTS1507N"
#define STS_ECLIENTVERSION	(STS_EBASE+7)
#  define STS_MCLIENTVERSION	  "app version not <= master lib version"
#define STS_EPLUGINVERSION_MSG  "SmeSTS1508N"
#define STS_EPLUGINVERSION	(STS_EBASE+8)
#  define STS_MPLUGINVERSION	  "plug-in version not match master version"
#define STS_ECLAIM_MSG          "SmeSTS1509N"
#define STS_ECLAIM		(STS_EBASE+9)
#  define STS_MCLAIM		  "object not claimed by a plug-in"
#define STS_EINIT_MSG           "SmeSTS1510N"
#define STS_EINIT		(STS_EBASE+10)
#  define STS_MINIT		  "not initialized"
#define STS_EOFFSET_MSG         "SmeSTS1511N"
#define STS_EOFFSET		(STS_EBASE+11)
#  define STS_MOFFSET		  "offset invalid for object or context"
#define STS_EAGAIN_MSG          "SmeSTS1512N"
#define STS_EAGAIN		(STS_EBASE+12)
#  define STS_MAGAIN		  "call should be repeated"
#define STS_ENOENT_MSG          "SmeSTS1513N"
#define STS_ENOENT		(STS_EBASE+13)
#  define STS_MNOENT		  "no more entries"
#define STS_EABORT_MSG          "SmeSTS1514N"
#define STS_EABORT		(STS_EBASE+14)
#  define STS_MABORT		  "operation aborted"
#define STS_ESAVEAS_MSG         "SmeSTS1515N"
#define STS_ESAVEAS		(STS_EBASE+15)
#  define STS_MSAVEAS		  "invalid save as attributes of image"
#define STS_ENOTSUP_MSG         "SmeSTS1516N"
#define STS_ENOTSUP		(STS_EBASE+16)
#  define STS_MNOTSUP		  "operation not supported"
#define STS_ESYS_MSG		"SmeSTS1517N"
#define STS_ESYS		(STS_EBASE+17)
#  define STS_MSYS		  "system call failed"
#define STS_EFNOTFOUND_MSG	"SmeSTS1518N"
#define STS_EFNOTFOUND		(STS_EBASE+18)
#  define STS_MFNOTFOUND	  "file not found"
#define STS_ESOCKET_MSG         "SmeSTS1519N"
#define STS_ESOCKET		(STS_EBASE+19)
#  define STS_MSOCKET		  "error occurred on network socket"
#define STS_ECONNECT_MSG        "SmeSTS1520N"
#define STS_ECONNECT		(STS_EBASE+20)
#  define STS_MCONNECT		  "storage server connection limit exceeded"
#define STS_EPROTO_MSG          "SmeSTS1521N"
#define STS_EPROTO		(STS_EBASE+21)
#  define STS_MPROTO		  "network protocol error"
#define STS_EINTERNAL_MSG       "SmeSTS1522N"
#define STS_EINTERNAL		(STS_EBASE+22)
#  define STS_MINTERNAL		  "software error"
#define STS_ESHUTDOWN_MSG       "SmeSTS1523N"
#define STS_ESHUTDOWN		(STS_EBASE+23)
#  define STS_MSHUTDOWN		  "server is shut down"
#define STS_EINTERFACE_MSG      "SmeSTS1524N"
#define	STS_EINTERFACE		(STS_EBASE+24)
#  define STS_MINTERFACE	  "interface to storage server invalid"
#define STS_ECLIENT_MSG         "SmeSTS1525N"
#define STS_ECLIENT		(STS_EBASE+25)
#  define STS_MCLIENT		  "unauthorized client host"
#define STS_ESERVER_MSG         "SmeSTS1526N"
#define STS_ESERVER		(STS_EBASE+26)
#  define STS_MSERVER		  "invalid server name"
#define STS_ESTARTFILE_MSG      "SmeSTS1527N"
#define STS_ESTARTFILE		(STS_EBASE+27)
#  define STS_MSTARTFILE	  "start file invalid syntax"
#define STS_EDNS_MSG            "SmeSTS1528N"
#define STS_EDNS		(STS_EBASE+28)
#  define STS_MDNS		  "network Domain Name Service error"
#define STS_EAUTH_MSG           "SmeSTS1529N"
#define STS_EAUTH		(STS_EBASE+29)
#  define STS_MAUTH		  "authorization failure"
#define STS_EIMGOVERLAP_MSG     "SmeSTS1530N"
#define STS_EIMGOVERLAP		(STS_EBASE+30)
#  define STS_MIMGOVERLAP	  "attempted to overlap creation of related images"
#define STS_ENOSPC_MSG          "SmeSTS1531N"
#define STS_ENOSPC		(STS_EBASE+31)
#  define STS_MNOSPC		  "out of space"
#define STS_EREPEATSTAT_MSG     "SmeSTS1532N"
#define STS_EREPEATSTAT		(STS_EBASE+32)
#  define STS_MREPEATSTAT	  "call should be repeated with same stat block"
#define STS_ENOLICENSE_MSG      "SmeSTS1533N"
#define STS_ENOLICENSE		(STS_EBASE+33)
#  define STS_MNOLICENSE	  "SnapVault secondary is not licensed on NearStore"
#define STS_ENOTENABLED_MSG     "SmeSTS1534N"
#define STS_ENOTENABLED		(STS_EBASE+34)
#  define STS_MNOTENABLED	  "SnapVault is not turned on on NearStore"
#define STS_EACCESS_MSG         "SmeSTS1535N"
#define STS_EACCESS		(STS_EBASE+35)
#  define STS_MACCESS		  "Host is not in the SnapVault access list on NearStore"
#define STS_ESAVEAS_MISMATCH_MSG "SmeSTS1536N"
#define STS_ESAVEAS_MISMATCH	(STS_EBASE+36)
#  define STS_MSAVEAS_MISMATCH	  "save as specified does not match previous image in backup set"
#define STS_EPERM_MSG           "SmeSTS1537N"
#define STS_EPERM		(STS_EBASE+37)
#  define STS_MPERM		  "access not allowed"
#define STS_ENXIO_MSG           "SmeSTS1538N"
#define STS_ENXIO		(STS_EBASE+38)
#  define STS_MNXIO		  "no such device"
#define STS_EIMGSTATE_MSG       "SmeSTS1539N"
#define STS_EIMGSTATE		(STS_EBASE+39)
#  define STS_MIMGSTATE		  "illegal backup image state, incremental with out full"
#define STS_ESTSVERSION_MSG     "SmeSTS1540N"
#define STS_ESTSVERSION		(STS_EBASE+40)
#  define STS_MSTSVERSION	  "storage server version doesn't support operation"
#define STS_EIMAGECOMPLETE_MSG  "SmeSTS1541N"
#define STS_EIMAGECOMPLETE	(STS_EBASE+41)
#  define STS_MIMAGECOMPLETE	  "attempted open for write on a complete image"
#define STS_EFULLNOTFOUND_MSG   "SmeSTS1542N"
#define STS_EFULLNOTFOUND	(STS_EBASE+42)
#  define STS_MFULLNOTFOUND	  "incremental with out full"
#define STS_ENOEVENT_MSG        "SmeSTS1543N"
#define STS_ENOEVENT		(STS_EBASE+43)
#  define STS_MNOEVENT		  "no events available"
#define STS_EEFSCHK_MSG          "SmeSTS1544N"
#define STS_EEFSCHK		(STS_EBASE+44)
#  define STS_MEFSCHK		  "warning error for delete files not completing successfully"
#define STS_EPGNBUSY_MSG	 "SmeSTS1545N"
#define STS_EPGNBUSY		(STS_EBASE+45)
#  define STS_MPGNBUSY		  "plugin busy"
#define STS_EPLUGIN_MSG		"SmeSTS1546N"
#define STS_EPLUGIN		(STS_EBASE+46)
#  define STS_MPLUGIN		  "plugin error"
#define STS_ESERVERVERSION_MSG	"SmeSTS1547N"
#define STS_ESERVERVERSION	(STS_EBASE+47)
#  define STS_MSERVERVERSION	  "storage server/plugin version mismatch"
#define STS_ESYS_DLOPEN_MSG     "SmeSTS1628N"
#define STS_ESYS_DLOPEN		(STS_EBASE+48)
#  define STS_MSYS_DLOPEN	  "dlopen"
#define STS_ESYS_DLSYM_MSG      "SmeSTS1629N"
#define STS_ESYS_DLSYM		(STS_EBASE+49)
/* GetProcAddress is not in the i18n dictionary, a request to add it was made 2/1/2005
   dlsym (unix) or GetProcAddress (Windows) */
#  define  STS_MSYS_DLSYM        "dlsym"
#define STS_ESYS_PTHREAD_COND_INIT_MSG "SmeSTS1630N"
#define STS_ESYS_PTHREAD_COND_INIT (STS_EBASE+50)
#  define STS_MSYS_PTHREAD_COND_INIT "Thread initialize conditional"

#define STS_EBPDBM_MSG          "SmeSTS1631N"
#define STS_EBPDBM		(STS_EBASE+51)
#  define STS_MBPDBM		  "NetBackup database error"
#define STS_EBPDBM_CRITICAL_MSG "SmeSTS1632N"
#define STS_EBPDBM_CRITICAL	(STS_EBASE+52)
# define STS_MBPDBM_CRITICAL	  "NetBackup critical database error"

/* x86 equivalents */
#define STS_ESYS_LOADLIBRARY_MSG	"SmeSTS1633N"
#define STS_ESYS_LOADLIBRARY		(STS_EBASE+53)
#  define STS_MSYS_LOADLIBRARY		"LoadLibrary"
#define	STS_ESYS_GETPROCADRESS_MSG	"SmeSTS1634N"
#define STS_ESYS_GETPROCADDRESS 	(STS_EBASE+54)
#  define STS_MSYS_GETPROCADDRESS	"obtain the address of an exported function"
#define STS_ESYS_CREATEEVENT_MSG	"SmeSTS1635N"
#define STS_ESYS_CREATEEVENT		(STS_EBASE+55)
#  define STS_MSYS_CREATEEVENT		"CreateEvent"

#define STS_ESTH_MSG		"SmeSTS1553N"
#define STS_ESTH		(STS_EBASE+56)
#  define STS_MESTH		"Stream Handler error"
#define STS_EOSTPXYPI_MSG	"SmeSTS1554N"
#define STS_EOSTPXYPI		(STS_EBASE+57)
#  define STS_MEOSTPXYPI	"OpenStorage Proxy Plugin Error"
#define STS_EOSTPXYSRV_MSG	"SmeSTS1555N"
#define STS_EOSTPXYSRV		(STS_EBASE+58)
#  define STS_MEOSTPXYSRV	"OpenStorage Proxy Server Error"

/*define new error codes here and change STS_EMAX accordingly. It should be the largest error code.*/
#define	STS_EPGNCRASHED_MSG		"SmeSTS1636N"
#define	STS_EPGNCRASHED			(STS_EBASE+59)
#define STS_MPGNCRASHED			"plugin crashed and is no longer available within this process"

#define STS_EINVALID_STORAGE_MSG "SmeSTS1637N"
#define STS_EINVALID_STORAGE  (STS_EBASE+60)
#define STS_MINVALID_STORAGE    "Invalid storage"

#define STS_EMAX		(STS_EBASE+61) /* MUST BE LARGEST ERROR CODE */
#define STS_ECMNMAX		(STS_EBASE+1023)


typedef struct sts_err_rec {
        int error;
        const char* string;
} stserrrec, sts_msg_t, *stserrrec_ptr;

/*
 * stsi.h error codes
 */
static stserrrec sts_errtostring[] = {
/*  0 */                  { STS_EOK,                    STS_MOK},
/*  1 */                  { STS_EINVAL,                 STS_MINVAL},
/*  2 */                  { STS_EMALLOC,                STS_MMALLOC},
/*  3 */                  { STS_EASYNCOPS,              STS_MASYNCOPS},
/*  4 */                  { STS_EASYNCDATA,             STS_MASYNCDATA},
/*  5 */                  { STS_EBUSY,                  STS_MBUSY},
/*  6 */                  { STS_EEXIST,                 STS_MEXIST},
/*  7 */                  { STS_ECLIENTVERSION,         STS_MCLIENTVERSION},
/*  8 */                  { STS_EPLUGINVERSION,         STS_MPLUGINVERSION},
/*  9 */                  { STS_ECLAIM,                 STS_MCLAIM},
/* 10 */                  { STS_EINIT,                  STS_MINIT},
/* 11 */                  { STS_EOFFSET,                STS_MOFFSET},
/* 12 */                  { STS_EAGAIN,                 STS_MAGAIN},
/* 13 */                  { STS_ENOENT,                 STS_MNOENT},
/* 14 */                  { STS_EABORT,                 STS_MABORT},
/* 15 */                  { STS_ESAVEAS,                STS_MSAVEAS},
/* 16 */                  { STS_ENOTSUP,                STS_MNOTSUP},
/* 17 */                  { STS_ESYS,                   STS_MSYS},
/* 18 */                  { STS_EFNOTFOUND,             STS_MFNOTFOUND},
/* 19 */                  { STS_ESOCKET,                STS_MSOCKET},
/* 20 */                  { STS_ECONNECT,               STS_MCONNECT},
/* 21 */                  { STS_EPROTO,                 STS_MPROTO},
/* 22 */                  { STS_EINTERNAL,              STS_MINTERNAL},
/* 23 */                  { STS_ESHUTDOWN,              STS_MSHUTDOWN},
/* 24 */                  { STS_EINTERFACE,             STS_MINTERFACE},
/* 25 */                  { STS_ECLIENT,                STS_MCLIENT},
/* 26 */                  { STS_ESERVER,                STS_MSERVER},
/* 27 */                  { STS_ESTARTFILE,             STS_MSTARTFILE},
/* 28 */                  { STS_EDNS,                   STS_MDNS},
/* 29 */                  { STS_EAUTH,                  STS_MAUTH},
/* 30 */                  { STS_EIMGOVERLAP,            STS_MIMGOVERLAP},
/* 31 */                  { STS_ENOSPC,                 STS_MNOSPC},
/* 32 */                  { STS_EREPEATSTAT,            STS_MREPEATSTAT},
/* 33 */                  { STS_ENOLICENSE,             STS_MNOLICENSE},
/* 34 */                  { STS_ENOTENABLED,            STS_MNOTENABLED},
/* 35 */                  { STS_EACCESS,                STS_MACCESS},
/* 36 */                  { STS_ESAVEAS_MISMATCH,       STS_MSAVEAS_MISMATCH},
/* 37 */                  { STS_EPERM,                  STS_MPERM},
/* 38 */                  { STS_ENXIO,                  STS_MNXIO},
/* 39 */                  { STS_EIMGSTATE,              STS_MIMGSTATE},
/* 40 */                  { STS_ESTSVERSION,            STS_MSTSVERSION},
/* 41 */                  { STS_EIMAGECOMPLETE,         STS_MIMAGECOMPLETE},
/* 42 */                  { STS_EFULLNOTFOUND,          STS_MFULLNOTFOUND},
/* 43 */                  { STS_ENOEVENT,               STS_MNOEVENT},
/* 44 */                  { STS_EEFSCHK,                STS_MEFSCHK},
/* 45 */                  { STS_EPGNBUSY,               STS_MPGNBUSY},
/* 46 */                  { STS_EPLUGIN,                STS_MPLUGIN},
/* 47 */                  { STS_ESERVERVERSION,         STS_MSERVERVERSION},
/* 48 */                  { STS_ESYS_DLOPEN,            STS_MSYS_DLOPEN},
/* 49 */                  { STS_ESYS_DLSYM,             STS_MSYS_DLSYM},
/* 50 */                  { STS_ESYS_PTHREAD_COND_INIT, STS_MSYS_PTHREAD_COND_INIT},
/* 51 */                  { STS_EBPDBM,       		STS_MBPDBM},
/* 52 */                  { STS_EBPDBM_CRITICAL,    	STS_MBPDBM_CRITICAL},
/* 53 */                  { STS_ESYS_LOADLIBRARY,       STS_MSYS_LOADLIBRARY},
/* 54 */                  { STS_ESYS_GETPROCADDRESS,	STS_MSYS_GETPROCADDRESS},
/* 55 */                  { STS_ESYS_CREATEEVENT,       STS_MBPDBM_CRITICAL},
/* 56 */                  { STS_ESTH,                   STS_MESTH},
/* 57 */                  { STS_EOSTPXYPI,              STS_MEOSTPXYPI},
/* 58 */                  { STS_EOSTPXYSRV,             STS_MEOSTPXYSRV},
/* 59 */                  { STS_EPGNCRASHED,            STS_MPGNCRASHED},
/* 60 */                  { STS_EINVALID_STORAGE,       STS_MINVALID_STORAGE},
/* 61 */                  { STS_EMAX,                   "LARGEST ERROR CODE"},
/* 1023 */                { STS_ECMNMAX,                "LARGEST ERROR CODE"}
};

/*
 * Plugins can use STS_EPGNBASE as an offset for all error codes
 * to avoid conflicts with codes defined by the libs.
 */
#define STS_EPGNBASE    (STS_ECMNMAX + 1)

typedef struct {
	char sln_name[STS_MAX_LSUNAME];
} sts_lsu_name_v7_t;
#define SWAP_STS_LSU_NAME_V7_T(s)
#ifndef MASTER_SHARED_LIB
typedef sts_lsu_name_v7_t sts_lsu_name_t;
#define SWAP_STS_LSU_NAME_T(s) SWAP_STS_LSU_NAME_V7_T(s)
#endif /* MASTER_SHARED_LIB */

#if STS_VERSION >= 9
typedef struct {
	sts_handle_t		stl_server_handle;
	sts_lsu_name_v7_t	stl_lsu_name;
} sts_lsu_v9_t;
#endif

typedef struct {
	sts_server_handle_t	stl_server_handle;
	sts_lsu_name_v7_t	stl_lsu_name;
} sts_lsu_v7_t;

#ifndef MASTER_SHARED_LIB
#if STS_VERSION >= 9 && !STS_COMPAT
typedef sts_lsu_v9_t sts_lsu_t;
#else
typedef sts_lsu_v7_t sts_lsu_t;
#endif
#endif /* MASTER_SHARED_LIB */

/* argument to async services */
typedef struct {
	sts_image_handle_t	aio_image_handle;
	sts_uint64_t		aio_bytes_transferred;
	int			aio_error;
	void			*aio_private; /* client must not change */
} sts_aioresult_v7_t;

#if STS_VERSION >= 9
typedef struct {
	sts_handle_t	aio_image_handle;
	sts_uint64_t	aio_bytes_transferred;
	int		aio_error;
	void		*aio_priv1; /* client must not change */
	void		*aio_priv2; /* client must not change */
} sts_aioresult_v9_t;
#endif

#if STS_VERSION >= 11 
typedef struct {
	sts_uint64_t	aio_bytes_transferred; /* for read/write/copy operations */
	int 		aio_error;
} sts_aioresult_v11_t;
#endif 


#ifndef MASTER_SHARED_LIB
#if STS_VERSION >= 11
typedef sts_aioresult_v11_t sts_aioresult_t;
#elif STS_VERSION >= 9
typedef sts_aioresult_v9_t sts_aioresult_t;
#else
typedef sts_aioresult_v7_t sts_aioresult_t;
#endif
#endif /* MASTER_SHARED_LIB */

#if STS_VERSION >= 11
typedef struct sts_opid_s *sts_opid_t;
#define STS_MAX_OPNAME 256
typedef struct {
	char op_name[STS_MAX_OPNAME];
} sts_opname_v11_t;
#ifndef MASTER_SHARED_LIB
typedef sts_opname_v11_t sts_opname_t;
#endif /* MASTER_SHARED_LIB */
#endif



/************************************************************************
 * FOLLOWING STRUCTS MAY BE EMBEDDED IN RPC MESSAGES AND MUST BE	*
 * N*8 BYTES LONG. USE FILLER FIELDS TO PREVENT INSERTION OF HOLES	*
 * BY THE COMPILER. BE SURE THE SWAP MACRO FOR EACH STRUCT CORRECTLY	*
 * REFLECTS THE STRUCT CONTENTS.					*
 * EVENTUALLY THIS UGLY STUFF WILL DISAPPEAR. AMEN.			*
 ***********************************************************************/

/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES	*
*********************************************************/
typedef struct {
	sts_image_name_v7_t	img_basename;
	sts_date_v7_t		img_date;
	sts_date_v7_t		img_fulldate; /* deprecated */
	sts_policy_v7_t		img_policy;
	sts_uint32_t		img_saveas; /* an STS_SA_* value */
	sts_uint32_t		img_flags;
#define STS_IMG_DIRECT_IO	0x04	/* Use Direct I/O on the image create*/
					/* gets stripped out in plugin */
#define STS_IMG_LGCY		0x02	/* convert from legacy to basic disk */
#define STS_IMG_FULL		0x01	/* full backup - default is incremental */
#define STS_IMG_INCR		0x00	/* incremental backup */
	sts_stream_format_v7_t	img_format;
} sts_image_def_v7_t;

#define SWAP_STS_IMAGE_DEF_V7_T(img) { \
	SWAP_STS_IMAGE_NAME_V7_T(&(img)->img_basename); \
	SWAP_STS_DATE_V7_T(&(img)->img_date); \
	SWAP_STS_DATE_V7_T(&(img)->img_fulldate); \
	SWAP_STS_POLICY_V7_T(&(img)->img_policy); \
	SWAP_STS_UINT32_T(&(img)->img_saveas); \
	SWAP_STS_UINT32_T(&(img)->img_flags); \
	SWAP_STS_STREAM_FORMAT_V7_T(&(img)->img_format); \
}

#if STS_VERSION >= 10
typedef struct {
	sts_uint64_t		version; /* API version of this struct */
	sts_dpaid_v10_t		is_dpaid;   /* the data protection app id */
	sts_dpaver_v10_t	is_dpaver; /* the data protection app ver */
	sts_isinfo_v10_t	is_info;
} sts_isid_v10_t;

/* must use DPA specific SWAP macro to swap is_info field */
#define SWAP_STS_ISID_V10_T(isid) { \
	SWAP_STS_UINT64_T(&(isid)->version); \
	SWAP_STS_DPAID_V10_T(&(isid)->is_dpaid); \
	SWAP_STS_DPAVER_V10_T(&(isid)->is_dpaver); \
	SWAP_STS_ISINFO_V10_T(&(isid)->is_info); \
}

#ifndef MASTER_SHARED_LIB
#if STS_VERSION >= 10
typedef sts_isid_v10_t sts_isid_t;
#define SWAP_STS_ISID_T(img) SWAP_STS_ISID_V10_T(img)
#endif	/* STS_VERSION >= 10 */
#endif /* MASTER_SHARED_LIB */


/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES	*
*********************************************************/
typedef struct {
	sts_uint64_t		version; /* API version of this struct */
	sts_image_name_v7_t	img_basename;
	sts_date_v7_t		img_date;
	sts_date_v7_t		img_fulldate; /* deprecated */
	sts_policy_v7_t		img_policy;
	sts_uint32_t		img_saveas; /* an STS_SA_* value */
	sts_uint32_t		img_flags;
#define STS_SRC_DYNAMIC   0x10  /* the client is a dynamicdata source */
#define STS_SRC_STATIC    0x08  /* the client is a static data source */
#define STS_IMG_DIRECT_IO 0x04  /* Use Direct I/O on the image create */
                                /* gets stripped out in plugin */
#define STS_IMG_LGCY      0x02  /* convert from legacy to basic disk */
#define STS_IMG_FULL      0x01  /* full backup - default is incremental */
#define STS_IMG_INCR      0x00  /* incremental backup */
	sts_stream_format_v7_t	img_format;
	sts_uint32_t		img_filler;
	sts_isid_v10_t		img_isid;
 } sts_image_def_v10_t;

#define SWAP_STS_IMAGE_DEF_V10_T(img) { \
	SWAP_STS_UINT64_T(&(img)->version); \
	SWAP_STS_IMAGE_NAME_V7_T(&(img)->img_basename); \
	SWAP_STS_DATE_V7_T(&(img)->img_date); \
	SWAP_STS_DATE_V7_T(&(img)->img_fulldate); \
	SWAP_STS_POLICY_V7_T(&(img)->img_policy); \
	SWAP_STS_UINT32_T(&(img)->img_saveas); \
	SWAP_STS_UINT32_T(&(img)->img_flags); \
	SWAP_STS_STREAM_FORMAT_V7_T(&(img)->img_format); \
	SWAP_STS_UINT32_T(&(img)->img_filler); \
	SWAP_STS_ISID_V10_T(&(img)->img_isid); \
}

#endif /* STS_VERSION >= 10 */

#ifndef MASTER_SHARED_LIB
#if STS_VERSION >= 10
typedef sts_image_def_v10_t sts_image_def_t;
#define SWAP_STS_IMAGE_DEF_T(img) SWAP_STS_IMAGE_DEF_V10_T(img)
#else
typedef sts_image_def_v7_t sts_image_def_t;
#define SWAP_STS_IMAGE_DEF_T(img) SWAP_STS_IMAGE_DEF_V7_T(img)
#endif	/* STS_VERSION >= 9 */
#endif /* MASTER_SHARED_LIB */

/* exported file system type - not relevant to external vendors */
typedef sts_uint32_t sts_exfs_v7_t;
#define SWAP_STS_EXFS_V7_T(fs) SWAP_STS_UINT32_T(fs)
#ifndef MASTER_SHARED_LIB
typedef sts_exfs_v7_t sts_exfs_t;
#define SWAP_STS_EXFS_T(fs) SWAP_STS_EXFS_V7_T(fs)
#endif /* MASTER_SHARED_LIB */

#define STS_EXFS_CIFS	1
#define STS_EXFS_NFS	2

/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES	*
*********************************************************/
/*
 * Described exported file system that accompanies a backup image.
 * Not relevant to external vendors.
 */
typedef struct {
	sts_exfs_v7_t	ex_fstype;
	sts_uint32_t	ex_filler;
	sts_mount_v7_t	ex_mount;
} sts_export_v7_t;

#define SWAP_STS_EXPORT_V7_T(ex) { \
	SWAP_STS_EXFS_V7_T(&(ex)->ex_fstype); \
	SWAP_STS_MOUNT_V7_T(&(ex)->ex_mount); \
}
#ifndef MASTER_SHARED_LIB
typedef sts_export_v7_t sts_export_t;
#define SWAP_STS_EXPORT_T(ex) SWAP_STS_EXPORT_V7_T(ex)
#endif /* MASTER_SHARED_LIB */

/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES	*
*********************************************************/
typedef struct {
	sts_image_def_v7_t	imo_def;
	sts_server_name_v7_t	imo_server;
	sts_lsu_name_v7_t	imo_lsu;
	sts_uint64_t		imo_size;	/* image size in bytes */
	sts_uint64_t		imo_block_size; /* preferred block size for read/write */
#define STS_MAXEXPORT 4
	sts_export_v7_t		imo_export[STS_MAXEXPORT];
	sts_uint32_t		imo_status;
#define STS_II_IMAGE_PENDING	0x1	/* image being created */
#define STS_II_FILES_PENDING	0x2	/* files being created */
#define STS_II_IMAGE_CREATED	0x4	/* image creation complete */
#define STS_II_FILES_CREATED	0x8	/* file creation complete */
#define STS_II_BUSY_READ	0x10	/* one or more open read handles */	
#define STS_II_BUSY_WRITE	0x20	/* one or more open write handles */
	sts_uint32_t	imo_filler;
} sts_image_info_v7_t;

#define SWAP_STS_IMAGE_INFO_V7_T(imo) { \
	SWAP_STS_IMAGE_DEF_V7_T(&(imo)->imo_def); \
	SWAP_STS_SERVER_NAME_V7_T(&(imo)->imo_server); \
	SWAP_STS_LSU_NAME_V7_T(&(imo)->imo_lsu); \
	SWAP_STS_UINT64_T(&(imo)->imo_size); \
	SWAP_STS_UINT64_T(&(imo)->imo_block_size); \
	{ int i; for (i=0; i<STS_MAXEXPORT; i++) \
		SWAP_STS_EXPORT_V7_T(&(imo)->imo_export[i]); } \
	SWAP_STS_UINT32_T(&(imo)->imo_status); \
}


#if STS_VERSION >= 10
/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES	*
*********************************************************/
typedef struct {
	sts_uint64_t 		version; 	/* API version of this struct */
	sts_image_def_v10_t	imo_def;
	sts_server_name_v7_t	imo_server;
	sts_lsu_name_v7_t	imo_lsu;
	sts_uint64_t		imo_size;	/* image size in bytes */
	sts_uint64_t		imo_block_size; /* preferred block size for read/write */
#define STS_MAXEXPORT 4
	sts_export_v7_t		imo_export[STS_MAXEXPORT];
	sts_uint32_t		imo_status;
#define STS_II_IMAGE_PENDING	0x1	/* image being created */
#define STS_II_FILES_PENDING	0x2	/* files being created */
#define STS_II_IMAGE_CREATED	0x4	/* image creation complete */
#define STS_II_FILES_CREATED	0x8	/* file creation complete */
#define STS_II_BUSY_READ	0x10	/* one or more open read handles */	
#define STS_II_BUSY_WRITE	0x20	/* one or more open write handles */
	sts_uint32_t		imo_filler;
} sts_image_info_v10_t;

#define SWAP_STS_IMAGE_INFO_V10_T(imo) { \
	SWAP_STS_UINT64_T(&(imo)->version); \
	SWAP_STS_IMAGE_DEF_V10_T(&(imo)->imo_def); \
	SWAP_STS_SERVER_NAME_V7_T(&(imo)->imo_server); \
	SWAP_STS_LSU_NAME_V7_T(&(imo)->imo_lsu); \
	SWAP_STS_UINT64_T(&(imo)->imo_size); \
	SWAP_STS_UINT64_T(&(imo)->imo_block_size); \
	{ int i; for (i=0; i<STS_MAXEXPORT; i++) \
		SWAP_STS_EXPORT_V7_T(&(imo)->imo_export[i]); } \
	SWAP_STS_UINT32_T(&(imo)->imo_status); \
}

#endif /* STS_VERSION >= 10 */

#ifndef MASTER_SHARED_LIB
#if STS_VERSION >= 10
typedef sts_image_info_v10_t sts_image_info_t;
#define SWAP_STS_IMAGE_INFO_T(imo) SWAP_STS_IMAGE_INFO_V10_T(imo)
#else
typedef sts_image_info_v7_t sts_image_info_t;
#define SWAP_STS_IMAGE_INFO_T(imo) SWAP_STS_IMAGE_INFO_V7_T(imo)
#endif /* STS_VERSION >= 10 */
#endif /* MASTER_SHARED_LIB */

	
/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES	*
*********************************************************/
/* attention message from LSU */
typedef struct {
	sts_severity_v7_t	sla_severity;
	sts_uint32_t		sla_filler;
	sts_lsu_attn_msg_v7_t	sla_msg;
} sts_lsu_attn_v7_t;

#define SWAP_STS_LSU_ATTN_V7_T(sla) { \
	SWAP_STS_SEVERITY_V7_T(&(sla)->sla_severity); \
	SWAP_STS_LSU_ATTN_MSG_V7_T(&(sla)->sla_msg); \
}
#ifndef MASTER_SHARED_LIB
typedef sts_lsu_attn_v7_t sts_lsu_attn_t;
#define SWAP_STS_LSU_ATTN_T(sla) SWAP_STS_LSU_ATTN_V7_T(sla)
#endif /* MASTER_SHARED_LIB */

	
/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES	*
*********************************************************/
typedef struct {
	sts_server_name_v7_t	lsu_server;
	sts_lsu_name_v7_t	lsu_name;
	sts_lsu_description_v7_t	lsu_description;
	sts_lsu_attn_v7_t		lsu_attn;	/* severity is STS_ESNONE if ok */
	sts_uint64_t		lsu_capacity;	/* bytes */
	sts_uint64_t		lsu_used;	/* bytes */
	sts_uint64_t		lsu_max_transfer; /* for read/write, bytes */
	sts_uint64_t		lsu_block_size;	/* preferred for read/write */
	sts_uint64_t		lsu_images;	/* currently resident on LSU */
	sts_uint32_t		lsu_saveas;	/* saveas - see sts_image_name_t */
	sts_uint32_t		lsu_media;
#define STS_LSU_MEDIUM_DISK	0x1
#define STS_LSU_MEDIUM_TAPE	0x2
} sts_lsu_info_v7_t; 

#define SWAP_STS_LSU_INFO_V7_T(lsu) { \
	SWAP_STS_SERVER_NAME_V7_T(&(lsu)->lsu_server); \
	SWAP_STS_LSU_NAME_V7_T(&(lsu)->lsu_name); \
	SWAP_STS_LSU_DESCRIPTION_V7_T(&(lsu)->lsu_description); \
	SWAP_STS_LSU_ATTN_V7_T(&(lsu)->lsu_attn); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_capacity); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_used); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_max_transfer); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_block_size); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_images); \
	SWAP_STS_UINT32_T(&(lsu)->lsu_saveas); \
	SWAP_STS_UINT32_T(&(lsu)->lsu_media); \
}

#if STS_VERSION >= 8

typedef struct {
	sts_server_name_v7_t	lsu_server;
	sts_lsu_name_v7_t	lsu_name;
	sts_lsu_description_v7_t	lsu_description;
	sts_lsu_attn_v7_t		lsu_attn;	/* severity is STS_ESNONE if ok */
	sts_uint64_t		lsu_capacity;	/* bytes */
	sts_uint64_t		lsu_used;	/* bytes */
	sts_uint64_t		lsu_max_transfer; /* for read/write, bytes */
	sts_uint64_t		lsu_block_size;	/* preferred for read/write, bytes */
	sts_uint64_t		lsu_images;	/* currently resident on LSU */
	sts_uint32_t		lsu_saveas;	/* saveas - see sts_image_name_t */
	sts_uint32_t		lsu_flags;
#define STS_LSUF_DISK		0x1	/* disk medium */
#define STS_LSUF_TAPE		0x2	/* tape medium */
#define STS_LSUF_GROUP_SNAP	0x10	/* images may be in snap group */
#define STS_LSUF_GROUP_REUSE	0x20	/* images may be in reuse group */
} sts_lsu_info_v8_t; 

#define SWAP_STS_LSU_INFO_V8_T(lsu) { \
	SWAP_STS_SERVER_NAME_V7_T(&(lsu)->lsu_server); \
	SWAP_STS_LSU_NAME_V7_T(&(lsu)->lsu_name); \
	SWAP_STS_LSU_DESCRIPTION_V7_T(&(lsu)->lsu_description); \
	SWAP_STS_LSU_ATTN_V7_T(&(lsu)->lsu_attn); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_capacity); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_used); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_max_transfer); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_block_size); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_images); \
	SWAP_STS_UINT32_T(&(lsu)->lsu_saveas); \
	SWAP_STS_UINT32_T(&(lsu)->lsu_flags); \
}

#endif /* STS_VERSION >= 8 */

typedef sts_uint32_t sts_lsu_alloc_v7_t;
#define STS_LSU_ALLOC_STAT	1
#define STS_LSU_ALLOC_DYN	2
#define SWAP_STS_LSU_ALLOC_V7_T(a) SWAP_STS_UINT32_T(a)

#if STS_VERSION >= 9

/* external vendors must be STS_LSU_AT_STATIC */
typedef enum {
	STS_LSU_AT_NONE		= 0,
	STS_LSU_AT_STATIC	= 1,
	STS_LSU_AT_DYNAMIC	= 2,
	STS_LSU_AT_POOL		= 3
} sts_lsu_alloc_v9_t;

#define SWAP_STS_LSU_ALLOC_V9_T(a) SWAP_STS_ENUM_T(a)
#endif /* STS_VERSION >= 9 */

#ifndef MASTER_SHARED_LIB
#if STS_VERSION >=9 && !STS_COMPAT
typedef sts_lsu_alloc_v9_t sts_lsu_alloc_t;
#define SWAP_STS_LSU_ALLOC_T(a) SWAP_STS_LSU_ALLOC_V9_T(a)
#else
typedef sts_lsu_alloc_v7_t sts_lsu_alloc_t;
#define SWAP_STS_LSU_ALLOC_T(a) SWAP_STS_LSU_ALLOC_V7_T(a)
#endif
#endif /* MASTER_SHARED_LIB */

#if STS_VERSION >= 9
/* LSU storage type - not relevant to external vendors */
typedef enum {
	STS_LSU_ST_NONE		= 0,
	STS_LSU_ST_FILE		= 1,
	STS_LSU_ST_BLOCK	= 2
} sts_lsu_storage_v9_t;

#define SWAP_STS_LSU_STORAGE_V9_T(s) SWAP_STS_ENUM_T(s)
#ifndef MASTER_SHARED_LIB
typedef sts_lsu_storage_v9_t sts_lsu_storage_t;
#define SWAP_STS_LSU_STORAGE_T(s) SWAP_STS_LSU_STORAGE_V9_T(s)
#endif /* MASTER_SHARED_LIB */
#endif /* STS_VERSION >= 9 */

#if STS_VERSION >= 9
/* LSU definition */
typedef struct {
	sts_lsu_name_v7_t	sld_name;
	sts_lsu_alloc_v9_t	sld_alloc;
	sts_lsu_storage_v9_t	sld_storage;
	sts_lsu_description_v7_t	sld_description;
	sts_config_v9_t		sld_config;
	sts_uint32_t		sld_flags;
#define STS_LSUF_DISK		0x1	/* disk medium */
#define STS_LSUF_TAPE		0x2	/* tape medium */
#define STS_LSUF_GROUP_SNAP	0x10	/* images may be in snap group */
#define STS_LSUF_GROUP_REUSE	0x20	/* images may be in reuse group */
#define STS_LSUF_ACTIVE		0x40	/* currently active */
#define STS_LSUF_STORAGE_FREED	   0x80  /* space management always done */
#define STS_LSUF_STORAGE_NOT_FREED 0x100 /* space management never done */
#define STS_LSUF_STORAGE_UNDEFINED 0x200 /* space management undefined */
#define STS_LSUF_SIS		0x400	/* implements SIS and values are reported */
#define STS_LSUF_SIS_UNDEF	0x800	/* implements SIS, values are not reported */
#define STS_LSUF_DOWN		0x1000	/* LSU is inoperative */
	sts_uint32_t		sld_saveas; /* STS_SA_* */
	sts_uint64_t		sld_max_transfer; /* for read/write, bytes */
	sts_uint64_t		sld_block_size; /* preferred for read/write, bytes */
	sts_uint64_t		sld_alloc_size; /* allocation block size, bytes */
	sts_lsu_label_v9_t	sld_label; /* application-defined */
} sts_lsu_def_v9_t;
#define SWAP_STS_LSU_DEF_V9_T(sld) { \
	SWAP_STS_LSU_NAME_V7_T(&(sld)->sld_name); \
	SWAP_STS_LSU_ALLOC_V9_T(&(sld)->sld_alloc); \
	SWAP_STS_LSU_STORAGE_V9_T(&(sld)->sld_storage); \
	SWAP_STS_LSU_DESCRIPTION_V7_T(&(sld)->sld_description); \
	SWAP_STS_CONFIG_V9_T(&(sld)->sld_config); \
	SWAP_STS_UINT32_T(&(sld)->sld_flags); \
	SWAP_STS_UINT32_T(&(sld)->sld_saveas); \
	SWAP_STS_UINT64_T(&(sld)->sld_max_transfer); \
	SWAP_STS_UINT64_T(&(sld)->sld_block_size); \
	SWAP_STS_UINT64_T(&(sld)->sld_alloc_size); \
	SWAP_STS_LSU_LABEL_V9_T(&(sld)->sld_label); \
}
#ifndef MASTER_SHARED_LIB
#if STS_VERSION >= 9 && STS_VERSION <=10
typedef sts_lsu_def_v9_t sts_lsu_def_t;
#define SWAP_STS_LSU_DEF_T(sld) SWAP_STS_LSU_DEF_V9_T(sld)
#endif
#endif /* MASTER_SHARED_LIB */
#endif /* STS_VERSION >= 9 */

#if STS_VERSION >= 11
/* LSU definition */
typedef struct {
	sts_uint64_t		version; /* API version of this struct */
	sts_lsu_name_v7_t	sld_name;
	sts_lsu_alloc_v9_t	sld_alloc;
	sts_lsu_storage_v9_t	sld_storage;
	sts_lsu_description_v7_t	sld_description;
	sts_config_v9_t		sld_config;
	sts_uint32_t		sld_flags;
#define STS_LSUF_DISK		0x1	/* disk medium */
#define STS_LSUF_TAPE		0x2	/* tape medium */
#define STS_LSUF_GROUP_SNAP	0x10	/* images may be in snap group */
#define STS_LSUF_GROUP_REUSE	0x20	/* images may be in reuse group */
#define STS_LSUF_ACTIVE		0x40	/* currently active */
#define STS_LSUF_STORAGE_FREED	   0x80  /* space management always done */
#define STS_LSUF_STORAGE_NOT_FREED 0x100 /* space management never done */
#define STS_LSUF_STORAGE_UNDEFINED 0x200 /* space management undefined */
#define STS_LSUF_SIS		0x400	/* implements SIS and values are reported */
#define STS_LSUF_SIS_UNDEF	0x800	/* implements SIS, values are not reported */
#define STS_LSUF_DOWN		0x1000	/* LSU is inoperative */
#define STS_LSUF_REP_ENABLED	0x2000	/* replication is enabled */
#define STS_LSUF_REP_TARGET		0x4000 /* this is a replicant of another LSU */
#define STS_LSUF_REP_SOURCE		0x8000 /* this LSU is replicated to other LSU(s) */
	sts_uint32_t		sld_saveas; /* STS_SA_* */
	sts_uint64_t		sld_max_transfer; /* for read/write, bytes */
	sts_uint64_t		sld_block_size; /* preferred for read/write, bytes */
	sts_uint64_t		sld_alloc_size; /* allocation block size, bytes */
	sts_lsu_label_v9_t	sld_label; /* application-defined */
	sts_uint32_t sld_rep_sources; /* number of LSU's that replicate to this LSU */
	sts_uint32_t sld_rep_targets; /* number of replication targets for this LSU */
} sts_lsu_def_v11_t;
#define SWAP_STS_LSU_DEF_V11_T(sld) { \
	SWAP_STS_UINT64_T(&(sld)->version); \
	SWAP_STS_LSU_NAME_V7_T(&(sld)->sld_name); \
	SWAP_STS_LSU_ALLOC_V9_T(&(sld)->sld_alloc); \
	SWAP_STS_LSU_STORAGE_V9_T(&(sld)->sld_storage); \
	SWAP_STS_LSU_DESCRIPTION_V7_T(&(sld)->sld_description); \
	SWAP_STS_CONFIG_V9_T(&(sld)->sld_config); \
	SWAP_STS_UINT32_T(&(sld)->sld_flags); \
	SWAP_STS_UINT32_T(&(sld)->sld_saveas); \
	SWAP_STS_UINT64_T(&(sld)->sld_max_transfer); \
	SWAP_STS_UINT64_T(&(sld)->sld_block_size); \
	SWAP_STS_UINT64_T(&(sld)->sld_alloc_size); \
	SWAP_STS_LSU_LABEL_V9_T(&(sld)->sld_label); \
	SWAP_STS_UINT32_T(&(sld)->sld_rep_sources); \
	SWAP_STS_UINT32_T(&(sld)->sld_rep_targets); \
}
#ifndef MASTER_SHARED_LIB
typedef sts_lsu_def_v11_t sts_lsu_def_t;
#define SWAP_STS_LSU_DEF_T(sld) SWAP_STS_LSU_DEF_V11_T(sld)
#endif /* MASTER_SHARED_LIB */
#endif /* STS_VERSION >= 11 */


#if STS_VERSION >= 9
/*
 * For an LSU that implements SIS and STS_LSUF_SIS is set, lsu_used is the amount
 * of data written before de-duping; lsu_capacity is the sum of lsu_used and remaining
 * physical capacity. If STS_LSUF_SIS_UNDEF is set, lsu_used_phys should be 0, and
 * lsu_capacity should always match lsu_capacity_phys. In all situations,
 * (lsu_capacity - lsu_used) bytes can be written without LSU overflow.
 */
typedef struct {
	sts_server_name_v7_t	lsu_server;
	sts_lsu_def_v9_t	lsu_def;
	sts_uint64_t		lsu_capacity;	/* bytes */
	sts_uint64_t		lsu_capacity_phys; /* physical capacity, in bytes */
	sts_uint64_t		lsu_used;	/* bytes */
	sts_uint64_t		lsu_used_phys;	/* bytes (actually occupied) */
	sts_uint64_t		lsu_images;	/* currently resident on LSU */
} sts_lsu_info_v9_t; 

#define SWAP_STS_LSU_INFO_V9_T(lsu) { \
	SWAP_STS_SERVER_NAME_V7_T(&(lsu)->lsu_server); \
	SWAP_STS_LSU_DEF_V9_T(&(lsu)->lsu_def); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_capacity); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_capacity_phys); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_used); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_used_phys); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_images); \
}

#endif /* STS_VERSION >= 9 */

#if STS_VERSION >= 11 
typedef struct {
	sts_server_name_v7_t	ls_server;
	sts_lsu_name_v7_t		ls_lsu;
} sts_lsu_spec_v11_t;

#define SWAP_STS_LSU_SPEC_V11_T(ls) { \
	SWAP_STS_SERVER_NAME_V7_T(&(ls)->ls_server); \
	SWAP_STS_LSU_NAME_V7_T(&(ls)->ls_lsu); \
}
#ifndef MASTER_SHARED_LIB
typedef sts_lsu_spec_v11_t sts_lsu_spec_t;
#define SWAP_STS_LSU_SPEC_T(ls) SWAP_STS_LSU_SPEC_V11_T(ls)
#endif
#endif 

#if STS_VERSION >= 11 
/*
 * For an LSU that implements SIS and STS_LSUF_SIS is set, lsu_used is the amount
 * of data written before de-duping; lsu_capacity is the sum of lsu_used and remaining
 * physical capacity. If STS_LSUF_SIS_UNDEF is set, lsu_used_phys should be 0, and
 * lsu_capacity should always match lsu_capacity_phys. In all situations,
 * (lsu_capacity - lsu_used) bytes can be written without LSU overflow.
 */
typedef struct {
	sts_uint64_t		version; /* API version of this struct */
	sts_server_name_v7_t	lsu_server;
	sts_lsu_def_v11_t	lsu_def;
	sts_uint64_t		lsu_capacity;	/* bytes */
	sts_uint64_t		lsu_capacity_phys; /* physical capacity, in bytes */
	sts_uint64_t		lsu_used;	/* bytes */
	sts_uint64_t		lsu_used_phys;	/* bytes (actually occupied) */
	sts_uint64_t		lsu_images;	/* currently resident on LSU */
} sts_lsu_info_v11_t; 

#define SWAP_STS_LSU_INFO_V11_T(lsu) { \
	SWAP_STS_UINT64_T(&(lsu)->version); \
	SWAP_STS_SERVER_NAME_V7_T(&(lsu)->lsu_server); \
	SWAP_STS_LSU_DEF_V11_T(&(lsu)->lsu_def); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_capacity); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_capacity_phys); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_used); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_used_phys); \
	SWAP_STS_UINT64_T(&(lsu)->lsu_images); \
}

#endif /* STS_VERSION >= 11 */


#ifndef MASTER_SHARED_LIB
#if STS_VERSION >= 11 && !STS_COMPAT
typedef sts_lsu_info_v11_t sts_lsu_info_t;
#define SWAP_STS_LSU_INFO_T(lsu) SWAP_STS_LSU_INFO_V11_T(lsu)
#elif STS_VERSION >= 9 && !STS_COMPAT
typedef sts_lsu_info_v9_t sts_lsu_info_t;
#define SWAP_STS_LSU_INFO_T(lsu) SWAP_STS_LSU_INFO_V9_T(lsu)
#elif STS_VERSION >= 8 && !STS_COMPAT
typedef sts_lsu_info_v8_t sts_lsu_info_t;
#define SWAP_STS_LSU_INFO_T(lsu) SWAP_STS_LSU_INFO_V8_T(lsu)
#else
typedef sts_lsu_info_v7_t sts_lsu_info_t;
#define SWAP_STS_LSU_INFO_T(lsu) SWAP_STS_LSU_INFO_V7_T(lsu)
#endif
#endif /* MASTER_SHARED_LIB */


/*
 * Returned by sts_get_server_prop(). An STS name must begin with
 * the appropriate prefix, the remainder of the name is determined
 * by the STS vendor. The prefix allows the name to be claimed by
 * the plugin claim function.
 */

typedef enum {
	STS_CRED_NONE	= 0, /* deprecated */
	STS_CRED_CLEAR	= STS_CRED_NONE,
	STS_CRED_MD5	= 1
} sts_cred_type_v7_t;
#define SWAP_STS_CRED_TYPE_V7_T(c) SWAP_STS_ENUM_T(c)
#ifndef MASTER_SHARED_LIB
typedef sts_cred_type_v7_t sts_cred_type_t;
#define SWAP_STS_CRED_TYPE_T(c) SWAP_STS_CRED_TYPE_V7_T(c)
#endif /* MASTER_SHARED_LIB */


#define STS_PFX_SEP ':' /* separates plugin prefix from server name */

/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES	*
*********************************************************/
typedef struct {
	sts_server_name_v7_t	srv_server;
#define STS_PFX_BASIC_DISK	"STSBasicDisk:" /* replaces legacy disk */
#define STS_PFX_ADVANCED_DISK	"AdvancedDisk:"	/* OST basic disk*/
#define STS_PFX_NTAP		"ntap:"		/* Network Appliance */
#define STS_PFX_SANDISK		"sandisk:"	/* SANDisk */
#define	STS_PFX_SHAREDDISK	"SharedDisk:"	/* SharedDisk (VxFI) */
#define STS_PFX_PD		"retired_PureDisk:"	/* PureDisk retired*/
	sts_stream_format_v7_t	srv_sth[STS_MAX_STH]; /* supported stream formats */
	sts_uint32_t		srv_flags;
#define STS_SRV_ASYNC		0x1	/* async service group supported */
#define STS_SRV_IMAGELIST	0x2	/* image list capability */
#define STS_SRV_CRED		0x4	/* credentials required */
#define STS_SRV_CONRW		0x8	/* concurrent read/write capability */
#if STS_VERSION >= 11
#define STS_SRV_STORED_EV	0x800 /* supports stored events */
#endif /* STS_VERSION >= 11 */
				/* upper 16 bits reserved */
	sts_uint32_t		srv_media; /* same values as in sts_lsu_info_v7_t */
	sts_uint32_t		srv_maxconnect; /* max concurrent connections */
	sts_uint32_t		srv_nconnect; /* open connections */
	sts_interface_v7_t	srv_interface[STS_MAX_IF];
#define STS_MAX_CRED		8
	sts_cred_type_v7_t	srv_cred[STS_MAX_CRED];
} sts_server_info_v7_t;

#define SWAP_STS_SERVER_INFO_V7_T(srv) { \
	SWAP_STS_SERVER_NAME_V7_T(&(srv)->srv_server); \
	{ int i; for (i=0; i<STS_MAX_STH; i++) \
		SWAP_STS_STREAM_FORMAT_V7_T(&(srv)->srv_sth[i]); } \
	SWAP_STS_UINT32_T(&(srv)->srv_flags); \
	SWAP_STS_UINT32_T(&(srv)->srv_media); \
	SWAP_STS_UINT32_T(&(srv)->srv_maxconnect); \
	SWAP_STS_UINT32_T(&(srv)->srv_nconnect); \
	{ int i; for (i=0; i<STS_MAX_IF; i++) \
		SWAP_STS_INTERFACE_V7_T(&(srv)->srv_interface[i]); } \
	{ int i; for (i=0; i<STS_MAX_CRED; i++) \
		SWAP_STS_CRED_TYPE_V7_T(&(srv)->srv_cred[i]); } \
}

#if STS_VERSION >= 8

typedef sts_uint32_t sts_srv_cap_v8_t;
#define SWAP_STS_SRV_CAP_V8_T(cap) SWAP_STS_UINT32_T(cap)
#ifndef MASTER_SHARED_LIB
typedef sts_srv_cap_v8_t sts_srv_cap_t;
#define SWAP_STS_SRV_CAP_T(cap) SWAP_STS_SRV_CAP_V8_T(cap)
#endif /* MASTER_SHARED_LIB */

/* TODO - WDD - fix RemoteDiskServer and remove */
#define STS_BITSPCW (sizeof(sts_srv_cap_t) * 8) /* bits per sts_srv_cap_t */
#if 0
#define STS_BITSPCW(srv_cap) (sizeof(srv_cap) * 8) /* bits per sts_srv_cap_t */
#endif

typedef struct {
	sts_server_name_v7_t	srv_server;
#define STS_PFX_BASIC_DISK	"STSBasicDisk:"	/* replaces legacy disk */
#define STS_PFX_ADVANCED_DISK	"AdvancedDisk:"	/* OST basic disk*/
#define STS_PFX_NTAP		"ntap:"		/* Network Appliance */
#define STS_PFX_SANDISK		"sandisk:"	/* SANDisk */
#define	STS_PFX_SHAREDDISK	"SharedDisk:"	/* SharedDisk (VxFI) */
	sts_stream_format_v7_t	srv_sth[STS_MAX_STH]; /* supported stream formats */
	sts_uint32_t		srv_flags;
#define STS_SRV_ASYNC		0x1	/* async image read/write service group supported */
/* following should be deprecated - image listing is required in all plugins */
#define STS_SRV_IMAGELIST	0x2	/* image list capability */
#define STS_SRV_CRED		0x4	/* credentials required */
#define STS_SRV_CONRW		0x8	/* concurrent read/write capability */
#define STS_SRV_DISK		0x10	/* supports disk media */
#define STS_SRV_TAPE		0x20	/* supports tape media */
#define STS_SRV_EVENT		0x40	/* deprecated */
#define STS_SRV_IMAGE_GROUP	0x80	/* image grouping service group supported */
#define STS_SRV_EVASYNC		0x40	/* async event service group supported */
#define STS_SRV_EVSYNC		0x100	/* sync event service group supported */
#define STS_SRV_IMAGE_COPY	0x200	/* image copy service group supported */
#define STS_SRV_NDMP_FSE	0x400	/* NDMP File Service Extension supported */
	sts_uint32_t		srv_maxconnect; /* max concurrent connections */
	sts_uint32_t		srv_nconnect; /* open connections */
#define STS_SRV_MAXCAP		65
	sts_srv_cap_v8_t	srv_cap[STS_SRV_MAXCAP]; /* server capabilities */
	/* see values and macros below */
	sts_interface_v7_t	srv_interface[STS_MAX_IF];
#define STS_MAX_CRED		8
	sts_cred_type_v7_t	srv_cred[STS_MAX_CRED];
	sts_server_name_v7_t	srv_ndmp_host; /* if STS_SRV_NDMP_FSE */
} sts_server_info_v8_t;

#define SWAP_STS_SERVER_INFO_V8_T(srv) { \
	SWAP_STS_SERVER_NAME_V7_T(&(srv)->srv_server); \
	{ int i; for (i=0; i<STS_MAX_STH; i++) \
		SWAP_STS_STREAM_FORMAT_V7_T(&(srv)->srv_sth[i]); } \
	SWAP_STS_UINT32_T(&(srv)->srv_flags); \
	SWAP_STS_UINT32_T(&(srv)->srv_maxconnect); \
	SWAP_STS_UINT32_T(&(srv)->srv_nconnect); \
	{ int i; for (i=0; i<STS_SRV_MAXCAP; i++) \
		SWAP_STS_SRV_CAP_V8_T(&(srv)->srv_cap[i]); } \
	{ int i; for (i=0; i<STS_MAX_IF; i++) \
		SWAP_STS_INTERFACE_V7_T(&(srv)->srv_interface[i]); } \
	{ int i; for (i=0; i<STS_MAX_CRED; i++) \
		SWAP_STS_CRED_TYPE_V7_T(&(srv)->srv_cred[i]); } \
	SWAP_STS_SERVER_NAME_V7_T(&(srv)->srv_ndmp_host); \
}

#endif /* STS_VERSION >= 8 */

#ifndef MASTER_SHARED_LIB
#if STS_VERSION >= 8 && !STS_COMPAT
typedef sts_server_info_v8_t sts_server_info_t;
#define SWAP_STS_SERVER_INFO_T(srv) SWAP_STS_SERVER_INFO_V8_T(srv)
#else
typedef sts_server_info_v7_t sts_server_info_t;
#define SWAP_STS_SERVER_INFO_T(srv) SWAP_STS_SERVER_INFO_V7_T(srv)
#endif
#endif /* MASTER_SHARED_LIB */

/*
 * Server capabilities are defined in the integer array srv_cap.
 * The following macros can be used to test/set/clear capabilities.
 * There is no need for the macros to be particularly efficient.
 */
/* TODO - WDD - replace with STS_BITSPCW */
#define STS_BITSPCW_SRV(srv) STS_BITSPCW
#if 0
#define STS_BITSPCW_SRV(srv) (STS_BITSPCW((srv)->srv_cap[0])) /* bits per sts_srv_cap_t */
#endif

#define STS_SET_SRV_CAP(srv, cap) \
	( (srv)->srv_cap[(cap)/STS_BITSPCW_SRV(srv)] |= (1 << ((cap)%STS_BITSPCW_SRV(srv))))
#define STS_TST_SRV_CAP(srv, cap) \
	( (srv)->srv_cap[(cap)/STS_BITSPCW_SRV(srv)] & (1 << ((cap)%STS_BITSPCW_SRV(srv))))
#define STS_CLR_SRV_CAP(srv, cap) \
	( (srv)->srv_cap[(cap)/STS_BITSPCW_SRV(srv)] &= ~(1 << ((cap)%STS_BITSPCW_SRV(srv))))

/*
 * Flags pertaining to aspects of each service can be defined with
 * the values between the service flag value and value of the next
 * service flag. TODO: remove flags for servies that are now required.
 */

/* There is a block of functions in src/common/libVsts/libsts.c in the function
 * stsm_server_cap_v8 that has one block of code ( 5 lines ) for each #define in this
 * section. If any services are added, you MUST add an appropriate code block in
 * that file. Just copy/paste the last one that is there and change the name of the 
 * service and it's #define'd integer value to match what you're adding here
*/
#if STS_VERSION >= 8

#define STS_SRVC_ASYNC_FLUSH		0
#define STS_SRVC_ASYNC_READ_IMAGE	10
#define STS_SRVC_ASYNC_WAIT		20
#define STS_SRVC_ASYNC_WRITE_IMAGE	30
#define STS_SRVC_CLAIM			40 /* required */
#define STS_SRVC_CLOSE_IMAGE		50
#define STS_SRVC_CLOSE_IMAGE_LIST	60
#define STS_SRVC_CLOSE_LSU_LIST		70
#define STS_SRVC_CLOSE_SERVER		80
#define STS_SRVC_COPY_IMAGE		90
#define STS_SRVC_CREATE_IMAGE		100
#ifndef STS_EXTERNAL_VENDOR
#define STS_SRVC_DELETE_FILES		110
#endif /* STS_EXTERNAL_VENDOR */
#define STS_SRVC_DELETE_IMAGE		120
#define STS_SRVC_GET_IMAGE_PROP		130
#define STS_SRVC_GET_IMAGE_PROP_BYNAME	140
#define STS_SRVC_GET_LSU_PROP_BYNAME	150
#define STS_SRVC_GET_SERVER_PROP	160
#define STS_SRVC_GET_SERVER_PROP_BYNAME	170
#define STS_SRVC_INCLUDE_IN_IMAGE	180
#define STS_SRVC_LIST_IMAGE		190
#define STS_SRVC_LIST_LSU		200
#define STS_SRVC_OPEN_LSU_LIST		210
#define STS_SRVC_OPEN_SERVER		220
#define STS_SRVC_OPEN_IMAGE		230
#define STS_SRVC_OPEN_IMAGE_LIST	240
#define STS_SRVC_READ_IMAGE		250
#define STS_SRVC_TERMINATE		260
#define STS_SRVC_WRITE_IMAGE		270
#define STS_SRVC_GET_IMAGE_GROUP	280
#define STS_SRVC_GET_IMAGE_GROUP_BYNAME	290
#define STS_SRVC_OPEN_IMAGE_GROUP_LIST	300

#endif /* STS_VERSION >= 8 */

#if STS_VERSION >= 9

/* flags for required interfaces should be removed */
#define STS_SRVC_ADD_EVSOURCE		310
#define STS_SRVC_ADD_EVSOURCE_AUTONAME	320
#define STS_SRVC_CLOSE_EVCHANNEL	330
#define STS_SRVC_CLOSE_IMAGE_INCOMPLETE	340
#define STS_SRVC_COPY_EXTENT		350
#define STS_SRVC_COPY_EXTENT_WHOLEIMAGE 351
#define STS_SRVC_COPY_EXTENT_REMOTE	360
#define STS_SRVC_CREATE_IMAGE_MIXED_MODE 370 /* mixed data modes */
#define STS_SRVC_DELETE_EVENT		380
#define STS_SRVC_GET_EVENT		390
#define STS_SRVC_IOCTL			400
#define STS_SRVC_OPEN_EVCHANNEL		410
#define STS_SRVC_OPEN_LSU_LIST_FILTER	420
#define STS_SRVC_WRITE_IMAGE_RANDOM	430 /* random + sparse writes */
#define STS_SRVC_OPEN_TARGET_SERVER	440 
#define STS_SRVC_FIND_LSU		450
#define STS_SRVC_LABEL_LSU		460

#endif /* STS_VERSION >= 9 */

#if STS_VERSION >= 10

#define STS_SRVC_CREATE_IMAGE_ISINFO	101
#define STS_SRVC_METADATA		470

#endif /* STS_VERSION >= 10 */

#if STS_VERSION >= 11
#define STS_SRVC_COPY_IMAGE_REMOTE 		91
#define STS_SRVC_ASYNC_COPY_IMAGE 		480	
#define STS_SRVC_ASYNC_COPY_IMAGE_REMOTE  	481	
#define STS_SRVC_NAMED_ASYNC_COPY_IMAGE 	490	
#define STS_SRVC_NAMED_ASYNC_COPY_IMAGE_REMOTE  491	
#define STS_SRVC_ASYNC_CANCEL			500	
#define STS_SRVC_NAMED_ASYNC_CANCEL		510
#define STS_SRVC_NAMED_ASYNC_WAIT		520
#define STS_SRVC_NAMED_ASYNC_STATUS		530
#define STS_SRVC_GET_SERVER_CONFIG		540
#define STS_SRVC_SET_SERVER_CONFIG		550
#define STS_SRVC_BEGIN_COPY_IMAGE		560
#define STS_SRVC_END_COPY_IMAGE					570
#define STS_SRVC_ASYNC_END_COPY_IMAGE			580
#define STS_SRVC_NAMED_ASYNC_END_COPY_IMAGE		590
#define STS_SRVC_GET_LSU_REPLICATION_PROP	 	600	
#define STS_SRVC_IOCONTROL	 	610	

#endif /* STS_VERSION >= 11 */



/********************************************************
* THIS STRUCT MUST BE N*8 BYTES, WITH NO COMPILER HOLES	*
*********************************************************/
/* argument to sts_open_server() */
typedef struct {
	sts_uint32_t	cr_len;  /* actual length */
	sts_cred_type_v7_t	cr_type;
	sts_cert_v7_t	cr_cert; /* may contain nulls */
} sts_cred_v7_t; 
#define SWAP_STS_CRED_V7_T(cr) { \
	SWAP_STS_UINT32_T(&(cr)->cr_len); \
	SWAP_STS_CRED_TYPE_V7_T(&(cr)->cr_type); \
	SWAP_STS_CERT_V7_T(&(cr)->cr_cert); \
}
#ifndef MASTER_SHARED_LIB
typedef sts_cred_v7_t sts_cred_t;
#define SWAP_STS_CRED_T(cr) SWAP_STS_CRED_V7_T(cr)
#endif /* MASTER_SHARED_LIB */

typedef struct {
	sts_pgn_pfx_v7_t		spp_pfx;	/* prefix itself */
	sts_pgn_pfx_label_v7_t	spp_label;	/* nice descripiton of prefix */
} sts_pgn_pfx_def_v7_t;

#ifndef MASTER_SHARED_LIB
typedef sts_pgn_pfx_def_v7_t sts_pgn_pfx_def_t;
#endif /* MASTER_SHARED_LIB */

#if STS_VERSION >= 9

/* argument to sts_list_plugin() */
typedef struct {
	sts_pgn_pfx_def_v7_t spd_pfxdef[STS_MAX_PFX];
	int		spd_npfx;
	sts_uint32_t	spd_flags;
/* this flag should be deprecated */
#define STS_SPD_LOCALONLY 0x1 /* plugin host always same as server host */
	sts_uint64_t	spd_build_version;
	sts_uint64_t	spd_build_version_minor;
	sts_uint64_t	spd_operating_version;
	sts_vendor_version_v9_t spd_vendor_version;
} sts_pgn_def_v9_t;

#endif /* STS_VERSION >= 9 */

/* argument to sts_list_plugin() */
typedef struct {
	sts_pgn_pfx_def_v7_t spd_pfxdef[STS_MAX_PFX];
	int		spd_npfx;
	sts_uint32_t	spd_flags;
/* this flag should be deprecated */
#define STS_SPD_LOCALONLY 0x1 /* plugin host always same as server host */
} sts_pgn_def_v7_t;

#ifndef MASTER_SHARED_LIB
#if STS_VERSION >= 9
typedef sts_pgn_def_v9_t sts_pgn_def_t;
#else
typedef sts_pgn_def_v7_t sts_pgn_def_t;
#endif
#endif /* MASTER_SHARED_LIB */

#if STS_VERSION >= 9
typedef struct {
	sts_pgn_name_v7_t	spg_name;
	sts_pgn_def_v9_t	spg_def;
} sts_plugin_v9_t;

#endif

typedef struct {
	sts_pgn_name_v7_t	spg_name;
	sts_pgn_def_v7_t spg_def;
} sts_plugin_v7_t;

#ifndef MASTER_SHARED_LIB
#if STS_VERSION >= 9
typedef sts_plugin_v9_t sts_plugin_t;
#else
typedef sts_plugin_v7_t sts_plugin_t;
#endif
#endif /* MASTER_SHARED_LIB */

#if STS_VERSION >= 11

typedef struct {
	sts_opname_v11_t	e_name;
	sts_aioresult_v11_t	e_result;
} sts_ev_op_v11_t;

typedef enum {
		STS_ESRV_ONLINE,	/* transitioned from offline */
		STS_ESRV_OFFLINE,	/* transitioned from online */
		STS_ESRV_AVOID		/* online but should not be used */
} sts_ev_srv_stat_type_v11_t;

typedef struct {
	sts_ev_srv_stat_type_v11_t e_action;
	sts_server_name_v7_t e_name;
} sts_ev_srv_stat_v11_t;

typedef enum {
		STS_ELSU_ONLINE,	/* transitioned from offline */
		STS_ELSU_OFFLINE,	/* transitioned from online */
		STS_ELSU_AVOID		/* online but should not be used */
} sts_ev_lsu_stat_type_v11_t;

typedef struct {
 	sts_ev_lsu_stat_type_v11_t e_action;
	sts_lsu_name_v7_t e_name;
} sts_ev_lsu_stat_v11_t;

typedef struct {
	sts_uint64_t	e_fields;
#define STS_EVT_LSU_INFO_SERVER			0x1
#define STS_EVT_LSU_INFO_CAPACITY		0x2
#define STS_EVT_LSU_INFO_CAPACITY_PHYS	0x4
#define STS_EVT_LSU_INFO_USED			0x8
#define STS_EVT_LSU_INFO_USED_PHYS		0x10
#define STS_EVT_LSU_INFO_IMAGES			0x20
#define STS_EVT_LSU_INFO_NAME			0x40
#define STS_EVT_LSU_INFO_ALLOC			0x80
#define STS_EVT_LSU_INFO_STORAGE		0x100
#define STS_EVT_LSU_INFO_DESCRIPTION	0x200
#define STS_EVT_LSU_INFO_CONFIG			0x400
#define STS_EVT_LSU_INFO_FLAGS			0x800
#define STS_EVT_LSU_INFO_SAVEAS			0x1000
#define STS_EVT_LSU_INFO_MAX_TRANSFER	0x2000
#define STS_EVT_LSU_INFO_BLOCK_SIZE		0x4000
#define STS_EVT_LSU_INFO_ALLOC_SIZE		0x8000
#define STS_EVT_LSU_INFO_LABEL			0x10000
	sts_lsu_info_v11_t	e_info;
} sts_ev_lsu_info_v11_t;

typedef struct {
	sts_uint64_t		e_fields;
#define STS_EVT_IMG_INFO_EXPORT		0x1
#define STS_EVT_IMG_INFO_STATUS		0x2
	sts_image_info_v10_t	e_info;
} sts_ev_img_info_v11_t;

typedef struct {
	sts_uint64_t		e_fields;
#define STS_EVT_SRV_INFO_STH		0x1
#define STS_EVT_SRV_INFO_FLAGS		0x2
#define STS_EVT_SRV_INFO_MAXCONNECT	0x4
#define STS_EVT_SRV_INFO_INTERFACE	0x8
#define STS_EVT_SRV_INFO_CRED		0x10
#define STS_EVT_SRV_INFO_NDMP_HOST	0x20
#define STS_EVT_SRV_INFO_CAP		0x40
	sts_server_info_v8_t e_info;
} sts_ev_srv_info_v11_t;

typedef struct {
	sts_server_name_v7_t	e_orig_server;
	sts_uint64_t        	e_num_images;
	sts_image_info_v10_t	e_imglist[1];
} sts_ev_image_dup_v11_t;

typedef sts_uint64_t sts_evseqno_v11_t;
#define SWAP_STS_EVSEQNO_V11_T(evseqno) SWAP_STS_UINT64_T(evseqno)

typedef enum { /* inline payload types */
	STS_EVT_SOURCE,		/* event source dropped */
	STS_EVT_OP,		/* named async op report */
	STS_EVT_LSU_INFO,	/* LSU properties changed */
	STS_EVT_SRV_STAT,	/* storage server status changed */
	STS_EVT_LSU_STAT,	/*  LSU status  changed */
	STS_EVT_IMG_INFO	 /* image properties changed */
} sts_ev_inline_type_v11_t;
#define SWAP_STS_INLINE_EVTYPE_V11_T(evtype) SWAP_STS_ENUM_T(evtype)

typedef	enum { /* detached payload types */
		STS_EVT_SRV_INFO,	/* server properties changed */
		STS_EVT_IMG_DUP	/* image replicant */
} sts_ev_detached_type_enum_v11_t;

typedef struct {
	sts_uint64_t ed_len;		/* payload length, bytes */
	sts_ev_detached_type_enum_v11_t ed_type;
	sts_uint32_t ed_filler;		/* keep it n* 8 */
} sts_ev_detached_type_v11_t; 
#define SWAP_STS_DETACHED_EVTYPE_V11_T(evtype) { \
	SWAP_STS_UINT64_T(&(evtype)->ed_len) \
	SWAP_STS_ENUM_T(&(evtype)->ed_type) \
}

typedef union {
	sts_ev_inline_type_v11_t		evt_inline; 
	sts_ev_detached_type_v11_t		evt_detached;
} sts_evtype_v11_t;

typedef struct {
	 union { 
		/* contains inline payloads only - all others are detached */
		sts_ev_op_v11_t			u_op;
		sts_ev_lsu_info_v11_t	u_lsu_info;
		sts_ev_srv_stat_v11_t	u_srv_stat;
		sts_ev_lsu_stat_v11_t	u_lsu_stat;
		sts_ev_img_info_v11_t	u_img_info;
		sts_evpl_v9_t			u_ev_pl;		/* payload for v9 */
	} u;
#define eb_op		u.u_op
#define eb_lsu_info	u.u_lsu_info
#define eb_srv_stat	u.u_srv_stat
#define eb_lsu_stat	u.u_lsu_stat
#define eb_img_info	u.u_img_info
#define eb_ev_pl 	u.u_ev_pl
} sts_evbody_v11_t;

/* eventflag argument to *_copy_image */
#define STS_EVFLAG_TRIGGER 0x1         /* trigger an event when this image complete */
#define STS_EVFLAG_INCLUDE 0x2         /* include the image def of this image in the event payload */
typedef struct {
	sts_uint64_t		version; 		/* API version of this struct */
	sts_evseqno_v11_t	ev_seqno;		/* sequence number */
	sts_server_name_v7_t ev_server;
	sts_uint64_t	ev_flags;
#define STS_EVF_STORED 0x1	/* can be deleted with sts_delete_event */
#define STS_EVF_INLINE	0x2	/* payload is inline */
	sts_evtype_v11_t	ev_type;
	sts_evbody_v11_t	ev_body; /* inline payload - new in v11 */
} sts_event_v11_t;
/* the content of ev_body is too complicated, so it is not swapped here */
#define SWAP_STS_EVENT_V11_T(event) { \
	SWAP_STS_UINT64_T(&(event)->version); \
	SWAP_STS_EVSEQNO_V11_T(&(event)->ev_seqno); \
	SWAP_STS_SERVER_NAME_V7_T(&(event)->ev_server); \
	SWAP_STS_UINT64_T(&(event)->ev_flags); \
	if (((event)->ev_type) & STS_EVF_INLINE) { \
		SWAP_STS_INLINE_EVTYPE_V11_T(&((event)->ev_type.evt_inline)); \
	} \
	else  {\
		SWAP_STS_DETACHED_EVTYPE_V11_T(&((event)->ev_type.evt_detached))\
	} \
	SWAP_STS_EVTYPE_T(&(event)->ev_type); \
}

#ifdef __cplusplus
extern "C" {
#endif
	typedef void (*sts_evhandler_v11_t)(sts_event_v11_t *);
#ifdef __cplusplus
}
#endif

#ifndef MASTER_SHARED_LIB
typedef sts_ev_op_v11_t sts_ev_op_t;
typedef sts_ev_srv_stat_v11_t sts_ev_srv_stat_t;
typedef sts_ev_lsu_stat_v11_t sts_ev_lsu_stat_t;
typedef sts_ev_lsu_info_v11_t sts_ev_lsu_info_t;
typedef sts_ev_img_info_v11_t sts_ev_img_info_t;
typedef sts_ev_srv_info_v11_t sts_ev_srv_info_t;
typedef sts_ev_image_dup_v11_t sts_ev_image_dup_t;
typedef sts_evseqno_v11_t sts_evseqno_t;
typedef sts_ev_inline_type_v11_t sts_ev_inline_type_t;
typedef sts_ev_detached_type_v11_t sts_ev_detached_type_t;
typedef sts_evtype_v11_t sts_evtype_t;
typedef sts_event_v11_t sts_event_t;
typedef sts_evhandler_v11_t sts_evhandler_t;
typedef sts_evbody_v11_t sts_evbody_t;
#define SWAP_STS_EVSEQNO_T(evseqno) SWAP_STS_EVSEQNO_V11_T(evseqno)
#define SWAP_STS_INLINE_EVTYPE_T(evtype) SWAP_STS_INLINE_EVTYPE_V11_T(evtype)
#define SWAP_STS_DETACHED_EVTYPE_T(evtype) SWAP_STS_DETACHED_EVTYPE_V11_T(evtype)
#define SWAP_STS_EVENT_T(event) SWAP_STS_EVENT_V11_T(event)
#endif

#endif 

#if STS_VERSION >= 9

/* event id - pointer or integer */
typedef union {
	void		*eid_ptr;
	sts_uint64_t	eid_val; 
} sts_evid_v9_t;
#define SWAP_STS_EVID_V9_T(ev) SWAP_STS_UINT64_T(&(ev)->eid_val)

#define STS_EVT_ALL_EXCEPT	0x1
#define	STS_EVT_NONE_EXCEPT	0x2

/* event */
typedef struct {
	sts_server_name_v7_t ev_server;
	sts_uint64_t	ev_flags; 
#define STS_EVF_STORED	0x1 	/* can be deleted with sts_delete_event() */
	sts_evpl_v9_t	ev_pl;	/* payload */
} sts_event_v9_t;
#define SWAP_STS_EVENT_V9_T(event) { \
	SWAP_STS_SERVER_NAME_V7_T(&(event)->ev_server); \
	SWAP_STS_UINT64_T(&(event)->ev_flags); \
	SWAP_STS_EVPL_V9_T(&(event)->ev_pl); \
}

#define STS_STORED_EVENT(ev) ((ev)->ev_flags & STS_EVF_STORED)

typedef void (*sts_evhandler_v9_t)(sts_handle_t, sts_event_v9_t *);

#ifndef MASTER_SHARED_LIB
#if STS_VERSION < 11 
typedef sts_evid_v9_t sts_evid_t;
typedef sts_event_v9_t sts_event_t;
typedef sts_evhandler_v9_t sts_evhandler_t;
#define SWAP_STS_EVID_T(ev) SWAP_STS_EVID_V9_T(ev)
#define SWAP_STS_EVENT_T(event) SWAP_STS_EVENT_V9_T(event)
#endif
#endif


#define STS_MAX_HOST_NAME STS_MAX_STSNAME
typedef char sts_host_name_v9_t[STS_MAX_HOST_NAME];
#define SWAP_STS_HOST_NAME_V9_T(host)
#ifndef MASTER_SHARED_LIB
typedef sts_host_name_v9_t sts_host_name_t;
#define SWAP_STS_HOST_NAME_T(host) SWAP_STS_HOST_NAME_V9_T(host)
#endif /* MASTER_SHARED_LIB */

typedef struct {
	sts_server_name_v7_t	st_server;
	sts_cred_v7_t		st_cred;
	sts_lsu_name_v7_t	st_lsu;
} sts_target_v9_t;
#define SWAP_STS_TARGET_V9_T(t) { \
	SWAP_STS_SERVER_NAME_V7_T(&(t)->st_server); \
	SWAP_STS_CRED_V7_T(&(t)->st_cred); \
	SWAP_STS_LSU_NAME_V7_T(&(t)->st_lsu); \
}
#ifndef MASTER_SHARED_LIB
typedef sts_target_v9_t sts_target_t;
#define SWAP_STS_TARGET_T(t) SWAP_STS_TARGET_V9_T(t)
#endif /* MASTER_SHARED_LIB */

typedef struct {
	sts_handle_t		tl_server_handle;
	sts_target_v9_t		tl_target;
} sts_target_lsu_v9_t;
#ifndef MASTER_SHARED_LIB
typedef sts_target_lsu_v9_t sts_target_lsu_t;
#endif /* MASTER_SHARED_LIB */

/* flags argument to sts_copy_extent()/stsp_copy_extent() */
#define STS_CX_APPEND		0x1	/* to_offset: append to target */
#define STS_CX_REMAINDER	0x2	/* len: remainder of source image */
 
/* flag argument to sts_close()/stsp_close_image() */
#define STS_CLOSEF_IH_INCOMPLETE    0x0 /* image is not	complete */
#define STS_CLOSEF_IH_COMPLETE      0x1 /* image is complete */
#define STS_CLOSEF_IH_ASYNC_ABORT   0x2 /* abort async writes */
#define STS_CLOSEF_IH_CPR           0x4 /* checkpoint/restart */
#define STS_CLOSEF_SSH_CLEANUP      0x8 /* close all handles associated with this session handle */ 

/* the following close types are deprecated */
#ifdef STS_CLOSEF_IH_DEPRECATED
#define STS_CLOSEF_IH_SOFT  STS_CLOSEF_IH_INCOMPLETE
#define STS_CLOSEF_IH_HARD  STS_CLOSEF_IH_ASYNC_ABORT
#endif

/* flags argument to sts_create_image()/stsp_create_image() */
#define STS_IC_PENDING	0x1
#define STS_IC_WRITE	0x2
#define STS_IC_INCLUDE	0x4
#define STS_IC_COPY	0x8

#endif /* STS_VERSION >= 9 */

#if STS_VERSION >= 8
typedef struct {
	int	sv_cap;		/* integer value representing the server capability */
	char	sv_string[STS_SERVER_CAP_STRING_MAX_LEN];	/* text version of the capability */
	
} sts_server_cap_entry_v8_t;
#ifndef MASTER_SHARED_LIB
typedef sts_server_cap_entry_v8_t sts_server_cap_entry_t;
#endif /* MASTER_SHARED_LIB */
#endif /* STS_VERSION >= 8 */

#if STS_VERSION >= 9
#define STS_IOCTL_JOB_STARTED		9993
#define STS_IOCTL_JOB_FINISHED		9994	
#define STS_IOCTL_CLEANUP_SESSION	9995
#define STS_IOCTL_GET_RESTORE_CNTL	9996
#define STS_IOCTL_SET_RESTORE_CNTL	9997
typedef struct {
	sts_int32_t fragnum;	/* Fragment child wants to skip to */
	sts_int64_t firstblk;	/* Relative to fragnum */
	sts_int64_t blocks_to_skip;
	sts_int64_t bytes_to_skip;
	sts_int32_t tar_pid;	/* Set by tar on startup */
	sts_int32_t tar_exitstat;	/* Set by tar on exit */
	sts_int32_t tar_control;	/* Set to 2 by bptm if restore aborting */
	sts_int32_t suspended;		/* Parent received SUSPEND */
	sts_int32_t reserve2;		/* For future use */
	unsigned char b_fragnum;
	unsigned char b_firstblk;
	unsigned char b_blocks_to_skip;
	unsigned char b_bytes_to_skip;
	unsigned char b_tar_pid;
	unsigned char b_tar_exitstat;
	unsigned char b_tar_control;
	unsigned char b_suspended;
			/* The following fields support the NBU media_shm_restore structure */
	unsigned char b_set_restore_info;
	sts_int32_t copy_num;
	unsigned char b_copy_num;
	sts_int32_t bprd_port;
	unsigned char b_bprd_port;
	char    restore_svr[STS_MAX_IMAGENAME]; /* NB_MAXHOSTNAMELEN */
	unsigned char b_restore_svr;
	char    client[STS_MAX_IMAGENAME];
	unsigned char b_client;
	char    backupid[STS_MAX_IMAGENAME]; /* MAX_BACKUPID_LEN */
	unsigned char b_backupid;
	sts_int32_t child_delay;
	unsigned char b_child_delay;
	sts_int32_t locate_available;
	unsigned char b_locate_available;
	sts_int32_t min_locate_skip;
	unsigned char b_min_locate_skip;
	sts_int32_t valid;
	unsigned char b_valid;
	char    bprd_ipc[STS_MAX_IMAGENAME]; /* VN_IPC_LENGTH_MAX */
	unsigned char b_bprd_ipc;
} sts_ioctl_restore_cntl_v9_t;
typedef sts_ioctl_restore_cntl_v9_t sts_ioctl_restore_cntl_t;

#define STS_IOCTL_SET_MEDIA_STATE 9998
typedef struct {
	sts_uint32_t sio_media_state_flags;
	sts_handle_t sio_media_handle; /* could be any STS Handle */
	sts_lsu_v9_t sio_lsu;
	/* flags argument to control ioctl */
#define STS_IOCTL_MEDIA_NOT_READY 	0
#define STS_IOCTL_MEDIA_READY     	1
#define STS_IOCTL_MEDIA_ERROR       2
#define STS_IOCTL_MEDIA_CHECKPOINT	3
#define STS_IOCTL_MEDIA_FRAGMENT    4
#define STS_IOCTL_MEDIA_MIN      	0
#define STS_IOCTL_MEDIA_MAX      	STS_IOCTL_MEDIA_FRAGMENT
} sts_ioctl_set_media_state_v9_t;
typedef sts_ioctl_set_media_state_v9_t sts_ioctl_set_media_state_t;

#if STS_VERSION >= 11
#define STS_IOCTL_IN    0x1
#define STS_IOCTL_OUT   0x2
#endif

#define STS_IOCTL_GET_DDUP_STATS	9999
#define STS_IOCTL_DS_MSG_LEN		2048
typedef struct {
	char 		ds_msg[STS_IOCTL_DS_MSG_LEN+1];
	sts_uint32_t	ds_msg_len;
} sts_ioctl_ddup_stats_info_v9_t; 
typedef sts_ioctl_ddup_stats_info_v9_t sts_ioctl_ddup_stats_info_t;
#endif /* STS_VERSION >= 9 */

#endif /* !_STSI_H_ */

