

           OST SDK 11.1.1 RELEASE NOTES

   NetBackup OpenStorage Software Development Kit 11.1.1 



CONTENTS
----------------------------------------------------------------------
 - Introduction
 - Contents of the SDK
 - System Requirements & Installation
 - SDK Changes
 - Terms and Conditions
 - Symantec Contact Information


INTRODUCTION
----------------------------------------------------------------------

This document summarizes the contents of the OpenStorage (OST) SDK
11.1.1 release.  The OST SDK is a development kit for building
intelligent disk storage systems accessible by a data protection
application (NetBackup) via the OpenStorage Application Programming
Interface.  The OST API is an image-oriented abstract interface to
disk storage.

The SDK version number (e.g., a.b.s) reflects the API version level
(a), the latest API bulletin number (b) for which changes in the
header files have been made, and the SDK refresh (s) against the
indicated API and bulletin references.  The third number (s) exists to
differentiate separate SDK releases issued against the same API and
bulletin numbers (a.b).  Note: The a.b version appears in a comment at
the top of the stsi.h and stspi.h header files.

This SDK (11.1.1) corresponds to OST API Version 11.0.


CONTENTS OF THE SDK
----------------------------------------------------------------------

Here is a general summary of the files and directories in the SDK.


HEADER FILES

(in the 'src/include' directory) A plugin needs to explicitly include
only stspi.h.  The stspi.h header file includes the others
automatically.  The build environment must reference src/include and
src/include/platforms/$(PLATFORM), where $(PLATFORM) is the
appropriate directory in src/include/platforms for the target
platform.


CORBA INTERFACE DEFINITION LANGUAGE (IDL) FILES

(in the 'src/idl' directory) An example of RPC/proxy mechanism of using 
CORBA to support replying STS API call from one host to another host. 
ProxyTypes.idl contains all of the data structures from OST version 9 to 11.1,
Proxy.idl contains all of the API functions from OST version 9 to 11.1.


DOCUMENTATION

(in the 'doc' directory) The Plugin Writer's Guide contains
information useful to the plugin developer.  The document introduces
OpenStorage API concepts, terminology and principles of operation.
The document also contains man pages that explain each API service in
detail.  The OST-TOI-Notes.pdf and the OST-Post-TOI-Notes.pdf contain
the material presented during the the vendor training class held
September 2006 at Symantec's Roseville offices. The bulletins
directory contains all previously issued Symantec API bulletins.
Bulletins may contain clarifications of obscure API semantics
(typically in response to vendor questions), news of imminent changes
to the API, or news relevant to future SDK versions.


SAMPLE PLUGIN

(in the 'sample' directory) A simple plugin, written in C++, has
been included for reference.  This plugin is provided for testing
purposes and to illustrate basic plugin concepts.  Because the plugin
design is overly simplified, it should not be used as a template for
designing a real vendor plugin.


PLUGIN TESTER

(in the 'tools' directory) The Plugin Tester is a tool for API-level
testing of plugins.  The Plugin Tester consists of a web application
that you run on the system where your plugin resides. The user
interface is invoked from a browser that need not be running on the
same system as the plugin.  Refer to the Release Note's Installation
section for instructions on installing the Plugin Tester.  A README
within the Plugin Tester tar file provides usage instructions.


PLUGIN TESTER SOURCE (native portion only)

(in the 'tools' directory) The pgndriver_src.tar file contains the
code for the native portion of the Plugin Tester.  It can be used to
test plugins on other platforms than what is supported by the packaged
Plugin Tester.


PREQUALIFICATION TEST TOOL

(in the 'tools' directory) The Plugin Pre-Qualification Test Tool is a
set of utilities for plugin developers to use to test and validate
their implementation of an OpenStorage plugin.  The tool is intended
to test plugins at a higher level than the Plugin Tester, exercising
the plugin in the context of test cases such as backup, restore, and
duplication.  Refer to the Release Note's Installation section for
instructions on installing the prequal tool. READMEs within the
prequal tar file provide usage instructions for the various utilities.


INSTALLING THE PLUGIN TESTER
----------------------------------------------------------------------

The Plugin Tester needs to be installed on the system where your
plugin resides.  Copy the pgntester.tar file to the system and unpack.

	 tar xvf pgntester.tar

A pgntester directory will be created.  Inside the directory is a
README with instructions on usage.  The Plugin Tester requires the
Java Runtime Environment.  To complete the installation, you must have
the SUN JRE v1.5.0 (or later) installed.  If your system does not have
the JRE installed, go to http://java.com/en/download/manual.jsp. Make
sure your your PATH variable includes <JRE-installation>/bin.


INSTALLING THE PREQUALIFICATION TEST TOOL
----------------------------------------------------------------------

The Pre-Qualification test tool package must be installed on the
system where your plugin resides.  Note: If your test environment
intends to mimic a multi-system NetBackup environment (e.g., master
server and one or more media servers), then install the prequal
package on all systems.  

Packages are platform-specific.
Note: The prequal tool will eventually be ported to all of NetBackup's
media server platforms.  The current SDK includes support for only a
subset - Solaris, Linux and Windows.  

Copy the prequal_xxxx.tar file (where xxxx indicates the platform) to
the system and unpack.  For example, 

	 tar xvf prequal_solaris.tar

A prequal directory will be created.  Inside the directory is a
README with instructions on usage.  


SDK CHANGES
----------------------------------------------------------------------
Updates introduced in 11.1.1:
1. Rebranded PWG and Bulletin as Veritas
2. Removed unsupported platforms
3. Updated SampleSTS.h with "#define MAXPATHLEN 1024"

Updates introduced in 11.1.0:
1. Support for OST Bulletin 11.1.0 changes.
2. SampleDisk plugin now supports all of OST 11.1.0 STSPI functions.

Updates introduced in 11.0.0:
1. Support for OST Bulletin 11.0 changes.
2. The prequal tool now supports OST v11.0 named async copy image and event.
   No change for pgntester at present. 
3. SampleDisk plugin now supports all of OST v11.0 STSPI functions and STS_EVT_OP event.
4. Corba IDL sample files for proxy now included in package.

Updates introduced in 10.0.0:
1. The prequal tool and pgntester now support OST v10.
   OST v9 support has been deprecated. 
2. Support for OST Bulletin 5 versioning changes.
3. Support of sts_image_def_t and sts_image_info_t changes
4. stsnbu.h file now included in package.

Updates introduced in 9.4.3:
1.  Platform support added in the SDK for HP-UX PA-RISC,
    HP-UX IA64, and AIX
2.  pgntestercli.pl tool added to the Plug-in Tester.

Updates introduced in 9.4.2:
1.  NetBackup 6.5GA libraries included
2.  Optimized Duplication support added to prequal tool.
3.  Optimized Duplication support added to SampleDisk plugin.
4.  Tests added to pgntester.

Updates introduced in 9.4.1:
1. Issues addressed within the prequal tool.
2. Enhancements were made to the pgntester and issues addressed.  
   Tests have been added to cover the entire required API set for the 
   OpenStorage 9.4 release.
3. Issues addressed within the SampleDisk plugin.

Updates introduced in 9.4.0:
1. Added API bulletins STSI-Bulletin4.pdf, STSPI_Bulletin-apndx.pdf 
   and modified header files to reflect API changes described in 
   bulletin #4.  The appendix to bulletin 4 describes minor API 
   changes put in place to support future OST to tape functionality.  
2. Enhanced sample plugin source to demonstrate how a future plugin
   coded to a later API version (e.g., v10) should initialize with a
   NetBackup core library running at an earlier API version (e.g.,
   v9).
3. Enhancements to the prequalification tool: New options to the
   stsmanager utility to perform checkpoint restarted backups;
   corresponding testsuites to run in the TestManager test harness and
   a support script for gathering and bundling log files, test reports
   and environment information.  The support script makes it easier
   for OST developers to gather the necessary items into a submittable
   bundle to Symantec development or the qualification team.
4. Enhanced sample plugin to impose size quotas on the LSUs and report
   out of capacity errors.  These changes were made to enable the
   sampledisk plugin to be used for demonstrating the out of space
   test sets in the prequal tool.  Other changes to the plugin were
   made to illustrate how a plugin deals with a backlevel STS core
   library, i.e., a core library running at STS v9 and the plugin at
   STS v10.
5. STEP_OST_Storage_Server_Profile.doc has been included.  
   This form was designed to accelerate the qualification testing of
   your OpenStorage plugin for Veritas NetBackup by Symantec.
   Specifically, it extracts information required by the Symantec 
   Hardware Compatibility Lab and Support teams, and cites important
   configuration and testing issues.  This form also highlights pertinent
   issues that must be addressed during testing to ensure your plugin
   meets the standard requirements

Updates introduced in 9.3.1:
1. Significant enhancements to the prequalification test tool. A test 
   harness has been added to execute an automated sequence of tests 
   and produce a submittable report to the Symantec qualification
   team.  Refer to the README in the tools/prequal_*.tar file.
2. Updates to the Plugin Writer's Guide - more information in the
   Plugin Implementation section, updates to reflect API bulletin #3,
   clarifications in response to questions from OpenStorage vendors.

Updates introduced in 9.3.0:
1. Significant documentation updates.  The Plugin Writer's Guide 
   replaces a previous collection of documents (man pages, 
   plugin writer's notes).  Note: The PWG is labeled 9.2 instead of
   9.3 because API bulletin #3 was released too close to the tech
   pubs deadline to be incorporated into the guide.
2. Addition of an initial version of a pre-qualification test tool.
   Expanded capabilities will be delivered in  upcoming SDK packages.
3. Enhancements to the Plugin Tester - added support for testing 
   plugin on the Windows platform, added the ability to output test
   results to a log file. Refer to the README in pgntester.tar.
4. Included the underlying C++ code which runs the Plugin Tester tool, 
   (i.e., pgndriver.cpp).  
5. Added API bulletin STSI-Bulletin3.pdf.
6. Modified headers to reflect API changes described in bulletin #3.
7. Minor correction in the sample plugin's Makefile to enable it to be
   built as an external vendor plugin rather than an internal 
   (Symantec) plugin. 
8. Minor update to stsi.h file to remove #ifndef STS_EXTERNAL_VENDOR
   from several definitions.  

Updates introduced in 9.2.0:
1. Applied new SDK release numbering system to align with API version.
2. Added bulletin STSI-Bulletin2.100606.doc.  
3. Modified headers to reflect API changes described in bulletin #2. 
   There is one exception -- in stsi.h, the removal of the deprecated 
   img_fulldate in sts_image_def_t will occur in the next SDK drop.
4. Added reference C++ plugin to sample directory.
5. Added Plugin Tester tool to tools directory.


PENDING UPDATES
----------------------------------------------------------------------

Please be patient. We recognize the need for a more comprehensive
SDK. We are working towards achieving that just as soon as we can.
Based on your feedback, we have plans to provide the following items
in an upcoming SDK release.

- Continued updates to the Pre-Qualification testing tool
    * Additional testsuites for the test harness
- A sample reference plugin written in C


TERMS AND CONDITIONS	
----------------------------------------------------------------------

The SDK is made available under non-disclosure agreement only.
Material provided within the SDK is governed by the OpenStorage API
(also referred to as the STS API) NDA currently in place between the
Partner and Symantec.  By downloading the SDK, you acknowledge this
agreement either for yourself or on behalf of your employer and agree
to be bound by its terms and conditions.  Additional software
distributed with the Plugin Tester is provided under the Apache
License v 2.0 (refer Apache_License_v2.0.txt).



SYMANTEC CONTACT INFORMATION
----------------------------------------------------------------------

For questions on API functionality, SDK content, and future SDK
content, send email to:

   <EMAIL>.
