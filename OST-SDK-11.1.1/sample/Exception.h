/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

#ifndef _EXCEPTION_H
#define _EXCEPTION_H

#include <string>

/*
 *	The Exception class is defined to report errors during execution of some methods.
 *	Error reporting by return values brings limitations while handling errors
 *	incurred during constructors of the objects.
 *
 * The Exception class is a simple structure with error code, descriptive string
 * and helper methods.
 *
 * The error code member of the exception can typically take value of any
 * standard STS error code declared in stsi.h
 *
 * The entrypoints code (sampledisk.cpp in this case) can return the error code
 * embedded in the exception as a result of failed STS service.
 *
 * The descriptive string inside the exception can be used for plugin's logging
 */

class STSException {
	private:
		std::string m_exception_string;
		int m_error_code;
	public:
		STSException(const char *);
		STSException(const char *,int);
		const char *toString();
		int error_code();
};

#endif /* _EXCEPTION_H */
