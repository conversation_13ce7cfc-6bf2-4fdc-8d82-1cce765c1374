/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

#ifndef _SAMPLEEVENTSERVER_H_
#define _SAMPLEEVENTSERVER_H_

#include <string>
#include <list>
#include <map>

#include "stsi.h"
#include "stspi.h"

#include "Common.h"
#include "SimpleLock.h"
#include "StorageServer.h"
#include "sampledisk.h"

class EventServer{
private:
	SimpleLock m_lock;
	SimpleLock m_eventlock;
	/* push mode event channel list*/
	std::list<stsp_evc_handle_t> m_subscribers;

	std::string m_evPath;
	std::string m_evSeqPath;
	/* a global sequence number for events, it's incremental */
	sts_evseqno_v11_t m_evseqno;

	comm_thread_t m_monitorThread;

public:
	bool m_bExit;

public:
	EventServer(std::string stsServerName);
	~EventServer();

	void refresh();
	int subscribeEvent(stsp_evc_handle_t eh);
	int unsubscribeEvent(stsp_evc_handle_t eh);
	int addEvent(sts_event_v11_t event);
	int addEvent(sts_event_v11_t event, void* payload);
	int getEvent(stsp_evc_handle_t eh, sts_event_v11_t *event);
	int getEventPayload(const sts_event_v11_t *event, void* buf);
	int delEvent(sts_event_v11_t *event);

private:
	sts_evseqno_v11_t readSeqNumber();
	void writeSeqNumber(sts_evseqno_v11_t seqno);
	int readEvent(sts_evseqno_v11_t n, sts_event_v11_t *event);
	int readEventPayload(sts_evseqno_v11_t n, size_t len, void* buf);
	int writeEvent(sts_evseqno_v11_t n, sts_event_v11_t *event);
	int writeEventPayload(sts_evseqno_v11_t n, size_t len, void* buf);
	void delEvent(sts_evseqno_v11_t n);
	void delEventPayload(sts_evseqno_v11_t n);
};

#endif//_SAMPLEEVENTSERVER_H_
