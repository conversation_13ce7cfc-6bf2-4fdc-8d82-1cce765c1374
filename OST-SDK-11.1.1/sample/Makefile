# $Id: <PERSON><PERSON><PERSON>,v 1.22 2010/01/07 10:44:39 $
#***************************************************************************
#* $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $ *
#***************************************************************************

.SUFFIXES:	.cpp .MT.o .dep

###
# Comment out the platform you want to build and 
# Define the following variables to match your environment
#


## linuxR_x64 is for RH 2.6 kernel
#PLATFORM	= linuxR_x64
## linuxS_x64 is for SUSE 2.6 kernel
#PLATFORM	= linuxS_x64
## solaris is for  Solaris Sparc 64
#PLATFORM	= solaris 
## solaris_x64 is for  Solaris x64 
#PLATFORM	= solaris_x64
## hp_ux is for HP-UX PA-RISC
#PLATFORM	= hp_ux
## hpia64 is for HP-UX IA64
#PLATFORM	= hpia64
## rs6000 is for AIX
#PLATFORM	= rs6000


## Compilers
## Multi-platform compiler
#CPP		= g++
## Solaris compiler
#CPP		= CC
## HP-UX compiler
#CPP		= aCC
## AIX compiler
#CPP		= xlC

## MT Compilers
## Multi-platform compiler
#MT_CPP		= g++
## Solaris compiler
#MT_CPP		= CC
## HP-UX compiler
#MT_CPP		= aCC -mt -D_RWSTD_MULTI_THREAD -D_REENTRANT
## AIX compiler
#MT_CPP		= xlC_r


## Comment out the solaris extra if you are building
## Solaris independent of compiler
#SOL_EXTRA	= -z text

## CC_FLAGS For Solaris Sparc 64
#CC_PLAT	= -xarch=v9
## CC_FLAGS For Solaris x64
#CC_PLAT	= -xtarget=opteron -xarch=generic64

## Compile flags
#CC_FLAGS	= -KPIC $(CC_PLAT) ${SOL_EXTRA} 
#GCC_FLAGS	= -fPIC -m64 ${SOL_EXTRA}
#aCC_FLAGS	= -O -AA +DA1.1 +DS2.0 +Z
#aCC_IA64_FLAGS = -AA +DSitanium2 +DD64
#xlC_FLAGS	= -+ -qstaticinline -qfuncsect -qonce

## Linker flags for shared object
##GCC shared flags
#SOL_GCCOPT	= -Wl,-zdefs,-R/opt/sfw/lib
#SHARED_FLAGS = -shared ${SOL_GCCOPT}
##CC shared flags
#SHARED_FLAGS = -G -zdefs
##aCC shared flags
#SHARED_FLAGS = -b
##xlC shared flags
#SHARED_FLAGS = -G

## Libraries comment out the linker you are using
#CC_LIBS    = -lCrun -lCstd
#GCC_LIBS  = -lstdc++

###########################################################
##	STOP DO NOT CHANGE ANYTHING BELOW	###########
###########################################################

####### Down is platform independent...####################

SO_SUFFIX	= .so
STSSAMPLELIB	= libstspisampledisk${SO_SUFFIX}
STSSAMPLELIBMT	= libstspisamplediskMT${SO_SUFFIX}

CPPFLAGS	= ${CC_FLAGS} ${GCC_FLAGS} \
		 ${aCC_FLAGS} ${aCC_IA64_FLAGS} ${xlC_FLAGS} \
		 -DSTS_EXTERNAL_VENDOR \
		 ${GFLAGS} -DSAMPLE_PGN_EXPORTS

SRCS	= sampledisk.cpp \
	  SampleStorageServer.cpp \
	  SimpleLock.cpp \
	  AsyncTaskManager.cpp \
	  Common.cpp \
	  EventServer.cpp \
	  Exception.cpp

OBJS	= ${SRCS:.cpp=.o}
MT_OBJS	= ${SRCS:.cpp=.MT.o}

LDFLAGS = ${SHARED_FLAGS} ${CPPFLAGS}

LIBS = ${CC_LIBS} ${GCC_LIBS} -lc

MT_LIBS = -lpthread ${LIBS}

INCLUDES = -I. -I../src/include \
		-I../src/include/platforms/${PLATFORM}

all: ${SRCS} ${STSSAMPLELIB} ${STSSAMPLELIBMT}

${STSSAMPLELIB}: ${OBJS} 
	${CPP} ${LDFLAGS} -o $@ ${OBJS} ${MT_LIBS}

${STSSAMPLELIBMT}: ${MT_OBJS} 
	${MT_CPP} ${LDFLAGS} -o $@ ${MT_OBJS} ${MT_LIBS}

.cpp.o: ${SRCS}
	${CPP} ${CPPFLAGS} ${INCLUDES} -c $< -o $@

.cpp.MT.o: ${SRCS}
	${MT_CPP} ${CPPFLAGS} -DSTS_MT ${INCLUDES} -c $< -o $@

clean:
	rm -f *.o *.MT.o ${STSSAMPLELIB} ${STSSAMPLELIBMT}

