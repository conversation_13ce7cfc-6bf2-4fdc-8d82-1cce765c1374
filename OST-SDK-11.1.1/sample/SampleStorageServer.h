/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

#ifndef _SAMPLESTORAGESERVER_H_
#define _SAMPLESTORAGESERVER_H_
#define MAXPATHLEN 1024

#include "StorageServer.h"
#include "EventServer.h"
#include "SimpleLock.h"

/***********************************************************
 *
 * This is the SAMPLE plugin's implementation of "LSU" class
 *
 ***********************************************************/
class SampleLSU : public LSU
{
private:
	LSUInfo m_info;
	std::string m_lsu_path;

	void updateInfo();

public:
	/* constructor */
	SampleLSU(const char *name);

	LSUInfo * getInfo();
	int setLabel(std::string label_info);
	std::string getLabel();
	/* operations that can be invoked on Image */
	Image * createImage(ImageDefinition idef);
	Image * openImage(ImageDefinition idef, int mode);
	void deleteImage(ImageDefinition idef);
	std::vector<Image *> *getImageList();
};

/***********************************************************************
 *
 * This is the SAMPLE plugin's implementation of abstract "Image" class
 *
 ***********************************************************************/
class SampleImage : public Image
{
	/* We have set the LSU size = 1 GB to demostrate that 
	 * the image will be spanned over to another STU when LSU is full.
	 * This is just an example, LSUs can be of any size in the real world.  
	 * Please feel free to change it for your testing purposes.
	 */

#define LSU_SIZE	1073800000

private:
	ImageInfo m_info;

	void updateInfo();
public:
	int image_fd;
	std::string image_path;
	SampleLSU *m_lsu;

	/* constructors */
	SampleImage(SampleLSU *lsu, const char* lsupath, const char *basename, const char *date);
	SampleImage();
	~SampleImage();

	ImageInfo *getInfo();

	/* operations that can be invoked on LSU */
	int write(void *buf, size_t length, size_t offset, sts_uint64_t *num_written);
	size_t read(void *buf, size_t length, size_t offset);
	int remove();	

};

/*******************************************************************************
 *
 * This is the SAMPLE plugin's implementation of abstract "StorageServer" class
 *
 *******************************************************************************/

class SampleStorageServer : public StorageServer
{
private:
	StorageServerInfo m_info;
	std::vector<LSU *> *m_lsus;

	typedef struct sample_server_config_t{
		std::string name;
		std::string description;
		std::string type;
		std::string value;
		bool fixed;
	}sample_server_config_s;
	/* server configs, for V11 interface */
	std::map<std::string, sample_server_config_s> m_config;

	EventServer m_evsrv;
public:
	/* constructor-destructor */
	SampleStorageServer(std::string serverName);
	~SampleStorageServer();

	/* Overriding functions for StorageServer interface */
	StorageServerInfo * getInfo();
	std::vector<LSU *> *getLSUList();
	LSU* getLSU(LSUDefinition *ldef);

	/* for v11 api */
	int getConfig(char* buf, sts_uint32_t buflen, sts_uint32_t *maxlen);
	int setConfig(const char *buf, char *msgbuf, sts_uint32_t msgbuflen);
	int subscribeEvent(stsp_evc_handle_t eh);
	int unsubscribeEvent(stsp_evc_handle_t eh);
	int addEvent(sts_event_v11_t event);
	int addEvent(sts_event_v11_t event, void* payload);
	int getEvent(stsp_evc_handle_t eh, sts_event_v11_t *event);
	int getEventPayload(const sts_event_v11_t *event, void* buf);
	int delEvent(sts_event_v11_t *event);
};

#endif /* _SAMPLESTORAGESERVER_H_ */
