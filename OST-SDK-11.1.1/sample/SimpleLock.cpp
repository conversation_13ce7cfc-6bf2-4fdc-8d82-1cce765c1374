/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

#include "stspi.h"

#include "Exception.h"
#include "SimpleLock.h"

#ifndef WIN32
SimpleLock::SimpleLock()
{
	int ret = pthread_mutex_init(&m_hMutex, NULL);
	if(ret != 0)
		throw STSException("SimpleLock initialize failed", ret);
}

SimpleLock::~SimpleLock()
{
	int ret = pthread_mutex_destroy(&m_hMutex);
	if(ret != 0)
		throw STSException("SimpleLock release failed", ret);
}

void SimpleLock::lock()
{
	int ret = pthread_mutex_lock(&m_hMutex);
	if(ret != 0)
		throw STSException("SimpleLock Lock failed", ret);
}

void SimpleLock::unlock()
{
	int ret = pthread_mutex_unlock(&m_hMutex);
	if(ret != 0)
		throw STSException("SimpleLock Unlock failed", ret);
}

#else
SimpleLock::SimpleLock()
{
	InitializeCriticalSection(&m_hMutex);
}

SimpleLock::~SimpleLock()
{
	DeleteCriticalSection(&m_hMutex);
}

void SimpleLock::lock()
{
	EnterCriticalSection(&m_hMutex);
}

void SimpleLock::unlock()
{
	LeaveCriticalSection(&m_hMutex);
}
#endif

