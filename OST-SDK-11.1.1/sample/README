
		README 

       Sample OpenStorage Plugin 11.1.0


CONTENTS
----------------------------------------------------------------------
 - About the Sample Plugin
 - Building on Unix/Linux
 - Building on Windows
 - Installing
 - Configuration File
 - Create the LSU
 - Known Issues with pgntester
 - Recent Changes
 - Symantec Contacts


ABOUT THE SAMPLE PLUGIN
----------------------------------------------------------------------
This directory contains source for a reference C++ OpenStorage plugin.
The sample code does not contain source for a storage server; instead,
the plugin performs duties typically performed by the storage
server. The native file system is the storage system, where
directories are LSUs and images are stored in files.

The sample plugin's vendor prefix is 'sampledisk' (as defined in
SampleStorageServer.h).

A sample plugin is provided because all vendors expressed interest.
However, a sample plugin can do very little in assisting a vendor to
design their own plugin and storage server.  The API abstractions are
very high-level to accommodate many different vendor implementations;
therefore, it is not possible to provide a sample plugin that
realistically illustrates what a real vendor plugin will do.


BUILDING ON UNIX/LINUX
----------------------------------------------------------------------
The Makefile accommodates building on Linux or Solaris.  Edit the 
Makefile and uncomment the lines that apply to the desired platform.

Example:  (For SUSE Linux version 9)

###
# Comment out the platform you want to build and 
# Define the following variables to match your environment
#

## linux is for 2.4 kernel both RH and SUSE
#PLATFORM	= linux
## linuxR_x64 is for RH 2.6 kernel
#PLATFORM	= linuxR_x64
## linuxS_x64 is for SUSE 2.6 kernel
PLATFORM	= linuxS_x64
## solaris is for  Solaris Sparc
#PLATFORM	= solaris 
## solaris_x64 is for  Solaris x64 
#PLATFORM	= solaris_x64 

## Compilers
CPP		= gcc
#CPP		= CC

## Comment out the solaris extra if you are building
## Solaris independent of compiler
#SOL_EXTRA	= -z text

## Compile flags
#CC_FLAGS	= -KPIC ${SOL_EXTRA} 
GCC_FLAGS	= -fPIC ${SOL_EXTRA} 

## Linker flags for shared object (gcc == shared and CC == -G)
#SOL_GCCOPT	= -Wl,-zdefs,-R/opt/sfw/lib
SHARED_FLAGS = -shared ${SOL_GCCOPT}
#SHARED_FLAGS = -G -zdefs

## libraries comment out the linker you are using
#CC_LIBS    = -lCrun -lCstd
GCC_LIBS  = -lstdc++



The following plugin libraries will be built:
libstspisampledisk.so
libstspisamplediskMT.so


BUILDING ON WINDOWS
----------------------------------------------------------------------
A VisualStudio project has been provided for building on Windows.
The related files are sample.sln and sample.vcproj.

The following plugin library will be built:
libstspisampledisk.dll  (Windows)


INSTALLING
----------------------------------------------------------------------
Copy the plugin libraries to:
/usr/openv/lib/ost-plugins on POSIX platforms
C:\Program Files\VERITAS\NetBackup\bin\ost-plugins on Windows


CONFIGURATION FILE
----------------------------------------------------------------------
The plugin relies upon a configuration file to tell the plugin as to
what to simulate for a backend storage server, e.g., storage server
name and name of the LSUs.  We have included a sample config file
(config.txt).  Bear in mind that the storage server and volume names
are arbitrary, and are not "real" devices.

Therefore, in the following example:

STORAGE_SERVER=leafie
VOLUMES=vol1,vol2,vol3
STORAGE_SERVER=lettie
VOLUMES=vol4,vol5,vol6
REPLICATION=leafie:vol1->leafie:vol3
REPLICATION=leafie:vol1->lettie:vol4
REPLICATION=leafie:vol2->lettie:vol5

"leafie" - does not exist - it is not a machine name and could be any
string you wish, it is important to remember what you enter here because
you will have to use that name in other configuration steps. 
(this also applies to "lettie")

vol1,vol2,vol3 - do not exist - these volume names can be any string 
you wish.  These volumes map to directories that are automatically 
created in the /tmp/volumes or C:\Temp\volumes directory.  The LSUs are
represented by these directories and data will be stored within them.

leafie:vol1->leafie:vol3 - replication relationship, it means that lsu
"leafie:vol3" is the target lsu for replication.

The configuration file must be copied to:
/root/config.txt on POSIX platforms
C:\root\config.txt on Windows

Read the source file SampleStorageServer.cpp to learn more about the 
config file's format.


RECENT CHANGES
----------------------------------------------------------------------

Changes in 11.1.0:

1.  Support added for all of OST 11.1.0 STSPI functions.

Changes in 11.0.0:

1.  Support added for all of OST v11.0 STSPI functions and STS_EVT_OP event.

Changes in 10.0.0:

1.  Support added for OST v10.
2.  Example in sts_create_image on how to cast to the DPA specific
    isinfo_t

Changes in 9.4.3

1.  Support added for HP-UX PA-RISC, HP-UX IA64, and AIX


Changes in 9.4.2

1.  sts_copy_extent API added to sampledisk.
2.  Flags set to advertise support for Optimized Duplication.


Changes in 9.4.1

1.  stspi_read_image and stspi_write image now check to make sure
    the buflen and offset provided by the DPA (data protection 
    application) are multiples of STS_BLOCK_SIZE.
2.  Minor bug fixes.
3.  The LSU directory on Windows switched from
    C:\Program Files\VERITAS\NetBackup to C:\Temp


Changes in 9.4.0

1.  SampleStorageServer.h now imposes a 1 GB max LSU size.  This allows
    the ability to easily test LSU out-of-space handling. This can be
    configured by changing the value of LSU_SIZE in SampleStorageServer.h
2.  SampleStorageServer.cpp can now store a lable for the lsu inside
    a flat file which resides on the lsu.  sts_label_lsu API implemented
    in sampledisk.cpp.
3.  The plugin now demonstrates how to operate with back-level core 
    libraries.


Changes in 9.3.1:

Implemented several more APIs (e.g, stspi_open_image_list,
sts_close_image_list), made modifications to APIs (stspi_list_image,
stspi_delete_image).

Changes in 9.3.0:

Updated Makefile to define the STS_EXTERNAL_VENDOR macro.  This needs
to be defined by external vendors. The sample plugin, written
internally by Symantec, mistakenly neglected to consider the sample
source as being an 'external' sample.


SYMANTEC CONTACTS
----------------------------------------------------------------------
For questions about the sample plugin, send email to:

      <EMAIL>




