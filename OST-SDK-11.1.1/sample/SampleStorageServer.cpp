/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */
#include <iostream>
#include <sstream>
#include <algorithm>
#include "Common.h"
#include "SampleStorageServer.h"
#include "Exception.h"
#include "stsi.h"
#include "stsnbu.h"
#include "sampledisk.h"

#ifdef WIN32
#include <direct.h>
#include <io.h>
#include <windows.h>
#include <process.h>
#else
#include <unistd.h>
#endif
#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <errno.h>

using namespace std;

vector<LSU *> *create_lsu_list(const char *str, const char *delim);
int set_lsu_replication_prop(string server_name, vector<LSU *>* lsu_list, vector<string> replications);
char *readline(char *line, int size, FILE *fp);

/*********************************************************************************
 *
 *
 * SampleStorageServer - SampleDisk's implementation of StorageServer interface
 *
 * NOTE: The sample plugin does not deal with real stand-alone physical servers,
 * that is why it is termed server-less plugin. The implementation of the 
 * StorageServer interface creates 'StorageServer' objects in the local 
 * address space. In the real environment a plugin developer would create 
 * instances of the StorageServer object in remote address spaces and the plugin 
 * would communicate with those instances over some kind of RPC mechanism. 
 *
 * In one of our implementations of such server-based plugins we define the
 * StorageServer interface in the form of CORBA IDL and create Java/C++ instances 
 * of StorageServer object.
 *
 *
 **********************************************************************************/

SampleStorageServer::SampleStorageServer(string serverName):
		  m_lsus(NULL),m_evsrv(serverName)
{
	string::size_type pos = serverName.find(':');
	if(pos == string::npos) throw STSException("Invalid server name");
	string prefix = serverName.substr(0,pos);
	if(prefix != SAMPLE_PREFIX) throw STSException("Invalid server type");
	m_info.name = serverName.substr(pos+1,string::npos);
	
	/* 
	 * The sample plugin reads about the environment from a simple configuration file.
	 *
	 * NOTE: In actual plugins, vendors are expected to have their own discovery mechanisms
	 * to populate STS data structures. Since this is a generic sample plugin, we employ
	 * a simple configuration file approach.
	 *
	 * A sample configuration file looks like this:
	 *
	 * STORAGE_SERVER=france.symantec.com 
	 * VOLUMES=vol0,vol1,vol2,vol3 
	 * STORAGE_SERVER=italy.symantec.com 
	 * VOLUMES=vol0,vol1,vol2,vol3
	 *
	 * This config file is read in the constructor of SampleStorageServer object.
	 * It is at this time that the data structures of the plugin are populated with this
	 * configuration. In real plugins as well, constructor of this object could be used 
	 * for initializing the data structures with the information.
	 *
	 */

	/* 
	 *	NOTE: It is assumed that the config.txt file is kept at the following 
	 *      hard-coded path.  If you wish to build and play with the sample 
	 *      plugin, you would create a configuration file with the above mentioned 
	 *      contents and place it at the following path.
	 *      
	 *      WARNING: Plugins should not assume they can use the local file system
	 *      for storing information.
	 */
#ifndef WIN32
	FILE *config_fp = fopen("/root/config.txt","r");
#else
	FILE *config_fp = fopen("C:\\root\\config.txt", "r");
#endif
	if(config_fp == NULL) throw STSException("Error opening config.txt file");

	char line_str[1024];
	string line,key,value;
	int line_number=0;
	bool isThisMe = false;
	bool isServerFound = false;
	vector<string> replications;

	/* Read line by line */
	while(NULL != readline(line_str,1024,config_fp)){
		line_number++;

		if(strlen(line_str) <= 0) continue; /* Blank line */

		if(line_str[0] == '#') continue; /* comment */

		line = line_str;

		string::size_type assign_offset = line.find('=');
		if(assign_offset == string::npos) continue; /* ignore invalid line - best effort parsing */

		/* Parse single line and extract key and value */
		key = line.substr(0, assign_offset);
		value = line.substr(assign_offset+1, string::npos);

		/*
		 *	This instance of StorageServer object is per physical StorageServer.
		 *	The configuration file however contains the whole configuration. Therefore for each
		 *	StorageServer entry in the config file, its name is compared with the name passed to
		 *	this constructor (also set to m_info.name), if the name matches then isThisMe set
		 *	to true. This flag is later used for populating LSU(a.k.a. Volume) information. 
		 *
		 *	isServerFound is a flag to detect if a matching server was found at all. After going
		 *	through all the StorageServer entries in the config file, if isServerFound is
		 *	found to set false, then "StorageServer not found" exception can be thrown.
		 */
		if(key == "STORAGE_SERVER"){
			if(this->m_info.name == value){
				isThisMe = true;
				isServerFound = true;
			} else {
				isThisMe = false;
				continue;
			}
		}

		/* Populating LSU information */
		if(isThisMe && (key == "VOLUMES")){
			m_lsus = create_lsu_list(value.c_str(),",");
		}

		/* Get replication information */
		if(key == "REPLICATION"){
			replications.push_back(value);
		}
	}
	fclose(config_fp);

	/* Matching storage server name was not found in the configuration file */
	if(!isServerFound){
		throw STSException("StorageServer not found");
	}

	/* Set replication property */
	if(m_lsus)
		set_lsu_replication_prop(this->m_info.name, m_lsus, replications);

	/* Add some sample config for testing */
	sample_server_config_s cfg;
	cfg.name = "config1";
	cfg.description = "sample config 1";
	cfg.type = "string";
	cfg.value = "sample1";
	cfg.fixed = true;
	m_config[cfg.name] = cfg;
	cfg.name = "config2";
	cfg.description = "sample config 2";
	cfg.type = "int";
	cfg.value = "1";
	cfg.fixed = false;
	m_config[cfg.name] = cfg;

	/* Add some sample stored event for testing */
	if (0) {
		sts_event_v11_t event;

		if (0) {
			/* inline payload - STS_EVT_SRV_STAT */
			event.ev_flags = STS_EVF_INLINE;
			event.ev_type.evt_inline = STS_EVT_SRV_STAT;
			event.ev_body.eb_srv_stat.e_action = STS_ESRV_ONLINE;
			strncpy(event.ev_body.eb_srv_stat.e_name, m_info.name.c_str(), sizeof(event.ev_body.eb_srv_stat.e_name)); 
			addEvent(event);
		}

		{
			/* detached payload - STS_EVT_SRV_INFO */
			sts_ev_srv_info_v11_t srv_info;
			event.ev_flags = 0;
			event.ev_type.evt_detached.ed_type = STS_EVT_SRV_INFO;
			event.ev_type.evt_detached.ed_len = sizeof(srv_info);
			event.ev_type.evt_detached.ed_filler = 0;
			srv_info.e_fields = STS_EVT_SRV_INFO_MAXCONNECT;
			srv_info.e_info.srv_maxconnect = 777;
			addEvent(event, &srv_info);
		}

		if (0) {
			/* detached payload - STS_EVT_IMG_DUP */
			int num_images = 10;
			size_t payload_len = sizeof(sts_ev_image_dup_v11_t) + (num_images-1)*(sizeof(sts_image_info_v10_t));

			/* STS_EVT_IMG_DUP event delivered via stsp_get_event() */
			event.ev_flags = 0;
			event.ev_type.evt_detached.ed_type = STS_EVT_IMG_DUP;
			event.ev_type.evt_detached.ed_len = payload_len;
			event.ev_type.evt_detached.ed_filler = 0;

			/* STS_EVT_IMG_DUP event payload delivered via stsp_get_event_payload() */
			sts_ev_image_dup_v11_t *ev_dup = (sts_ev_image_dup_v11_t*) malloc(payload_len);
			memset(ev_dup, 0, payload_len);

			strcpy(ev_dup->e_orig_server,                     "v_orig_server_");
			ev_dup->e_num_images = num_images;

			sts_image_info_v10_t *ev_image;
			stsnbu_isinfo_t		*ev_isinfo;

			/* image list */
			for (int ix=0; ix<ev_dup->e_num_images; ix++) 
			{
				ev_image = &(ev_dup->e_imglist[ix]);
				ev_isinfo = (stsnbu_isinfo_t*) &(ev_image->imo_def.img_isid.is_info[0]);

				/* imo_def members are origin-based */
				strcpy(ev_isinfo->isi_slpname,                "din_slpname");		  /* slp name as defined by origin master */
				strcpy(ev_isinfo->isi_bckpid.bi_master_server,"din_bi_master");		  /* origin/source master */

				strcpy(ev_image->imo_def.img_isid.is_dpaid,   "dis_dpaid_");
				strcpy(ev_image->imo_def.img_isid.is_dpaver,  "dis_dpaver_");

				sprintf(ev_image->imo_def.img_basename,       "dxid_image basename_%d_", ix);

				/* other imo_* members are target-based */
				strcpy(ev_image->imo_server,                  "dii_server_");		  /* replication/destination master */
				sprintf(ev_image->imo_lsu.sln_name,           "dxii_lsu name_%d_", ix);
			}

			addEvent(event, (void*)ev_dup);
			free (ev_dup);
		}

	} /* event test */

}

SampleStorageServer::~SampleStorageServer()
{
	/*delete the lsu list*/
	if(NULL != m_lsus)
	{
		vector<LSU *>::size_type size = m_lsus->size();
		for(vector<LSU *>::size_type i = 0 ; i < size ; ++i)
		{
			delete (*m_lsus)[i]; 
		}
		delete m_lsus;
	}

}

StorageServerInfo *
SampleStorageServer::getInfo()
{
	return &m_info;
}

vector<LSU *> *
SampleStorageServer::getLSUList()
{
	return m_lsus;
}

LSU *
SampleStorageServer::getLSU(LSUDefinition *ldef)
{
	if(ldef == NULL) return NULL;
	if(m_lsus == NULL) return NULL;
	vector<LSU *>::size_type size = m_lsus->size();
	for(vector<LSU *>::size_type  i = 0 ; i < size ; ++i){
		if((*m_lsus)[i]->getInfo()->ldef.name == ldef->name){
			return (*m_lsus)[i];
		}
	}
	return NULL;
}

int
SampleStorageServer::getConfig(char* buf, sts_uint32_t buflen, sts_uint32_t *maxlen)
{
	ostringstream ost;
	std::map<std::string, sample_server_config_s>::iterator it;
	/* server config parameter string as follows:
	 * name=...,description=...,type=...,value=...,fixed=y|n;
	 */
	for(it = m_config.begin(); it != m_config.end(); ++it){
		sample_server_config_s& cfg = it->second;
		if(it != m_config.begin()){
			ost<<";";
		}
		ost<<"name="<<cfg.name<<",";
		ost<<"description="<<cfg.description<<",";
		ost<<"type="<<cfg.type<<",";
		ost<<"value="<<cfg.value<<",";
		ost<<"fixed="<<(cfg.fixed?"y":"n");
	}
	sts_uint32_t slen = (sts_uint32_t)ost.str().size()+1;
	if(buflen>slen){
		strcpy(buf, ost.str().c_str());
		return STS_EOK;
	}
	else{
		/* buflen is less than configuration string's */
		*maxlen = slen;
		return STS_ENOSPC;
	}
}

int
SampleStorageServer::setConfig(const char *buf, char *msgbuf, sts_uint32_t msgbuflen)
{
	ostringstream ost;
	std::string str = buf, substr, name, value;
	size_t pos = 0, pos1;
	do{
		/* split configuration parameters */
		pos1 = str.find(";", pos);
		if(pos1 != std::string::npos){
			substr = str.substr(pos, pos1-pos);
			pos = pos1 + 1;
		}
		else{
			substr = str.substr(pos);
			pos = std::string::npos;
		}

		if(substr.size() == 0)
			continue;

		/* split the parameter name and value */
		pos1 = substr.find("=");
		if(pos1 == std::string::npos || pos1 == 0){
			ost<<"parameter error: \""<<substr<<"\"";
#ifndef WIN32
			snprintf(msgbuf, msgbuflen, ost.str().c_str());
#else
			_snprintf(msgbuf, msgbuflen, ost.str().c_str());
#endif
			return STS_EINVAL;
		}

		name = substr.substr(0, pos1);
		value = substr.substr(pos1+1);

		if(m_config.find(name) != m_config.end()){
			if(m_config[name].fixed){
				ost<<"option \""<<name<<"\" is fixed, cannot be modifyed";
#ifndef WIN32
				snprintf(msgbuf, msgbuflen, ost.str().c_str());
#else
				_snprintf(msgbuf, msgbuflen, ost.str().c_str());
#endif
				return STS_EINVAL;
			}
		}
		else{
			ost<<"option \""<<name<<"\" is not supported";
#ifndef WIN32
			snprintf(msgbuf, msgbuflen, ost.str().c_str());
#else
			_snprintf(msgbuf, msgbuflen, ost.str().c_str());
#endif
			return STS_EINVAL;
		}
		m_config[name].value = value;

	}while(pos != std::string::npos);
	return STS_EOK;
}

int
SampleStorageServer::subscribeEvent(stsp_evc_handle_t eh)
{
	return m_evsrv.subscribeEvent(eh);
}

int
SampleStorageServer::unsubscribeEvent(stsp_evc_handle_t eh)
{
	return m_evsrv.unsubscribeEvent(eh);
}

int
SampleStorageServer::addEvent(sts_event_v11_t event)
{
	/* the storage server choose STORED event mode. */
	event.ev_flags |= STS_EVF_STORED;
	
	return m_evsrv.addEvent(event);
}

int
SampleStorageServer::addEvent(sts_event_v11_t event, void* payload)
{
	/* the storage server choose STORED event mode. */
	event.ev_flags |= STS_EVF_STORED;
	
	return m_evsrv.addEvent(event, payload);
}

int
SampleStorageServer::getEvent(stsp_evc_handle_t eh, sts_event_v11_t *event)
{
	int ret = m_evsrv.getEvent(eh, event);

	/* fill server name into ev_server */
	strncpy(event->ev_server, (string(SAMPLE_PREFIX)+":"+m_info.name).c_str(), sizeof(event->ev_server));

	return ret;
}

int
SampleStorageServer::getEventPayload(const sts_event_v11_t *event, void* buf)
{
	return m_evsrv.getEventPayload(event, buf);
}

int
SampleStorageServer::delEvent(sts_event_v11_t *event)
{
	return m_evsrv.delEvent(event);
}

/*
 * Utility functions for parsing
 */

/*
 *	Given a string with list of LSUs delimited by a special characher (',')
 *	this function returns a vector of LSU objects for those LSUs.
 */
#define MAX_NUM_TOKENS 20
vector<LSU *> * 
create_lsu_list(const char *str, const char *delim)
{
	int i = 0;
	if(str == 0 || delim == 0)
	{
		return 0;
	}
	string lsuNames = str;
	vector<LSU *> *lsu_list = new vector<LSU *>(MAX_NUM_TOKENS);
	string::size_type pos = lsuNames.find(delim);
	while(pos != string::npos){
		string name = lsuNames.substr(0, pos);
		(*lsu_list)[i] = new SampleLSU(name.c_str());
		lsuNames = lsuNames.substr(pos + 1);
		++i;
		pos = lsuNames.find(delim);
	}
	if(!lsuNames.empty())
	{
		(*lsu_list)[i++] = new SampleLSU(lsuNames.c_str());
	}
	lsu_list->resize(i);

	return lsu_list;
}

/*
 * Set lsu replication property
 */
int
set_lsu_replication_prop(string server_name, vector<LSU *>* lsu_list, vector<string> replications)
{
	int i,j;
	for(i = 0; i < replications.size(); i++){
		string replication = replications[i];
		/* string format like "srcserver:lsu1->targetserver:lsu3" */
		const char* delim = "->";
		string::size_type pos = replication.find(delim);
		if(pos != string::npos){
			string source = replication.substr(0, pos);
			string target = replication.substr(pos+strlen(delim));
			if(source == target || source.empty() || target.empty())
				continue;

			/* match the related lsu */
			for(j = 0; j < lsu_list->size(); j++){
				sts_lsu_spec_v11_t lsu_spec;
				LSUInfo* info = (*lsu_list)[j]->getInfo();

				string name = server_name + ":" + info->ldef.name;
				if(name == source){
					/* It is a source lsu for replicating */
					pos = target.find(":");
					if(pos == string::npos)
						continue;
					string server = target.substr(0, pos);
					string lsu = target.substr(pos + 1);
#ifndef WIN32
					snprintf( lsu_spec.ls_server, sizeof(lsu_spec.ls_server), "%s:%s", SAMPLE_PREFIX, server.c_str());
#else
					_snprintf( lsu_spec.ls_server, sizeof(lsu_spec.ls_server), "%s:%s", SAMPLE_PREFIX, server.c_str());
#endif
					strncpy( lsu_spec.ls_lsu.sln_name, lsu.c_str(), sizeof(lsu_spec.ls_lsu.sln_name));

					/* set lsu replication flag and property */
					info->ldef.lsu_flags |= STS_LSUF_REP_ENABLED;
					info->ldef.lsu_flags |= STS_LSUF_REP_SOURCE;
					info->ldef.lsu_rep_targets += 1;
					info->target_lsu.push_back(lsu_spec);
				}
				if(name == target){
					/* It is a target lsu for replicating */
					pos = target.find(":");
					if(pos == string::npos)
						continue;
					string server = source.substr(0, pos);
					string lsu = source.substr(pos + 1);
#ifndef WIN32
					snprintf( lsu_spec.ls_server, sizeof(lsu_spec.ls_server), "%s:%s", SAMPLE_PREFIX, server.c_str());
#else
					_snprintf( lsu_spec.ls_server, sizeof(lsu_spec.ls_server), "%s:%s", SAMPLE_PREFIX, server.c_str());
#endif
					strncpy( lsu_spec.ls_lsu.sln_name, lsu.c_str(), sizeof(lsu_spec.ls_lsu.sln_name));

					/* set lsu replication flag and property */
					info->ldef.lsu_flags |= STS_LSUF_REP_ENABLED;
					info->ldef.lsu_flags |= STS_LSUF_REP_TARGET;
					info->ldef.lsu_rep_sources += 1;
					info->source_lsu.push_back(lsu_spec);
				}
			}
		}
	}
	return 0;
}

/*
 * Wrapper function to read from file and do some basic error checking
 */
char *
readline(char *line, int size, FILE *fp)
{
	char *buf;
	char *res;

	buf = new char[size];

	res = fgets(buf,size,fp);

	if(NULL == res){
		delete[] buf;
		return NULL;
	} else {
		for(int i=0,j=0; i<size; i++){
			if(!isspace(buf[i])){
				line[j++] = buf[i];
			}
		}
		delete[] buf;
		return res;
	}
}


/********************************************************** 
 *
 * SampleLSU - SampleDisk's implementation of STS LSU interface
 *
 ***********************************************************/

SampleLSU::SampleLSU(const char *name)
{
	m_info.lsu_capacity_phys = LSU_SIZE;
	m_info.lsu_capacity = m_info.lsu_capacity_phys;
	m_info.lsu_used_phys = m_info.lsu_used;
	m_info.num_images = 0;

	m_info.ldef.name = name;
	m_info.ldef.max_transfer = 65536;
	m_info.ldef.block_size = STS_BLOCK_SIZE;
	
	m_info.ldef.lsu_flags = STS_LSUF_DISK|STS_LSUF_ACTIVE;
	m_info.ldef.lsu_rep_targets = 0;

	/*
	 * The SAMPLE storage sits on the local disk.
	 * The LSU name tells the location of storage on the disk - /tmp/volumes/<LSU_name>
	 * Images created on this LSU are files inside the /tmp/volumes/<LSU_name> directory
	 */
	m_lsu_path = commGenStorePath("volumes", m_info.ldef.name);
}

LSUInfo *
SampleLSU::getInfo()
{
	updateInfo();
	return &m_info;
}

int 
SampleLSU::setLabel(string label_info)
{
	const char *who = "SampleStorageServer::setLabel";
	/*
	 * setLabel would create a flat file on the disk and store the Label of the LSU.
	 * The file would be created at the same location as the image files over the LSU.
	 */
	
	string label_file_name = m_lsu_path + m_info.ldef.name + "label.txt";

	int label_fd =
	#ifndef WIN32
		open(label_file_name.c_str(), O_CREAT | O_TRUNC | O_RDWR, S_IRWXU );			
	#else
		_open(label_file_name.c_str(),_O_CREAT | _O_TRUNC | _O_RDWR | _O_BINARY, _S_IREAD | _S_IWRITE );
	#endif
	if (label_fd <0)
	{
		if(errno == EACCES)
			throw STSException("Permission denied for writing label",STS_EPERM);
		else {
			ostringstream ost;
			ost << "Unknown error while writing label - Error ID " << errno;
			throw STSException(ost.str().c_str());
		}
	}

	int length = label_info.size();

	int bytes_written =
	#ifndef WIN32
		write(label_fd, label_info.c_str(), length);
	#else
		_write(label_fd, label_info.c_str(), length);
	#endif
	if(bytes_written == -1) {
		ostringstream ost;
		ost << "Error while writing label: errno " << errno;
	  	throw STSException(ost.str().c_str(),STS_EINTERNAL);
	}

	return 0;
}

std::string 
SampleLSU::getLabel()
{
	const char *who = "SampleStorageServer::getLabel";

	/*
	 * getLabel would read the LSU label from flat file on the disk.
	 */
	string label_file_name = m_lsu_path + m_info.ldef.name + "label.txt";
	
	FILE* label_fd = fopen(label_file_name.c_str(), "r");
	if (label_fd == NULL)
	{
		if(errno == EACCES)
			throw STSException("Permission denied for reading label",STS_EPERM);
		else if(errno == ENOENT){
			string blank = " ";
			return blank;
		}
		else{
			ostringstream ost;
			ost << "Unknown error while reading label - Error ID " << errno;
			throw STSException(ost.str().c_str());
		}
	}
	
	char line_read[1024];
	string line, label_key, label_value;
	int line_number = 0;
	int assign_offset = 0;
	int label_found = 0;

	/*Read the file line by line and look for the label*/
	while(NULL != readline(line_read, 1024, label_fd)){
                line_number++;

                if(strlen(line_read) <= 0) continue; /*Blank line*/
                if(line_read[0] == '#') continue; /*comments*/

                line = line_read;

                assign_offset = line.find(':');
                if(assign_offset == string::npos) continue;

                label_key = line.substr(0, assign_offset);
                label_value = line.substr(assign_offset+1, string::npos);
                if(label_key == "LSU_Label"){
			label_found = 1;
			break;
		}

	}

	if(label_found)
		return label_value;
	else
		throw STSException("Label could not be found in the file",STS_EINVAL);
}

Image *
SampleLSU::createImage(ImageDefinition idef)
{
	const char *who = "SampleStorageServer::createImage";

	/*
	 * createImage is supposed to create a new image on the storage.
	 * If an image with the same definition already exists in this LSU then that will
	 * be an error condition
	 */
	
	/*
	 *	All the fields of Image definition structure define a unique image.
	 *	In this sample plugin, we concatenate all these fields in forming the name of the
	 *	file with which we store this image.
	 */
	string image_file_name = m_lsu_path + idef.basename + "_" + idef.date + ".img";

	SampleImage *pimg = new SampleImage();
	pimg->m_lsu = this;
	pimg->image_path = m_lsu_path;
	
	/* Create a new image file */
	if((pimg->image_fd = 
	#ifndef WIN32
			open(image_file_name.c_str(),O_CREAT | O_EXCL | O_RDWR, S_IRWXU )) < 0) 
	#else
			_open(image_file_name.c_str(),_O_CREAT | _O_EXCL | _O_RDWR | _O_BINARY, _S_IREAD | _S_IWRITE )) < 0)
	#endif
	{
		delete pimg;
		if(errno == EEXIST){
			throw STSException((string(image_file_name)+" already exists").c_str(),
								 STS_EEXIST);
		} else if(errno == EACCES){
			throw STSException("Permission denied for creating image",STS_EPERM);
		} else {
			ostringstream ost;
			ost << "Unknown error while creating image - Error ID " << errno;
			throw STSException(ost.str().c_str());
		}
	}

	/* write image def to file head */
	int num_written = 
#ifndef WIN32
	::write(pimg->image_fd,&idef.buf,sizeof(idef.buf));
#else
	_write(pimg->image_fd,&idef.buf,sizeof(idef.buf));
#endif
	if(num_written != sizeof(idef.buf)){
		ostringstream ost;
		ost << "Error in write: errno " << errno << ", num_written " << num_written;
		throw STSException(ost.str().c_str(),STS_EINTERNAL);
	}

	return pimg;
}

Image *
SampleLSU::openImage(ImageDefinition idef, int mode)
{
	/*
	 *	openImage is supposed to open handle to existing image present on the storage.
	 *	If the image already exists then it is not an error condition.
	 */
	string image_file_name = m_lsu_path + idef.basename + "_" + idef.date + ".img";

	SampleImage *pimg = new SampleImage();
	pimg->m_lsu = this;
	pimg->image_path = m_lsu_path;

	int flags = 0;
	if(mode == STS_O_WRITE){
#ifdef WIN32
		flags = _O_RDWR | _O_BINARY;
#else
		flags = O_EXCL | O_RDWR;
#endif		
	} else {
#ifdef WIN32
		flags = _O_RDONLY | _O_BINARY;
#else
		flags = O_RDONLY;
#endif
	}

	if((pimg->image_fd = 
#ifndef WIN32
	open(image_file_name.c_str(), flags )) < 0) 
#else
	_open(image_file_name.c_str(), flags )) < 0)
#endif
	{
		if(errno == EEXIST){
			/* that's right */
		} else if(errno == EACCES){
			delete pimg;
			throw STSException("Permission denied for creating image",STS_EPERM);
		} else if(errno == ENOENT){
			delete pimg;
			throw STSException((string(image_file_name)+" could not be found").c_str(),
				STS_ENOENT);
		} else {
			ostringstream ost;
			ost << "Unknown error while creating image - Error ID " << errno;
			delete pimg;
			throw STSException(ost.str().c_str(),STS_EINTERNAL);
		}
	}

	return pimg;
}

void 
SampleLSU::deleteImage(ImageDefinition idef)
{
	const char *who = "SampleStorageServer::deleteImage";

	/*
	 *	deleteImage is supposed to remove an existing image present on the storage.
	 *	If the image already exists then it is not an error condition.
	 */
	errno = 0;

	string image_file_name = m_lsu_path + idef.basename + "_" + idef.date + ".img";

	/* Remove an image file */
	if(
	#ifndef WIN32
			remove(image_file_name.c_str())  < 0)
	#else
			remove(image_file_name.c_str())  < 0)
	#endif
	{
		if(errno == ENOENT){
			throw STSException((string(image_file_name)+" could not be found").c_str(),STS_EPERM);
		} else if(errno == EACCES){
			throw STSException((string("Permission denied for deleting image ")+image_file_name).c_str(),STS_EPERM);
		} else {
			ostringstream ost;
			ost << "Unknown error while deleting image - Error ID " << errno;
			throw STSException(ost.str().c_str(), STS_EINTERNAL);
		}
	}
}


vector<Image *> *
SampleLSU::getImageList()
{

	const char *who = "SampleStorageServer::getImageList";

	/*
	 *	getImageList looks in the LSU directory and gets a list of all files.
	 *	If the files has a .img suffix, it is considered an image and added
	 *	to the image list.
	 */

    vector <std::string> files;
    vector <Image *> *images = new vector<Image *>;



	/*
	 *	Discover all files in the LSU
	*/

#ifndef WIN32

    DIR *dp;
    struct dirent *dir_entry;
    struct stat stat_info;
    char cwd[MAXPATHLEN] = {0};

    if((dp = opendir(m_lsu_path.c_str())) == NULL) {
        delete images;
        return 0;
    }

    // you have to be in the dir in order to get correct stat_info
    getcwd(cwd, sizeof(cwd));
    chdir(m_lsu_path.c_str());
    while((dir_entry = readdir(dp)) != NULL) {
        lstat(dir_entry->d_name,&stat_info);

        if(S_ISDIR(stat_info.st_mode)) {							//ignore sub directories
            continue;
        }
        else {														//add file name to list of files
            files.push_back(string(dir_entry->d_name));
        }
    }

    chdir(cwd);
    closedir(dp);

#else

    BOOL            fFinished;
    HANDLE          hList;
    TCHAR           szDir[MAX_PATH+1];
    WIN32_FIND_DATA FileData;

    // Get the proper directory path
    _snprintf(szDir, sizeof(szDir), "%s*", m_lsu_path.c_str());

    // Get the first file
    hList = FindFirstFile(szDir, &FileData);
    if (hList != INVALID_HANDLE_VALUE){
        // Traverse through the directory structure
        fFinished = FALSE;
        while (!fFinished)
        {
            // Check the object is a directory or not
            if (!(FileData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY)) {	//ignore sub directories
                files.push_back(FileData.cFileName);						//add file name to list of files
            }

            if (!FindNextFile(hList, &FileData))
            {
                fFinished = TRUE;
            }
        }
    }

    FindClose(hList);

#endif

	// No Image files found
	if (files.size() == 0) return images;


	/*
	 *	Determine which files are images
	 */
	string::size_type dateSep;
	string::size_type suffixSep;
	string basename;
	string date;

	//look for .img files
	std::vector<std::string>::iterator filesIterator;
	for(filesIterator = files.begin(); filesIterator != files.end(); filesIterator++)
	{
		string file = *(filesIterator);
		if( (suffixSep = file.rfind(".img")) != string::npos){
			//parse the file name for the basename and date
			dateSep = file.rfind("_");
			basename = file.substr(0, dateSep);
			date = file.substr(dateSep+1, suffixSep - (dateSep+1));

			images->push_back(new SampleImage(this, m_lsu_path.c_str(), basename.c_str(), date.c_str()));
		}

	}

	return images;
}

void
SampleLSU::updateInfo()
{
	//Find out the total size of all the images on LSU.
	const char *who = "SampleLSU::updateInfo";
	size_t lsu_used = 0;
	size_t lsu_images = 0;
	
#ifndef WIN32
	DIR *dp;
	struct dirent *dir_entry;
	struct stat stat_info;
	char cwd[MAXPATHLEN] = {0};

	if((dp = opendir(m_lsu_path.c_str()))) {
		// you have to be in the dir in order to get correct stat_info
		getcwd(cwd, sizeof(cwd));
		chdir(m_lsu_path.c_str());
		while((dir_entry = readdir(dp)) != NULL) {
			if(strstr(dir_entry->d_name, ".img") == 0)
				continue;
			lstat(dir_entry->d_name,&stat_info);

			if(S_ISDIR(stat_info.st_mode))  //ignore sub directories
				continue;
			else{
				lsu_used += stat_info.st_size;
				lsu_images++;
			}
		}

		chdir(cwd);
		closedir(dp);
	}

#else
	BOOL            fFinished;
	HANDLE          hList;
	TCHAR           szDir[MAX_PATH+1];
	WIN32_FIND_DATA FileData;

	// Get the proper directory path
	_snprintf(szDir, sizeof(szDir), "%s*.img", m_lsu_path.c_str());

	// Get the first file
	hList = FindFirstFile(szDir, &FileData);
	if (hList != INVALID_HANDLE_VALUE){
		// Traverse through the directory structure
		fFinished = FALSE;
		while (!fFinished)
		{
			// Check the object is a directory or not
			if (!(FileData.dwFileAttributes & FILE_ATTRIBUTE_DIRECTORY))       //ignore sub directories
			lsu_used += (FileData.nFileSizeHigh * (MAXDWORD+1)) + FileData.nFileSizeLow;
			lsu_images++;
			
			if (!FindNextFile(hList, &FileData))
			{
				fFinished = TRUE;
			}
		}
	}
	FindClose(hList);
#endif

	m_info.num_images = lsu_images;
	m_info.lsu_used_phys = lsu_used;
	m_info.lsu_used = m_info.lsu_used_phys;
}

/*************************************************************
 *
 * SampleImage - SampleDisk's implementation of STS Image interface 
 *
 *************************************************************/

SampleImage::SampleImage(SampleLSU *lsu, const char* lsupath, const char *basename, const char *date) : image_fd(-1)
{
	m_lsu = lsu;
	image_path = lsupath;
	m_info.idef.basename = basename;
	m_info.idef.date = date;
	m_info.idef.size = 0;

	/* update Image Info */
	string image_file_name = (std::string)lsupath + basename + "_" + date + ".img";
	if((image_fd = 
#ifndef WIN32
		open(image_file_name.c_str(), O_RDONLY )) < 0) 
#else
		_open(image_file_name.c_str(), _O_RDONLY | _O_BINARY )) < 0)
#endif
	{
		if(errno == EEXIST){
			/* that's right */
		} else if(errno == EACCES){
			throw STSException("Permission denied for creating image",STS_EPERM);
		} else if(errno == ENOENT){
			throw STSException((string(image_file_name)+" could not be found").c_str(),
				STS_ENOENT);
		} else {
			ostringstream ost;
			ost << "Unknown error while creating image - Error ID " << errno;
			throw STSException(ost.str().c_str(),STS_EINTERNAL);
		}
	}
	updateInfo();
#ifndef WIN32
	close(image_fd);
#else
	_close(image_fd);
#endif
	image_fd = -1;
}


SampleImage::SampleImage() : image_fd(-1)
{
}

Image::~Image()
{
}

SampleImage::~SampleImage()
{
	if(image_fd != -1){
#ifndef WIN32
		close(image_fd);
#else
		_close(image_fd);
#endif
	}
}

void
SampleImage::updateInfo()
{
	struct stat stat;
	if(image_fd != -1){
		fstat(image_fd, &stat);
		if(stat.st_size < sizeof(m_info.idef.buf)){
			throw STSException("updateInfo: Invalid Image File");
		}
		m_info.idef.size = stat.st_size - sizeof(m_info.idef.buf);

#ifndef WIN32
		lseek(image_fd,0,SEEK_SET);
#else
		_lseek(image_fd,0,SEEK_SET);
#endif

		size_t num_read = 
#ifndef WIN32
			::read(image_fd,&m_info.idef.buf,sizeof(m_info.idef.buf));
#else
			_read(image_fd,&m_info.idef.buf,sizeof(m_info.idef.buf));
#endif

		if(num_read != sizeof(m_info.idef.buf)) {
			ostringstream ost;
			ost << "updateInfo: Error in read: errno " << errno << ", num_read " << num_read;
			throw STSException(ost.str().c_str(),STS_EINTERNAL);
		}
	}
}

ImageInfo *
SampleImage::getInfo()
{
	updateInfo();
	return &m_info;
}

int 
SampleImage::write(void *buf,
	size_t length,
	size_t offset,
	sts_uint64_t *num_written)
{
	const char *who = "SampleImage::write";
	size_t lsu_used;
	int status;
	
	if(image_fd == -1) throw STSException("FileDescriptor is NULL",STS_EINTERNAL);

	/* skip file head */
	offset += sizeof(m_info.idef.buf);

	int new_offset = 
#ifndef WIN32
	lseek(image_fd,offset,SEEK_SET);
#else
	_lseek(image_fd,offset,SEEK_SET);
#endif

	if(new_offset != offset) throw STSException("Error in lseek",STS_EINTERNAL);

	lsu_used = m_lsu->getInfo()->lsu_used;

	//Check if LSU has space available to write the image.
	if(lsu_used + length > LSU_SIZE){
		length = LSU_SIZE - lsu_used;
		status = STS_ENOSPC;
	}
	else 
		status = STS_EOK;

	*num_written = 
#ifndef WIN32
	::write(image_fd,buf,length);
#else
	_write(image_fd,buf,length);
#endif
	if(*num_written == -1) {
		ostringstream ost;
		ost << "Error in write: errno " << errno;
	  	throw STSException(ost.str().c_str(),STS_EINTERNAL);
	}
	if(*num_written < length) {
		throw STSException("Not all bytes were written",STS_EINTERNAL);
	}
	return status;
}

size_t
SampleImage::read(void *buf,
	size_t length,
	size_t offset)
{
	const char *who = "SampleImage::read";

	if(image_fd == -1) throw STSException("FileDescriptor is NULL",STS_EINTERNAL);

	/* skip file head */
	offset += sizeof(m_info.idef.buf);

	int new_offset = 
#ifndef WIN32
	lseek(image_fd,offset,SEEK_SET);
#else
	_lseek(image_fd, offset, SEEK_SET );
#endif
	if(new_offset != offset) throw STSException("Error in lseek",STS_EINTERNAL);

	size_t num_read = 
#ifndef WIN32
		::read(image_fd,buf,length);
#else
		_read(image_fd,buf,length);
#endif

	if(num_read == -1) {
		ostringstream ost;
		ost << "Error in read: errno " << errno;
	  	throw STSException(ost.str().c_str(),STS_EINTERNAL);
	}

	return num_read;
}

int
SampleImage::remove()
{
	return 0;
}

