/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

#include <sys/stat.h>
#include <sys/types.h>
#include <stdio.h>
#include <errno.h>

#include "Common.h"
#include "Exception.h"

#ifndef WIN32
#include <unistd.h>
#else
#include <io.h>
#include <direct.h>
#endif

void commSleep(int msec)
{
#ifndef WIN32
		usleep(msec*1000);
#else
		Sleep(msec);
#endif
}

int commAccess(const char* path, int mode)
{
#ifndef WIN32
	return access(path, mode);
#else
	return _access(path, mode);
#endif
}

int commThreadCreate(comm_thread_func_t func, void* args, comm_thread_t* thread_handle)
{
	int ret = 0;
#ifndef WIN32
	ret = pthread_create(thread_handle, NULL, func, args);
#else
	*thread_handle = (HANDLE)_beginthreadex(NULL, NULL, func, args, 0, NULL);
	if(*thread_handle == (HANDLE)-1)
		ret = errno;
#endif
	return ret;
}

void commThreadWait(comm_thread_t thread_handle)
{
#ifndef WIN32
	pthread_join(thread_handle, NULL);
#else
	WaitForSingleObject(thread_handle, INFINITE);
	CloseHandle(thread_handle);
#endif
}

static void createPath(std::string path)
{
#ifndef WIN32
	if(mkdir(path.c_str(),S_IWUSR|S_IRUSR|S_IXUSR) != 0)
#else
	if(_mkdir(path.c_str())!= 0)
#endif
	{
#ifndef WIN32
		/* Ignore if the directory already exists */
		if(errno != EEXIST &&
				/* sometimes, in solaris, mkdir return -1 but errno return 0,
				 *so check the path manually */
				errno != 0 && access(path.c_str(), 0) != 0){
#else
		if(errno != EEXIST){
#endif
			throw STSException("Error creating storage directory");
		}
	}
}

std::string commGenStorePath(std::string serverName, std::string subPath)
{
	std::string path = BASE_DIR;
	createPath(path);
	path += serverName.substr(serverName.find(':')+1) + DIR_SEPARATOR;
	createPath(path);
	path += subPath + DIR_SEPARATOR;
	createPath(path);

	return path;
}

