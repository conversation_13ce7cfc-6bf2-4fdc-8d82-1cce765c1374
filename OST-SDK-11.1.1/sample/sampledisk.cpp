/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */
#include "stspi.h"
#include "stsnbu.h"
#include "Common.h"
#include "SampleStorageServer.h"
#include "sampledisk.h"
#include "Exception.h"
#include "AsyncTaskManager.h"

#include <iostream>
#include <sstream>

using namespace std;

/* 
 * NULL pointer check. Good to do before dereferencing it.
 */
#define VALIDATE(x) \
	if(x == NULL) return STS_EINVAL;


/*
 * Log the message with the callback function that comes in the session.
 */
#define LOG_ERROR(sd,msg) \
	ost << msg; \
	if(sd && sd->sd_log) sd->sd_log(sd->sd_id,ost.str().c_str(),STS_ESERROR); \
	ost.str("");	//clears the buffer

#define LOG_INFO(sd,msg) \
	ost << msg; \
	if(sd && sd->sd_log) sd->sd_log(sd->sd_id,ost.str().c_str(),STS_ESINFO); \
	ost.str("");	//clears the buffer


const sts_session_def_t *
SESSION_I(stsp_image_handle_t ih){
	if(ih) return ih->sd;
	return 0;
}

ostringstream ost;

#ifdef STS_MT 
API_Export int stspi_ismt; /* advertise we are a multi-threaded plugin */
#endif

static AsyncTaskManager *mngr = NULL;

/*
 * map server name to server handle, provide for delete event and get event payload.
 */
map<string, stsp_server_handle_t> evc_srvs;

int
stspi_async_read_image_v11(
		stsp_image_handle_t image_handle,
		void *buf,
		sts_uint64_t len,
		sts_uint64_t offset,
		stsp_opid_t *opid)
{
	VALIDATE(image_handle);
	VALIDATE(buf);
	VALIDATE(opid);

	/* create an async task for read image job*/
	ReadImageTask *task = new ReadImageTask(image_handle, buf, len, offset);
	*opid = reinterpret_cast<stsp_opid_t>(task);
	return mngr->addTask(task);
}

int
stspi_async_wait_v11(
		const sts_session_def_v7_t *sd, 
		stsp_opid_t opid,
		int blockflag, 
		sts_aioresult_v11_t *result)
{
	VALIDATE(sd);
	VALIDATE(opid);
	VALIDATE(result);
	Task* task = reinterpret_cast<Task*>(opid);

	return mngr->waitTaskResult(task, blockflag, result);
}

int
stspi_async_write_image_v11(
		stsp_image_handle_t image_handle,
		sts_stat_v7_t *stat,
		void *buf,
		sts_uint64_t len,
		sts_uint64_t offset,
		stsp_opid_t *opid)
{
	VALIDATE(image_handle);
	VALIDATE(buf);
	VALIDATE(opid);

	/* create an async task for write image job*/
	WriteImageTask *task = new WriteImageTask(image_handle, stat, buf, len, offset);
	*opid = reinterpret_cast<stsp_opid_t>(task);
	return mngr->addTask(task);
}

int
stspi_claim(
	const sts_server_name_t serverName)
{
	VALIDATE(serverName);

	string sn = serverName;

	/* Parse and extract the prefix from the serverName */
	string prefix = sn.substr(0,sn.find(':'));

	if(prefix == SAMPLE_PREFIX){
		/* Yes this is my prefix */
		return STS_EOK;
	} else {
		/* No I don't know this prefix */
		return STS_ECLAIM;
	}
}

int
stspi_close_evchannel_v9(
		stsp_evc_handle_t evc_handle)
{
	VALIDATE(evc_handle);
	VALIDATE(evc_handle->sh);

	/* unsubsribe event on storage server */
	evc_handle->sh->sts->unsubscribeEvent(evc_handle);

	delete evc_handle;
	return STS_EOK;
}

static int copy_image(const stsp_lsu_t* to_lsu,
		const sts_image_def_v10_t* to_img,
		stsp_image_handle_t from_ih,
		const sts_opname_v11_t imageset,
		int eventflag)
{
	int ret;

	/* get source image info */
	sts_image_info_v10_t imginfo;
	ret = stspi_get_image_prop_v10(from_ih, &imginfo);
	if(ret != STS_EOK)
		return ret;

	/* create target image */
	stsp_image_handle_t to_ih;
	ret = stspi_create_image_v10(to_lsu, to_img, 0, &to_ih);
	if(ret != STS_EOK)
		return ret;

	/* copy image data from source to target,
	 * in sample plugin, just simply call copy_extent to do this job */
	sts_uint64_t bytesCopied;
	ret = stspi_copy_extent(to_ih, 0, from_ih, 0, imginfo.imo_size, 0, &bytesCopied);
	if(ret == STS_EOK && imginfo.imo_size == bytesCopied){
		/* generate STS_EVT_IMG_DUP event */
		if(eventflag | STS_EVFLAG_TRIGGER){
			sts_event_v11_t event;
			sts_ev_image_dup_t *dup_info;
			vector<sts_image_def_v10_t> imgdef_list;
			size_t dup_info_size;

			if(strlen(imageset.op_name)){
				/* for duplication */
				mngr->getImageSet().wait(imageset);
				imgdef_list = mngr->getImageSet().getImageDefList(imageset);
			}
			else{
				/* for replication */
				sts_image_info_v10_t imginfo;
				ret = stspi_get_image_prop_v10(from_ih, &imginfo);
				imgdef_list.push_back(imginfo.imo_def);
			}
			/* send STS_EVT_IMG_DUP event to target lsu */
			dup_info_size = sizeof(sts_ev_image_dup_t) + sizeof(sts_image_info_v10_t)*imgdef_list.size();
			dup_info = (sts_ev_image_dup_t*)calloc(1, dup_info_size);

			event.ev_flags = 0;
			event.ev_type.evt_detached.ed_type = STS_EVT_IMG_DUP;
			event.ev_type.evt_detached.ed_len = dup_info_size;
			event.ev_type.evt_detached.ed_filler = 0;

			strncpy(dup_info->e_orig_server, from_ih->lsu.sl_server_handle->sts->getInfo()->name.c_str(), sizeof(dup_info->e_orig_server));
			dup_info->e_num_images = imgdef_list.size();
			size_t i = 0;
			for(i = 0; i < dup_info->e_num_images; i ++){
				stspi_get_image_prop_byname_v10(to_lsu, &(imgdef_list[i]), &(dup_info->e_imglist[i]));
			}
			to_lsu->sl_server_handle->sts->addEvent(event, dup_info);
			free(dup_info);
		}

		stspi_close_image(to_ih, STS_CLOSEF_IH_COMPLETE, 0);
	}
	else{
		stspi_close_image(to_ih, STS_CLOSEF_IH_INCOMPLETE, 0);
		stspi_delete_image_v10(to_lsu, to_img, 0);
	}

	return ret;
}

/* delete image on target lsu */
static int delete_image_on_replication_target(
		const stsp_lsu_t *lsu,
		const sts_image_def_v10_t *imageDefinition,
		int asyncFlag)
{
	int ret;

	/* get source lsu info */
	sts_lsu_info_v11_t lsuinfo;
	ret = stspi_get_lsu_prop_byname_v11(lsu, &lsuinfo);
	if(ret != STS_EOK)
		return ret;
	if(lsuinfo.lsu_def.sld_rep_targets == 0)
		return STS_EOK;

	/* get target lsu info */
	sts_lsu_spec_v11_t *lsu_specs = new sts_lsu_spec_v11_t[lsuinfo.lsu_def.sld_rep_targets];
	ret = stspi_get_lsu_replication_prop_v11(lsu, 0, NULL, lsuinfo.lsu_def.sld_rep_targets, lsu_specs);

	/* copy image to target lsu */
	sts_uint32_t i;
	for(i=0; i<lsuinfo.lsu_def.sld_rep_targets; i++){
		/* open target server */
		stsp_server_handle_t target_sh;
		ret = stspi_open_target_server(lsu->sl_server_handle, lsu_specs[i].ls_server, NULL, NULL, &target_sh);
		if(ret != STS_EOK)
			continue;

		stsp_lsu_t target_lsu;
		target_lsu.sl_server_handle = target_sh;
		target_lsu.sl_lsu_name = lsu_specs[i].ls_lsu;

		stspi_delete_image_v10(&target_lsu, imageDefinition, asyncFlag);
		stspi_close_server(target_sh);
	}
	return STS_EOK;

}

/* image replication
 * 1. get lsu info from image handle
 * 2. copy image to target
 * 3. if STS_EVFLAG_TRIGGER flag is set, send event to target
 */
static int copy_image_to_replication_target(
		const sts_image_def_v10_t *to_img,
		stsp_image_handle_t from_ih,
		const sts_opname_v11_t imageset,
		int eventflag)
{
	int ret;

	/* get source lsu info */
	sts_lsu_info_v11_t lsuinfo;
	ret = stspi_get_lsu_prop_byname_v11(&from_ih->lsu, &lsuinfo);
	if(ret != STS_EOK)
		return ret;
	if(lsuinfo.lsu_def.sld_rep_targets == 0)
		return STS_EOK;

	/* get target lsu info */
	sts_lsu_spec_v11_t *lsu_specs = new sts_lsu_spec_v11_t[lsuinfo.lsu_def.sld_rep_targets];
	ret = stspi_get_lsu_replication_prop_v11(&from_ih->lsu, 0, NULL, lsuinfo.lsu_def.sld_rep_targets, lsu_specs);

	/* copy image to target lsu */
	sts_uint32_t i;
	for(i=0; i<lsuinfo.lsu_def.sld_rep_targets; i++){
		/* open target server */
		stsp_server_handle_t target_sh;
		ret = stspi_open_target_server(from_ih->lsu.sl_server_handle, lsu_specs[i].ls_server, NULL, NULL, &target_sh);
		if(ret != STS_EOK)
			break;

		stsp_lsu_t target_lsu;
		target_lsu.sl_server_handle = target_sh;
		target_lsu.sl_lsu_name = lsu_specs[i].ls_lsu;

		ret = copy_image(&target_lsu, to_img, from_ih, imageset, eventflag);
		if(ret != STS_EOK)
			break;
		stspi_close_server(target_sh);
	}
	return ret;
}

int
stspi_close_image(
	stsp_image_handle_t ih, 
	int completeFlag, 
	int forceFlag)
{
	int ret = STS_EOK;
	VALIDATE(ih);

	/* replicate before image is closed */
	if(completeFlag == STS_CLOSEF_IH_COMPLETE){
		/* set imageset string to "\0" to replicate to target lsu */
		sts_opname_v11_t imageset = {0};
		ret = copy_image_to_replication_target(&ih->img_def, ih, imageset, STS_EVFLAG_TRIGGER|STS_EVFLAG_INCLUDE);
	}

	/* Free the memory for Image object */
	if(ih->iptr)
	{
	 	delete ih->iptr;
		ih->iptr = 0;
	}
	delete ih;
	return ret;
}

int 
stspi_close_image_list(
	stsp_image_list_handle_t image_list_handle)
{
	VALIDATE(image_list_handle);

	/* Free memory of private vector<Image *> object */
	if(image_list_handle->image_list){
		/* Free memory of each Image object in the list */
		for(int i=0; i<image_list_handle->image_list->size(); i++){
			delete (*(image_list_handle->image_list))[i];
		}

		delete image_list_handle->image_list;
		image_list_handle->image_list = NULL;
	}
	
	/* Free the memory of handle */
	delete image_list_handle;

	return STS_EOK;
}


int 
stspi_close_lsu_list(
	const stsp_lsu_list_handle_t lsuListHandle)
{
	VALIDATE(lsuListHandle);

	/* Free the memory of handle */
	delete lsuListHandle;

	return STS_EOK;
}

int 
stspi_close_server(
    stsp_server_handle_t sh)
{
	VALIDATE(sh);

	/* Free the StorageServer object */
	if(sh->sts){
		delete sh->sts;
		sh->sts = NULL;
	}
	return STS_EOK;
}


int 
stspi_copy_extent(
	stsp_image_handle_t to_image,
	sts_uint64_t to_offset,
	stsp_image_handle_t from_image,
	sts_uint64_t from_offset,
	sts_uint64_t length,
	int flags,
	sts_uint64_t 	*bytesCopied)
{

	VALIDATE(to_image);
	VALIDATE(from_image);
	VALIDATE(bytesCopied);

	if(length % STS_BLOCK_SIZE)  {
		LOG_ERROR(SESSION_I(from_image), "Length not multiple of STS_BLOCK_SIZE");
		return STS_EINVAL;		
	}

	if(to_offset % STS_BLOCK_SIZE)  {
		LOG_ERROR(SESSION_I(from_image), "to_offset not multiple of STS_BLOCK_SIZE");
		return STS_EINVAL;		
	}


	if(from_offset % STS_BLOCK_SIZE)  {
		LOG_ERROR(SESSION_I(from_image), "from_offset not multiple of STS_BLOCK_SIZE");
		return STS_EINVAL;		
	}

	int status = STS_EOK;	

	//create the buffer
	char *buf = new char[length];

	//read from the from_image
	sts_uint64_t bytesRead = 0;

	try {
		bytesRead = from_image->iptr->read(buf,length,from_offset);
	} catch(STSException & ex){
		LOG_ERROR(SESSION_I(from_image),ex.toString());
		return ex.error_code();
	}

	if(0 == bytesRead)  {
		LOG_ERROR(SESSION_I(from_image), "0 bytes read.");
		return STS_EINTERNAL;
	}

	//write to the to_image
	try {
		status = to_image->iptr->write(buf,length,to_offset,bytesCopied);
	} catch(STSException & ex){
		LOG_ERROR(SESSION_I(from_image),ex.toString());
		return ex.error_code();
	}

	if(*bytesCopied != bytesRead)  {
		LOG_ERROR(SESSION_I(from_image), "bytes written not equal to bytes read");
		return STS_EINTERNAL;
	}

	delete buf;

	return status;
}


int 
stspi_create_image_v9(
	const stsp_lsu_t *lsu, 
	const sts_image_def_v7_t *imageDefinition, 
	int pendingFlag, 
	stsp_image_handle_t *imageHandle)
{
	VALIDATE(lsu);
	VALIDATE(imageDefinition);
	VALIDATE(imageHandle);

	/* Extract storage server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL, something is wrong 
	 * server handle should not be NULL at this point */
	if(sh == NULL) return STS_EINTERNAL;

	/* If StorageServer object inside the server handle is NULL
	 * then it is internal error at this point */
	if(sh->sts == NULL) return STS_EINTERNAL;


	/* Query StorageServer object to get LSU object for given LSU name */
	LSUDefinition ldef;

	ldef.name=lsu->sl_lsu_name.sln_name;
	LSU *plsu = sh->sts->getLSU(&ldef);

	/* LSU not found */
	if(plsu == NULL) return STS_ENOENT;

	/* Create ImageDefinition object from image_def structure */
	ImageDefinition idef;
	idef.basename = imageDefinition->img_basename;
	idef.date = imageDefinition->img_date;

	Image *pi = NULL;

	/* Tell the LSU to create Image with this image definition */
	try {
		pi = plsu->createImage(idef);
	} catch (STSException & ex){
		/* Something went wrong in creating Image */
		LOG_ERROR(sh->sd,ex.toString());
		/* Free the LSU object */
		return ex.error_code();
	}

	/* Image creation was successful */

	/* Create an image handle */
	*imageHandle = new stsp_image_handle_s;
	/* Stuff the Image object in the handle */
	(*imageHandle)->iptr = pi;
	/* Tie the image handle with session */
	(*imageHandle)->sd = sh->sd;

	return STS_EOK;
}


int 
stspi_create_image_v10(
	const stsp_lsu_t *lsu, 
	const sts_image_def_v10_t *imageDefinition, 
	int pendingFlag, 
	stsp_image_handle_t *imageHandle)
{
	VALIDATE(lsu);
	VALIDATE(imageDefinition);
	VALIDATE(imageHandle);

	/* Extract storage server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL, something is wrong 
	 * server handle should not be NULL at this point */
	if(sh == NULL) return STS_EINTERNAL;

	/* If StorageServer object inside the server handle is NULL
	 * then it is internal error at this point */
	if(sh->sts == NULL) return STS_EINTERNAL;


	/* Query StorageServer object to get LSU object for given LSU name */
	LSUDefinition ldef;

	ldef.name=lsu->sl_lsu_name.sln_name;
	LSU *plsu = sh->sts->getLSU(&ldef);

	/* LSU not found */
	if(plsu == NULL) return STS_ENOENT;

	/* Create ImageDefinition object from image_def structure */
	ImageDefinition idef;
	idef.basename = imageDefinition->img_basename;
	idef.date = imageDefinition->img_date;
	idef.buf = *imageDefinition;
	Image *pi = NULL;

	/* 
	 *  Currently, sampledisk does not do anything
	 *  with the DPA specific sts_isinfo_t.  This code is
	 *  here merely as a model of what a plugin *might*
	 *  do to handle DPA specific code.
	*/
	if ((strcmp(imageDefinition->img_isid.is_dpaid, NBU_65)) == 0)
	{
		/* 
		 * cast the img_isid.is_info char buffer to
		 * be of type stsnbu_isinfo_v65_t
		*/
		stsnbu_isinfo_v65_t *isinfo = (stsnbu_isinfo_v65_t *) imageDefinition->img_isid.is_info;
		
		/* 
		 *  now the plugin can access the DPA specific fields
		 *  and use the information as it sees fit.
		*/


	}else if ((strcmp(imageDefinition->img_isid.is_dpaid, "OSTSDK_PREQUAL")) == 0)
	{
		/* the prequal tool uses the NBU specific isinfo_t */
		stsnbu_isinfo_v65_t *isinfo = (stsnbu_isinfo_v65_t *) imageDefinition->img_isid.is_info;
		ostringstream isinfo_stream;

		isinfo_stream <<"OSTSDK_PREQUAL isinfo_t:"<<endl;
		isinfo_stream <<"Backup ID.Master Server: "<<isinfo->isi_bckpid.bi_master_server<<endl;
		isinfo_stream <<"Backup ID.Backup Time: "<<isinfo->isi_bckpid.bi_time<<endl;
		isinfo_stream <<"Backup ID.Copy Number "<<isinfo->isi_bckpid.bi_copy_number<<endl;
		isinfo_stream <<"Backup Stream: "<<isinfo->isi_strm_num<<endl;
		isinfo_stream <<"Image Type: "<<isinfo->isi_img_type<<endl;
		isinfo_stream <<"Fragment Number: "<<isinfo->isi_frag_num<<endl;
		isinfo_stream <<"Resume Number: "<<isinfo->isi_instance_num<<endl;
		isinfo_stream <<"Client: "<<isinfo->isi_client<<endl;
		
		try {	
			LOG_INFO(sh->sd,isinfo_stream.str());
		} catch(STSException & ex){
			LOG_ERROR(sh->sd,ex.toString());
			return ex.error_code();
		}
	}else if ((strcmp(imageDefinition->img_isid.is_dpaid, "OSTSDK_PGNTESTER")) == 0)
	{
		/* This is the pgntesters dpaid.  There is nothing to handle here */

	}else {
		/* This is an unknown dpaid, ignore it */
	}

	/* Tell the LSU to create Image with this image definition */
	try {
		pi = plsu->createImage(idef);
	} catch (STSException & ex){
		/* Something went wrong in creating Image */
		LOG_ERROR(sh->sd,ex.toString());
		/* Free the LSU object */
		return ex.error_code();
	}

	/* Image creation was successful */

	/* Create an image handle */
	*imageHandle = new stsp_image_handle_s;
	/* Stuff the Image object in the handle */
	(*imageHandle)->iptr = pi;
	/* Tie the image handle with session */
	(*imageHandle)->sd = sh->sd;

	(*imageHandle)->lsu = *lsu;
	(*imageHandle)->img_def = *imageDefinition;

	return STS_EOK;
}

int
stspi_delete_event_v11(
		const stsp_server_handle_t server_handle, 
		sts_event_v11_t *event)
{
	VALIDATE(server_handle);
	VALIDATE(event);

	return server_handle->sts->delEvent(event);
}

int 
stspi_delete_image_v9(
	const stsp_lsu_t      *lsu, 
	const sts_image_def_v7_t *imageDefinition, 
	int                   asyncFlag)
{
	//return STS_ENOTSUP;
	VALIDATE(lsu);
	VALIDATE(imageDefinition);

	/* Extract storage server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL, something is wrong 
	 * server handle should not be NULL at this point */
	if(sh == NULL) return STS_EINTERNAL;

	/* If StorageServer object inside the server handle is NULL
	 * then it is internal error at this point */
	if(sh->sts == NULL) return STS_EINTERNAL;


	/* Query StorageServer object to get LSU object for given LSU name */
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *plsu = sh->sts->getLSU(&ldef);

	/* LSU not found */
	if(plsu == NULL) return STS_ENOENT;

	/* Create ImageDefinition object from image_def structure */
	ImageDefinition idef;
	idef.basename = imageDefinition->img_basename;
	idef.date = imageDefinition->img_date;

	/* Tell the LSU to delete Image with this image definition */
	try {
		plsu->deleteImage(idef);
	} catch (STSException & ex){
		/* Something went wrong in creating Image */
		LOG_ERROR(sh->sd,ex.toString());
		/* Free the LSU object */
		return ex.error_code();
	}

	/* Image deletetion was successful */

	return STS_EOK;
}


int 
stspi_delete_image_v10(
	const stsp_lsu_t      *lsu, 
	const sts_image_def_v10_t *imageDefinition, 
	int                    asyncFlag)
{
	//return STS_ENOTSUP;
	VALIDATE(lsu);
	VALIDATE(imageDefinition);

	/* Extract storage server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL, something is wrong 
	 * server handle should not be NULL at this point */
	if(sh == NULL) return STS_EINTERNAL;

	/* If StorageServer object inside the server handle is NULL
	 * then it is internal error at this point */
	if(sh->sts == NULL) return STS_EINTERNAL;


	/* Query StorageServer object to get LSU object for given LSU name */
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *plsu = sh->sts->getLSU(&ldef);

	/* LSU not found */
	if(plsu == NULL) return STS_ENOENT;

	/* Create ImageDefinition object from image_def structure */
	ImageDefinition idef;
	idef.basename = imageDefinition->img_basename;
	idef.date = imageDefinition->img_date;

	/* Tell the LSU to delete Image with this image definition */
	try {
		plsu->deleteImage(idef);
	} catch (STSException & ex){
		/* Something went wrong in creating Image */
		LOG_ERROR(sh->sd,ex.toString());
		/* Free the LSU object */
		return ex.error_code();
	}

	/* Image deletetion was successful */

	/* delete image on target lsu */
	delete_image_on_replication_target(lsu, imageDefinition, asyncFlag);

	return STS_EOK;
}

int
stspi_get_event_v11(
		stsp_evc_handle_t evc_handle,
		sts_event_v11_t *event)
{
	VALIDATE(evc_handle);
	VALIDATE(event);

	return evc_handle->sh->sts->getEvent(evc_handle, event);
}

int 
stspi_get_image_prop_v9(
	stsp_image_handle_t  imageHandle, 
	sts_image_info_v7_t    *imageInfo)
{
	VALIDATE(imageHandle);
	VALIDATE(imageInfo);

	VALIDATE(imageHandle->iptr);
	ImageInfo* info;
	try{
		info = imageHandle->iptr->getInfo();
	} catch (STSException & ex){
		LOG_ERROR(SESSION_I(imageHandle),ex.toString());
		return ex.error_code();
	}
	memset(imageInfo, 0, sizeof(*imageInfo));
	if(info){
		imageInfo->imo_size = info->idef.size;
		return STS_EOK;
	}
	else{
		return STS_EINVAL;
	}
}


int 
stspi_get_image_prop_v10(
	stsp_image_handle_t  imageHandle, 
	sts_image_info_v10_t    *imageInfo)
{
	VALIDATE(imageHandle);
	VALIDATE(imageInfo);

	VALIDATE(imageHandle->iptr);

	stsp_server_handle_t sh = imageHandle->lsu.sl_server_handle;
	ImageInfo* info;
	try{
		info = imageHandle->iptr->getInfo();
	} catch (STSException & ex){
		LOG_ERROR(sh->sd,ex.toString());
		return ex.error_code();
	}
	memset(imageInfo, 0, sizeof(*imageInfo));
	if(info){
		imageInfo->version = 10;
		imageInfo->imo_def = info->idef.buf;
		strncpy(imageInfo->imo_server, 
				sh->sts->getInfo()->name.c_str(),
				sizeof(imageInfo->imo_server));
		imageInfo->imo_lsu = imageHandle->lsu.sl_lsu_name;
		imageInfo->imo_size = info->idef.size;
		imageInfo->imo_block_size = STS_BLOCK_SIZE;
		imageInfo->imo_status = STS_II_IMAGE_CREATED;
		return STS_EOK;
	}
	else{
		return STS_EINVAL;
	}
	return STS_EOK;
}


int 
stspi_get_image_prop_byname_v9(
	const stsp_lsu_t      *lsu, 
	const sts_image_def_v7_t *imageDefinition, 
	sts_image_info_v7_t      *imageInfo)
{
	VALIDATE(lsu);
	VALIDATE(imageDefinition);
	VALIDATE(imageInfo);

	/* Extract server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL at this point, something is wrong */
	if(sh == NULL) return STS_EINTERNAL;

	/* If server handle doesn't have valid StorageServer object 
	 * something is wrong */
	if(sh->sts == NULL) return STS_EINTERNAL;

	/* Query StorageServer with given LSU name */
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *plsu = sh->sts->getLSU(&ldef);

	/* LSU was not found */
	if(plsu == NULL) return STS_ENOENT;

	/* Create ImageDefinition object and populate it 
	 * with image_def structure */
	ImageDefinition idef;
	idef.basename = imageDefinition->img_basename;
	idef.date = imageDefinition->img_date;

	/* Open an existing image with given definition on given LSU */
	Image *pi = NULL;
	try {
		pi = plsu->openImage(idef,STS_O_READ);
	} catch (STSException & ex){
		LOG_ERROR(sh->sd,ex.toString());
		return ex.error_code();
	}

	/* Get Image object's info */
	ImageInfo *pii;
	try{
		pii = pi->getInfo();
	} catch (STSException & ex){
		LOG_ERROR(sh->sd,ex.toString());
		return ex.error_code();
	}

	memset(imageInfo, 0, sizeof(*imageInfo));
	strcpy(imageInfo->imo_def.img_basename, pii->idef.basename.c_str());
	strcpy(imageInfo->imo_def.img_date, pii->idef.date.c_str());

	delete pi;
	return STS_EOK;
}


int 
stspi_get_image_prop_byname_v10(
	const stsp_lsu_t      *lsu, 
	const sts_image_def_v10_t *imageDefinition, 
	sts_image_info_v10_t      *imageInfo)
{
	VALIDATE(lsu);
	VALIDATE(imageDefinition);
	VALIDATE(imageInfo);

	/* Extract server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL at this point, something is wrong */
	if(sh == NULL) return STS_EINTERNAL;

	/* If server handle doesn't have valid StorageServer object 
	 * something is wrong */
	if(sh->sts == NULL) return STS_EINTERNAL;

	/* Query StorageServer with given LSU name */
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *plsu = sh->sts->getLSU(&ldef);

	/* LSU was not found */
	if(plsu == NULL) return STS_ENOENT;

	/* Create ImageDefinition object and populate it 
	 * with image_def structure */
	ImageDefinition idef;
	idef.basename = imageDefinition->img_basename;
	idef.date = imageDefinition->img_date;

	/* Open an existing image with given definition on given LSU */
	Image *pi = NULL;
	try {
		pi = plsu->openImage(idef,STS_O_READ);
	} catch (STSException & ex){
		LOG_ERROR(sh->sd,ex.toString());
		return ex.error_code();
	}

	/* Get Image object's info */
	ImageInfo *info;
	try{
		info = pi->getInfo();
	} catch (STSException & ex){
		LOG_ERROR(sh->sd,ex.toString());
		return ex.error_code();
	}

	memset(imageInfo, 0, sizeof(*imageInfo));
	if(info){
		imageInfo->version = 10;
		imageInfo->imo_def = info->idef.buf;
		strncpy(imageInfo->imo_server, 
				sh->sts->getInfo()->name.c_str(),
				sizeof(imageInfo->imo_server));
		imageInfo->imo_lsu = lsu->sl_lsu_name;
		imageInfo->imo_size = info->idef.size;
		imageInfo->imo_block_size = STS_BLOCK_SIZE;
		imageInfo->imo_status = STS_II_IMAGE_CREATED;
	}

	delete pi;
	return STS_EOK;
}


int 
stspi_get_lsu_prop_byname_v9(
	const stsp_lsu_t	*lsu, 
	sts_lsu_info_v9_t 		*lsuInfo)
{
	VALIDATE(lsu);
	VALIDATE(lsuInfo);

	/* Extract server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL something is wrong */
	if(sh == NULL) return STS_EINTERNAL;

	/* If server handle doesn't have valid StorageServer object
	 * something is wrong */
	if(sh->sts == NULL) return STS_EINTERNAL;

	/* Query StorageServer object with desired LSU name */
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *pl = sh->sts->getLSU(&ldef);

	/* LSU was not found */
	if(pl == NULL) return STS_ENOENT;

	/* LSU was found, now extract its info */
	LSUInfo *pli = pl->getInfo();
	string lsu_label;
	try{
		lsu_label = pl->getLabel();
	} catch(STSException & ex){
		LOG_ERROR(sh->sd,ex.toString());
		lsu_label = "";
	}

	/* Something went wrong while getting the info of LSU */
	if(pli == NULL) return STS_EINTERNAL;

	memset(lsuInfo, 0, sizeof(*lsuInfo));
	strncpy(lsuInfo->lsu_server,
			sh->sts->getInfo()->name.c_str(),
			sizeof(lsuInfo->lsu_server));
	lsuInfo->lsu_capacity = pli->lsu_capacity;
	lsuInfo->lsu_capacity_phys = pli->lsu_capacity_phys;
	lsuInfo->lsu_used = pli->lsu_used;
	lsuInfo->lsu_used_phys = pli->lsu_used_phys;
	lsuInfo->lsu_images = pli->num_images;

	lsuInfo->lsu_def.sld_alloc = STS_LSU_AT_STATIC;
	lsuInfo->lsu_def.sld_storage = STS_LSU_ST_FILE;
	/* Copy LSU's info into lsu_info structure */
	strncpy(lsuInfo->lsu_def.sld_name.sln_name,pli->ldef.name.c_str(),STS_MAX_LSUNAME);
	lsuInfo->lsu_def.sld_max_transfer = pli->ldef.max_transfer;
	lsuInfo->lsu_def.sld_block_size = pli->ldef.block_size;
	strncpy(lsuInfo->lsu_def.sld_label,lsu_label.c_str(),STS_MAX_LSU_LABEL);
	lsuInfo->lsu_def.sld_flags = pli->ldef.lsu_flags;

	return STS_EOK;
}

int 
stspi_get_lsu_prop_byname_v11(
	const stsp_lsu_t	*lsu, 
	sts_lsu_info_v11_t 		*lsuInfo)
{
	VALIDATE(lsu);
	VALIDATE(lsuInfo);

	/* Extract server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL something is wrong */
	if(sh == NULL) return STS_EINTERNAL;

	/* If server handle doesn't have valid StorageServer object
	 * something is wrong */
	if(sh->sts == NULL) return STS_EINTERNAL;

	/* Query StorageServer object with desired LSU name */
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *pl = sh->sts->getLSU(&ldef);

	/* LSU was not found */
	if(pl == NULL) return STS_ENOENT;

	/* LSU was found, now extract its info */
	LSUInfo *pli = pl->getInfo();
	string lsu_label;
	try{
		lsu_label = pl->getLabel();
	} catch(STSException & ex){
		LOG_INFO(sh->sd,ex.toString());
		lsu_label = "";
	}

	/* Something went wrong while getting the info of LSU */
	if(pli == NULL) return STS_EINTERNAL;

	memset(lsuInfo, 0, sizeof(*lsuInfo));
	lsuInfo->version = 11;
	strncpy(lsuInfo->lsu_server,
			sh->sts->getInfo()->name.c_str(),
			sizeof(lsuInfo->lsu_server));
	lsuInfo->lsu_capacity = pli->lsu_capacity;
	lsuInfo->lsu_capacity_phys = pli->lsu_capacity_phys;
	lsuInfo->lsu_used = pli->lsu_used;
	lsuInfo->lsu_used_phys = pli->lsu_used_phys;
	lsuInfo->lsu_images = pli->num_images;

	lsuInfo->lsu_def.version = 11;
	lsuInfo->lsu_def.sld_alloc = STS_LSU_AT_STATIC;
	lsuInfo->lsu_def.sld_storage = STS_LSU_ST_FILE;
	/* Copy LSU's info into lsu_info structure */
	strncpy(lsuInfo->lsu_def.sld_name.sln_name,pli->ldef.name.c_str(),STS_MAX_LSUNAME);
	lsuInfo->lsu_def.sld_max_transfer = pli->ldef.max_transfer;
	lsuInfo->lsu_def.sld_block_size = pli->ldef.block_size;
	strncpy(lsuInfo->lsu_def.sld_label,lsu_label.c_str(),STS_MAX_LSU_LABEL);
	lsuInfo->lsu_def.sld_flags = pli->ldef.lsu_flags;
	lsuInfo->lsu_def.sld_rep_sources = pli->ldef.lsu_rep_sources;
	lsuInfo->lsu_def.sld_rep_targets = pli->ldef.lsu_rep_targets;

	return STS_EOK;
}

int 
stspi_get_server_prop(
	stsp_server_handle_t  sh, 
	sts_server_info_t	 *serverInfo)
{
	VALIDATE(sh);
	VALIDATE(serverInfo);

	/* If handle doesn't have StorageServer object, 
	 * something is wrong */
	if(sh->sts == NULL) return STS_ESERVER;

	/* Query the Info object from the StorageServer */
	StorageServerInfo *si = sh->sts->getInfo();
	if(si == NULL) return STS_EINTERNAL;

	/* Copy the server info into server_info structure */
	strncpy(serverInfo->srv_server,si->name.c_str(),STS_MAX_STSNAME);

	/* If credentials are required for accessing this storage server, 
	 * do this */
	serverInfo->srv_flags = 0;
	serverInfo->srv_flags |= STS_SRV_CRED;
	serverInfo->srv_flags |= STS_SRV_IMAGE_COPY;	// set for duplication
	serverInfo->srv_flags |= STS_SRV_STORED_EV;	// set for stored event
	serverInfo->srv_flags |= STS_SRV_ASYNC;		// set for async read/write
	serverInfo->srv_flags |= STS_SRV_EVASYNC;	// set for push event
	serverInfo->srv_flags |= STS_SRV_EVSYNC;	// set for pull event

	/* List	of capabilities	supported by this storage server */
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLAIM);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_IMAGE_LIST);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_LSU_LIST);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_SERVER);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CREATE_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_DELETE_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_IMAGE_PROP);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_IMAGE_PROP_BYNAME);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_LSU_PROP_BYNAME);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_SERVER_PROP);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_SERVER_PROP_BYNAME);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_LIST_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_LIST_LSU);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_IMAGE_LIST);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_LSU_LIST);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_SERVER);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_READ_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_TERMINATE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_WRITE_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_COPY_EXTENT);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_READ_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_WAIT);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_WRITE_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ADD_EVSOURCE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_EVCHANNEL);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_DELETE_EVENT);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_EVENT);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_EVCHANNEL);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_TARGET_SERVER);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_NAMED_ASYNC_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_CANCEL);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_NAMED_ASYNC_CANCEL);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_NAMED_ASYNC_WAIT);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_NAMED_ASYNC_STATUS);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_SERVER_CONFIG);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_SET_SERVER_CONFIG);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_BEGIN_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_END_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_END_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_NAMED_ASYNC_END_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_LSU_REPLICATION_PROP);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_IOCONTROL);
	/*
	*  The sampledisk plugin does not support remote duplication
	*  But, if it did, the following cap would be set
	*/
	//STS_SET_SRV_CAP(serverInfo, STS_SRV_IMAGE_COPY_REMOTE);

	return STS_EOK;
}

int 
stspi_get_server_prop_byname(
	const sts_session_def_t *session, 
	const sts_server_name_t  serverName, 
	sts_server_info_t	    *serverInfo)
{
	VALIDATE(session);
	VALIDATE(serverName);
	VALIDATE(serverInfo);

	/* We will temporarily get a StorageServer object 
	 * to get its properties */
	StorageServer *psts = NULL;

	/* Get the StorageServer object for given server name */
	try{
		psts = new SampleStorageServer(serverName);
	} catch(STSException& ex) {
		LOG_ERROR(session,ex.toString());
		return STS_ESERVER;
	}

	/* Query StorageServer for its info */
	StorageServerInfo *si = psts->getInfo();

	/* Problem getting StorageServer info */
	if(si == NULL) {
		if(psts) delete psts;
		return STS_EINTERNAL;
	}

	/* Copy StorageServer's info into server_info structure */
	strncpy(serverInfo->srv_server,si->name.c_str(),STS_MAX_STSNAME);

	/* If credentials are required for accessing this storage server,
	 * do this */
	serverInfo->srv_flags = 0;
	serverInfo->srv_flags |= STS_SRV_CRED;
	serverInfo->srv_flags |= STS_SRV_IMAGE_COPY;	// set for duplication
	serverInfo->srv_flags |= STS_SRV_STORED_EV;	// set for stored event
	serverInfo->srv_flags |= STS_SRV_ASYNC;		// set for async read/write
	serverInfo->srv_flags |= STS_SRV_EVASYNC;	// set for push event
	serverInfo->srv_flags |= STS_SRV_EVSYNC;	// set for pull event

	/* capabilities supported by this storage server */
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLAIM);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_IMAGE_LIST);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_LSU_LIST);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_SERVER);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CREATE_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_DELETE_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_IMAGE_PROP);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_IMAGE_PROP_BYNAME);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_LSU_PROP_BYNAME);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_SERVER_PROP);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_SERVER_PROP_BYNAME);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_LIST_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_LIST_LSU);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_IMAGE_LIST);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_LSU_LIST);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_SERVER);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_READ_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_TERMINATE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_WRITE_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_COPY_EXTENT);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_READ_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_WAIT);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_WRITE_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ADD_EVSOURCE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_CLOSE_EVCHANNEL);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_DELETE_EVENT);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_EVENT);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_EVCHANNEL);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_OPEN_TARGET_SERVER);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_NAMED_ASYNC_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_CANCEL);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_NAMED_ASYNC_CANCEL);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_NAMED_ASYNC_WAIT);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_NAMED_ASYNC_STATUS);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_SERVER_CONFIG);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_SET_SERVER_CONFIG);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_BEGIN_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_END_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_ASYNC_END_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_NAMED_ASYNC_END_COPY_IMAGE);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_GET_LSU_REPLICATION_PROP);
	STS_SET_SRV_CAP(serverInfo, STS_SRVC_IOCONTROL);
	/*
	*  The sampledisk plugin does not support remote duplication
	*  But, if it did, the following cap would be set
	*/
	//STS_SET_SRV_CAP(serverInfo, STS_SRV_IMAGE_COPY_REMOTE);
	/* Free the temporary object */
	if(psts) delete psts;

	return STS_EOK;
}


int 
stspi_list_image_v9(
	stsp_image_list_handle_t image_list_handle, 
	sts_image_def_v7_t *img)
{
	VALIDATE(image_list_handle);
	VALIDATE(img);
	
	/* Extract vector of images from image list handle */
	vector<Image *> *image_list = image_list_handle->image_list;

	/* Check if the cursor in image list handle has already reached the end */
	if(image_list->size() == image_list_handle->cursor) return STS_ENOENT;

	/* Index into the vector of images and get the info of cursor'th image */
	ImageInfo *pli = (*image_list)[image_list_handle->cursor]->getInfo();

	/* Error getting image info */
	if(pli == NULL) return STS_EINTERNAL;

	/* Return the name and date of image read from the info */
	strncpy(img->img_basename,pli->idef.basename.c_str(),STS_MAX_IMAGENAME);
		strncpy(img->img_date,pli->idef.date.c_str(),STS_MAX_DATE);
	 /* strncpy(img->img_fulldate,pli->idef.fulldate.c_str(),STS_MAX_DATE);*/

	/* Increment the cursor/iterator */
	image_list_handle->cursor++;

	return STS_EOK;
}


int 
stspi_list_image_v10(
	stsp_image_list_handle_t image_list_handle, 
	sts_image_def_v10_t *img)
{
	VALIDATE(image_list_handle);
	VALIDATE(img);
	
	/* Extract vector of images from image list handle */
	vector<Image *> *image_list = image_list_handle->image_list;

	/* Check if the cursor in image list handle has already reached the end */
	if(image_list->size() == image_list_handle->cursor) return STS_ENOENT;

	/* Index into the vector of images and get the info of cursor'th image */
	ImageInfo *pli = (*image_list)[image_list_handle->cursor]->getInfo();

	/* Error getting image info */
	if(pli == NULL) return STS_EINTERNAL;

	*img = pli->idef.buf;

	/* Increment the cursor/iterator */
	image_list_handle->cursor++;

	return STS_EOK;
}

int
stspi_open_evchannel_v11(
		const sts_session_def_v7_t *sd,
		const sts_server_name_v7_t server, 
		const sts_cred_v7_t *cred,
		const sts_interface_v7_t iface, 
		sts_evhandler_v11_t handler,
		sts_event_v11_t *event, 
		int flags, 
		sts_evseqno_v11_t evseqno, 
		stsp_evc_handle_t *pevc_handle)
{
	int ret;
	int isPushMode;
	VALIDATE(sd);
	VALIDATE(server);
	VALIDATE(pevc_handle);
	
	
	if(handler){
		isPushMode = 1;
		VALIDATE(event);
	}
	else{
		isPushMode = 0;
		if(event)
			return STS_EINVAL;
	}

	/* get server handle by server name */
	stsp_server_handle_t sh;
	if(evc_srvs.find(server) != evc_srvs.end()){
		sh = evc_srvs[server];
	}
	else{
		ret = stspi_open_server(sd, server, cred, iface, &sh);
		if(ret != STS_EOK)
			return ret;
	}
	evc_srvs[server] = sh;

	*pevc_handle = new stsp_evc_handle_s;
	(*pevc_handle)->sh = sh;
	(*pevc_handle)->flags = flags;
	(*pevc_handle)->mode = isPushMode;
	(*pevc_handle)->seq = evseqno;
	(*pevc_handle)->handler = handler;
	(*pevc_handle)->event = event;


	if(isPushMode){
		/* subsribe event for the push mode channel */
		ret = sh->sts->subscribeEvent(*pevc_handle);
	}
	else{
		ret = STS_EOK;
	}

	return ret;
}

int 
stspi_list_lsu(
	stsp_lsu_list_handle_t lsuListHandle, 
	sts_lsu_name_t         *lsuName)
{
	VALIDATE(lsuListHandle);
	VALIDATE(lsuName);
	
	/* Extract vector of LSUs from lsu list handle */
	vector<LSU *> *lsu_list = lsuListHandle->lsu_list;

	/* Check if the cursor in lsu list handle has already reached the end */
	if((lsu_list == NULL) || (lsu_list->size() == lsuListHandle->cursor)) 
		return STS_ENOENT;

	/* Index into the vector of LSUs and get the info of cursor'th LSU */
	LSUInfo *pli = (*lsu_list)[lsuListHandle->cursor]->getInfo();

	/* Error getting LSU info */
	if(pli == NULL) return STS_EINTERNAL;

	/* Return the name of LSU read from the info */
	strncpy(lsuName->sln_name,pli->ldef.name.c_str(),STS_MAX_LSUNAME);

	/* Increment the cursor/iterator */
	lsuListHandle->cursor++;

	return STS_EOK;
}


int
stspi_open_image_list(
	const stsp_lsu_t *lsu,
	int type,
	stsp_image_list_handle_t *image_list_handle)
{
	VALIDATE(lsu);
	VALIDATE(image_list_handle);

	/* Extract server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL something is wrong */
	if(sh == NULL) return STS_EINTERNAL;

	/* If server handle doesn't have valid StorageServer object
	 * something is wrong */
	if(sh->sts == NULL) return STS_EINTERNAL;

	/* Query StorageServer object with desired LSU name */
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *plsu = sh->sts->getLSU(&ldef);

	/* LSU was not found */
	if(plsu == NULL) return STS_ENOENT;

	/* Query LSU for list of Images */
	vector<Image *> *image_list;
	try{
		image_list = plsu->getImageList();
	} catch(STSException& ex) {
		LOG_ERROR(sh->sd,ex.toString());
		return ex.error_code();
	}

	/* Create Image list handle */
	*image_list_handle = new stsp_image_list_handle_s;

	/* Stuff the server handle in lsu list handle */
	(*image_list_handle)->sh = sh;
	(*image_list_handle)->image_list = image_list;

	/* Reset the cursor/iterator of this lsu list */
	(*image_list_handle)->cursor = 0;

	return STS_EOK;
}


int
stspi_open_lsu_list_v9(
	stsp_server_handle_t sh,
	const sts_lsu_def_v9_t *lsudef,
	stsp_lsu_list_handle_t *lsu_list_handle)
{
	VALIDATE(sh);
	/* VALIDATE(lsudef); */ /* If you use it */
	VALIDATE(lsu_list_handle);

	/* If server handle doesn't have valid StorageServer object
	 * at this point, then something is wrong */
	if(sh->sts == NULL) return STS_EINTERNAL;

	/* Query StorageServer for list of LSUs it hosts */
	vector<LSU *> *lsu_list = sh->sts->getLSUList();

	/* in case StorageServer has no LSUs */
	if(lsu_list == NULL) {
		LOG_ERROR(sh->sd,"LSU List is NULL");
		return STS_ENOENT;
	}
	if(NULL != lsudef)
	{
		int i =0;
		while(i<lsu_list->size() && 
			(*lsu_list)[i]->getInfo()->ldef.name != lsudef->sld_name.sln_name)
			++i;

		if(i>=lsu_list->size())
			return STS_ENOENT;
		/* 
		 * There is an eventual mem leak here..
		 * but that's not a surprise.. after all this is a sample..
		*/
		vector<LSU*>*list = new vector<LSU*> (1);
		(*list)[0] = (*lsu_list)[i];
		lsu_list = list;
	}
	/* Create LSU list handle */
	*lsu_list_handle = new stsp_lsu_list_handle_s;

	/* Stuff the server handle in lsu list handle */
	(*lsu_list_handle)->sh = sh;
		
	(*lsu_list_handle)->lsu_list = lsu_list;
	
	/* Reset the cursor/iterator of this lsu list */
	(*lsu_list_handle)->cursor = 0;
	return STS_EOK;
}

int
stspi_open_lsu_list_v11(
	stsp_server_handle_t sh,
	const sts_lsu_def_v11_t *lsudef,
	stsp_lsu_list_handle_t *lsu_list_handle)
{
	VALIDATE(sh);
	/* VALIDATE(lsudef); */ /* If you use it */
	VALIDATE(lsu_list_handle);

	/* If server handle doesn't have valid StorageServer object
	 * at this point, then something is wrong */
	if(sh->sts == NULL) return STS_EINTERNAL;

	/* Query StorageServer for list of LSUs it hosts */
	vector<LSU *> *lsu_list = sh->sts->getLSUList();

	/* in case StorageServer has no LSUs */
	if(lsu_list == NULL) {
		LOG_ERROR(sh->sd,"LSU List is NULL");
		return STS_ENOENT;
	}
	if(NULL != lsudef)
	{
		int i =0;
		while(i<lsu_list->size() && 
			(*lsu_list)[i]->getInfo()->ldef.name != lsudef->sld_name.sln_name)
			++i;

		if(i>=lsu_list->size())
			return STS_ENOENT;
		/* 
		 * There is an eventual mem leak here..
		 * but that's not a surprise.. after all this is a sample..
		*/
		vector<LSU*>*list = new vector<LSU*> (1);
		(*list)[0] = (*lsu_list)[i];
		lsu_list = list;
	}
	/* Create LSU list handle */
	*lsu_list_handle = new stsp_lsu_list_handle_s;

	/* Stuff the server handle in lsu list handle */
	(*lsu_list_handle)->sh = sh;
		
	(*lsu_list_handle)->lsu_list = lsu_list;
	
	/* Reset the cursor/iterator of this lsu list */
	(*lsu_list_handle)->cursor = 0;
	return STS_EOK;
}

int 
stspi_open_server(
	const sts_session_def_t *session, 
	const sts_server_name_t sts_server_name, 
	const sts_cred_t        *credentials, 
	const sts_interface_t   stsInterface, 
	stsp_server_handle_t    *sh)
{
	VALIDATE(session);
	VALIDATE(sh);
	VALIDATE(sts_server_name);
	VALIDATE(credentials); /* If you use them */

	/* Allocate new server handle */
	*sh = new stsp_server_handle_s;

	try{
		/* Create new StorageServer object and stuff into the handle */
		(*sh)->sts = new SampleStorageServer(sts_server_name);
		/* Tie the handle with the session */
		(*sh)->sd = session;
	} catch(STSException& ex) {
		LOG_ERROR(session,ex.toString());
		return STS_ESERVER;
	}
	return STS_EOK;
}


int 
stspi_open_target_server(
	stsp_server_handle_t     sh,
	const sts_server_name_t  sts_server_name, 
	const sts_cred_t         *credentials, 
	const sts_interface_t    stsInterface, 
	stsp_server_handle_t     *target_server_handle)
{
	VALIDATE(sh);
	VALIDATE(sts_server_name);
	//VALIDATE(credentials); /* If you use them */
	VALIDATE(target_server_handle);

	/* Allocate new server handle */
	*target_server_handle = new stsp_server_handle_s;

	try{
		/* Create new StorageServer object and stuff into the handle */
		(*target_server_handle)->sts = new SampleStorageServer(sts_server_name);
		/* Tie the handle with the session */
		(*target_server_handle)->sd = sh->sd;
	} catch(STSException& ex) {
		LOG_ERROR(sh->sd,ex.toString());
		return STS_ESERVER;
	}
	return STS_EOK;
}


int 
stspi_open_image_v9(
	const stsp_lsu_t         *lsu, 
	const sts_image_def_v7_t *imageDefinition, 
	int                       mode,
	stsp_image_handle_t       *imageHandle)
{
	VALIDATE(lsu);
	VALIDATE(imageDefinition);
	VALIDATE(imageHandle);

	/* Extract server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL at this point, something is wrong */
	if(sh == NULL) return STS_EINTERNAL;

	/* 
	 * If server handle doesn't have valid StorageServer object 
	 * something is wrong
	*/
	if(sh->sts == NULL) return STS_EINTERNAL;

	/* Query StorageServer with given LSU name */
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *plsu = sh->sts->getLSU(&ldef);

	/* LSU was not found */
	if(plsu == NULL) return STS_ENOENT;

	/* Create ImageDefinition object and populate it 
	 * with image_def structure */
	ImageDefinition idef;
	idef.basename = imageDefinition->img_basename;
	idef.date = imageDefinition->img_date;

	/* Open an existing image with given definition on given LSU */
	Image *pi = NULL;
	try {
		pi = plsu->openImage(idef,mode);
	} catch (STSException & ex){
		LOG_ERROR(sh->sd,ex.toString());
		return ex.error_code();
	}
	
	/* Create Image handle */
	*imageHandle = new stsp_image_handle_s;

	/* Stuff Image object in the Image handle */
	(*imageHandle)->iptr = pi;
	/* Tie the image handle with session */
	(*imageHandle)->sd = sh->sd;

	return STS_EOK;
}


int 
stspi_open_image_v10(
	const stsp_lsu_t          *lsu, 
	const sts_image_def_v10_t *imageDefinition, 
	int                       mode, 
	stsp_image_handle_t       *imageHandle)
{
	VALIDATE(lsu);
	VALIDATE(imageDefinition);
	VALIDATE(imageHandle);

	/* Extract server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL at this point, something is wrong */
	if(sh == NULL) return STS_EINTERNAL;

	/*
	 * If server handle doesn't have valid StorageServer object 
	 * something is wrong
	*/
	if(sh->sts == NULL) return STS_EINTERNAL;

	/* Query StorageServer with given LSU name */
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *plsu = sh->sts->getLSU(&ldef);

	/* LSU was not found */
	if(plsu == NULL) return STS_ENOENT;

	/* 
	 * Create ImageDefinition object and populate it 
	 * with image_def structure
	*/
	ImageDefinition idef;
	idef.basename = imageDefinition->img_basename;
	idef.date = imageDefinition->img_date;

	/* Open an existing image with given definition on given LSU */
	Image *pi = NULL;
	try {
		pi = plsu->openImage(idef,mode);
	} catch (STSException & ex){
		LOG_ERROR(sh->sd,ex.toString());
		return ex.error_code();
	}
	
	/* Create Image handle */
	*imageHandle = new stsp_image_handle_s;

	/* Stuff Image object in the Image handle */
	(*imageHandle)->iptr = pi;
	/* Tie the image handle with session */
	(*imageHandle)->sd = sh->sd;

	(*imageHandle)->lsu = *lsu;
	(*imageHandle)->img_def = *imageDefinition;

	return STS_EOK;
}


int 
stspi_read_image(
	stsp_image_handle_t  ih,
	void                 *buf,
	sts_uint64_t         length,
	sts_uint64_t         offset,
	sts_uint64_t         *bytesRead)
{
	VALIDATE(ih);
	VALIDATE(buf);
	VALIDATE(bytesRead);

	/* The image handle should have valid Image object */
	if(ih->iptr == NULL) return STS_EINTERNAL;

	if(length % STS_BLOCK_SIZE)  {
		LOG_ERROR(SESSION_I(ih), "Length not multiple of STS_BLOCK_SIZE");
		return STS_EINVAL;		
	}

	if(offset % STS_BLOCK_SIZE)  {
		LOG_ERROR(SESSION_I(ih), "Offset not multiple of STS_BLOCK_SIZE");
		return STS_EINVAL;		
	}

	try {
		*bytesRead = ih->iptr->read(buf,length,offset);
	} catch(STSException & ex){
		LOG_ERROR(SESSION_I(ih),ex.toString());
		return ex.error_code();
	}
	return STS_EOK;
}

int
stspi_terminate()
{
	/* Couldn't think of doing anything here,
	 * everything already cleaned up in this plugin */
	if(mngr){
		delete mngr;
		mngr = NULL;
	}
	return STS_EOK;
}

int 
stspi_write_image(
	stsp_image_handle_t ih,
	sts_stat_t          *stat,
	void                *buf,
	sts_uint64_t        length,
	sts_uint64_t        offset,
	sts_uint64_t        *bytesWritten)
{
	VALIDATE(ih);
	VALIDATE(buf);
	VALIDATE(bytesWritten);

	if(length % STS_BLOCK_SIZE)  {
		LOG_ERROR(SESSION_I(ih), "Length not multiple of STS_BLOCK_SIZE");
		return STS_EINVAL;		
	}

	if(offset % STS_BLOCK_SIZE)  {
		LOG_ERROR(SESSION_I(ih), "Offset not multiple of STS_BLOCK_SIZE");
		return STS_EINVAL;		
	}

	int status = STS_EOK;	
	try {
		status = ih->iptr->write(buf,length,offset,bytesWritten);
	} catch(STSException & ex){
		LOG_ERROR(SESSION_I(ih),ex.toString());
		return ex.error_code();
	}
	return status;
}


int 
stspi_label_lsu(
	const stsp_lsu_t *lsu,
	sts_lsu_label_t lsu_label)
{	

	/* stspi_label_lsu() stores the Label for an LSU. The label is an externally generated (by DPA)  
	 * identifier for the LSU. 
	 * Although, we (the storage server) have assigned our own name for the LSU, there may be many
	 * different storage servers and many different LSUs in the DPA's environment. By applying its 
	 * own labels to the LSUs, a DPA can uniquely identify each LSU. 
	 * Storing the label within the storage server enables the DPA to accurately recreate its database
	 * and catalog when needed. In an event of disaster recovery, the storage server's images can be 
	 * reimported and both the catalog and device database can be completely regenerated. 
	 */	

	VALIDATE(lsu);
	/* Extract server handle from lsu */
	stsp_server_handle_t sh = lsu->sl_server_handle;

	/* If server handle is NULL something is wrong */
	if(sh == NULL) return STS_EINTERNAL;

	/* If server handle doesn't have valid StorageServer object
	* something is wrong */
	if(sh->sts == NULL) return STS_EINTERNAL;
	
	/* Query StorageServer object with desired LSU name */
	
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *pl = sh->sts->getLSU(&ldef);
	
	/* LSU was not found */
	if(pl == NULL) return STS_ENOENT;


	/* LSU was found, now write LSU specific info and set the label */
	LSUInfo *pli = pl->getInfo();
	ostringstream label_info_stream;
	string label_info;

	label_info_stream <<"#This file holds the LSU specific information:"<<endl;
	label_info_stream <<"#LSU_Name: "<<pli->ldef.name.c_str()<<endl;
	label_info_stream <<"#LSU_Max_Transfer_Size: "<<pli->ldef.max_transfer<<endl;
	label_info_stream <<"#LSU_Block_Size: "<<pli->ldef.block_size<<endl;
	label_info_stream <<"LSU_Label: "<<lsu_label<<endl;
		
	try {
		label_info = label_info_stream.str();	
		if( pl->setLabel(label_info) != 0)  {
			return STS_EINTERNAL;
		}
	} catch(STSException & ex){
		LOG_ERROR(sh->sd,ex.toString());
		return ex.error_code();
	}

	return STS_EOK;
}

int
stspi_write_image_meta(
	stsp_image_handle_t image_handle,
	void *buf,
	sts_uint64_t length,
	sts_uint64_t offset,
	sts_uint64_t *bytesWritten)
{
	VALIDATE(image_handle);
	VALIDATE(buf);
	VALIDATE(bytesWritten);
	
	int status = STS_EOK;
	try {
		status = image_handle->iptr->write(buf,length,offset,bytesWritten);
	} catch(STSException & ex){
		LOG_ERROR(SESSION_I(image_handle),ex.toString());
		return ex.error_code();
	}
	return status;
}

int 
stspi_read_image_meta(
	stsp_image_handle_t image_handle,
	void *buf,
	sts_uint64_t len,
	sts_uint64_t offset,
	sts_uint64_t *bytesread)
{
	VALIDATE(image_handle);
	VALIDATE(buf);
	VALIDATE(bytesread);

	try {
		*bytesread = image_handle->iptr->read(buf,len,offset);
	} catch(STSException & ex){
		LOG_ERROR(SESSION_I(image_handle),ex.toString());
		return ex.error_code();
	}
	return STS_EOK;
}

int
stspi_async_cancel_v11(
		const sts_session_def_v7_t *sd, 
		stsp_opid_t opid)
{
	VALIDATE(sd);
	VALIDATE(opid);

	return mngr->cancelTask(reinterpret_cast<Task*>(opid));
}

int
stspi_async_copy_image_v11(
		const stsp_lsu_v7_t *to_lsu, 
		const sts_image_def_v10_t *to_img,
		const stsp_lsu_v7_t *from_lsu, 
		const sts_image_def_v10_t *from_img, 
		stsp_opid_t *opid,
		const sts_opname_v11_t imageset,
		int eventflag)
{
	VALIDATE(to_img);
	VALIDATE(from_lsu);
	VALIDATE(from_img);
	VALIDATE(opid);
	VALIDATE(strlen(imageset.op_name));

	/* create an async task for copy image job*/
	CopyImageTask* task = new CopyImageTask(to_lsu, to_img, from_lsu, from_img, NULL, imageset, eventflag);
	*opid = reinterpret_cast<stsp_opid_t>(task);
	return mngr->addTask(task);
}

int
stspi_copy_image_v11(
		const stsp_lsu_v7_t *to_lsu, 
		const sts_image_def_v10_t *to_img,
		const stsp_lsu_v7_t *from_lsu, 
		const sts_image_def_v10_t *from_img, 
		const sts_opname_v11_t imageset,
		int eventflag)
{
	int ret;
	stsp_image_handle_t from_ih = NULL;

	VALIDATE(to_img);
	VALIDATE(from_lsu);
	VALIDATE(from_img);
	VALIDATE(strlen(imageset.op_name));

	/* increase the count number of working tasks in imageset. */
	ret = mngr->getImageSet().addTask(imageset, eventflag, to_img);
	if(ret != STS_EOK)
		goto finished;


	/* open source image */
	ret = stspi_open_image_v10(from_lsu, from_img, STS_O_READ, &from_ih);
	if(ret != STS_EOK)
		goto finished;

	if(to_lsu == NULL){
		ret = copy_image_to_replication_target(to_img, from_ih, imageset, eventflag);
	}
	else{
		ret = copy_image(to_lsu, to_img, from_ih, imageset, eventflag);
	}

	mngr->getImageSet().decTask(imageset);
	if(eventflag | STS_EVFLAG_TRIGGER){
		mngr->getImageSet().release(imageset);
	}

finished:
	if(from_ih){
		stspi_close_image(from_ih, 0, 0);
	}
	return ret;
}

int
stspi_get_event_payload_v11(
		const stsp_server_handle_t server_handle, 
		void *plbuf, 
		const sts_event_v11_t *event)
{
	VALIDATE(server_handle);
	VALIDATE(plbuf);
	VALIDATE(event);

	return server_handle->sts->getEventPayload(event, plbuf);
}

int
stspi_named_async_cancel_v11(
		stsp_server_handle_t server_handle,  
		const sts_opname_v11_t opname)
{
	VALIDATE(server_handle);

	return mngr->cancelTask(opname.op_name);
}

int
stspi_named_async_copy_image_v11(
		const stsp_lsu_v7_t *target_lsu, 
		const sts_image_def_v10_t *target_image, 
		const stsp_lsu_v7_t *source_lsu, 
		const sts_image_def_v10_t *source_image, 
		const sts_opname_v11_t opname,
		const sts_opname_v11_t imageset,
		int eventflag)
{
	VALIDATE(target_image);
	VALIDATE(source_lsu);
	VALIDATE(source_image);
	VALIDATE(strlen(imageset.op_name));

	/* create a named async copy image task */
	CopyImageTask* task = new CopyImageTask(target_lsu, target_image, source_lsu, source_image, opname.op_name, imageset, eventflag);
	return mngr->addTask(task, opname.op_name);
}

int
stspi_named_async_status_v11(
		stsp_server_handle_t server_handle,
		sts_opname_v11_t opname)
{
	VALIDATE(server_handle);
	return mngr->requestTaskStatus(server_handle, opname.op_name);
}

int
stspi_named_async_wait_v11(
		stsp_server_handle_t server_handle, 
		const sts_opname_v11_t opname, 
		int blockflag, 
		sts_aioresult_v11_t *result)
{
	VALIDATE(server_handle);
	VALIDATE(result);

	return mngr->waitTaskResult(opname.op_name, blockflag, result);
}

int
stspi_get_server_config_v11(
		stsp_server_handle_t server_handle,
		char *buf, 
		sts_uint32_t buflen, 
		sts_uint32_t *maxlen)
{
	VALIDATE(server_handle);
	VALIDATE(server_handle->sts);
	VALIDATE(buf);
	VALIDATE(maxlen);

	return server_handle->sts->getConfig(buf, buflen, maxlen);
}

int
stspi_set_server_config_v11(
		stsp_server_handle_t server_handle,
		const char *buf, 
		char *msgbuf, 
		sts_uint32_t msgbuflen)
{
	VALIDATE(server_handle);
	VALIDATE(server_handle->sts);
	VALIDATE(buf);
	VALIDATE(msgbuf);

	return server_handle->sts->setConfig(buf, msgbuf, msgbuflen);
}

/* in sampledisk, stspi_begin_copy_image do nothing about copy image,
 * it just set the need_copy_image flag in the image handle,
 * when stspi_*_end_copy_image is called, do copy image to target lsu.
 *
 * for real solution, it is better to open image on target lsu, and
 * write dup data to target image when write_image function is called */
int 
stspi_begin_copy_image(
		const stsp_lsu_v7_t *target_lsu, 
		const sts_image_def_v10_t *target_image, 
		stsp_image_handle_t image_handle, 
		sts_opname_v11_t imageset,  
		int eventflag)
{
	VALIDATE(target_image);
	VALIDATE(image_handle);
	VALIDATE(strlen(imageset.op_name));

	image_handle->need_copy_image = true;
	if(target_lsu){
		image_handle->is_target_null = false;
		image_handle->target_lsu = *target_lsu;
	}
	else{
		image_handle->is_target_null = true;
	}
	image_handle->target_image = *target_image;
	image_handle->imageset = imageset;
	image_handle->eventflag = eventflag;

	return STS_EOK;
}

int 
stspi_end_copy_image(
		stsp_image_handle_t image_handle)
{
	int ret;

	if(!image_handle->need_copy_image)
		return STS_EINVAL;

	/* increase the count number of working tasks in imageset. */
	ret = mngr->getImageSet().addTask(image_handle->imageset, image_handle->eventflag, &image_handle->target_image);
	if(ret != STS_EOK)
		goto finished;

	if(image_handle->is_target_null){
		ret = copy_image_to_replication_target(&image_handle->target_image, image_handle, image_handle->imageset, image_handle->eventflag);
	}
	else{
		ret = copy_image(&image_handle->target_lsu, &image_handle->target_image, image_handle, image_handle->imageset, image_handle->eventflag);
	}

	mngr->getImageSet().decTask(image_handle->imageset);
	if(image_handle->eventflag | STS_EVFLAG_TRIGGER){
		mngr->getImageSet().release(image_handle->imageset);
	}
finished:
	return ret;
}

int 
stspi_async_end_copy_image(
		stsp_image_handle_t image_handle, 
		stsp_opid_t *opid)
{
	EndCopyImageTask *task = new EndCopyImageTask(image_handle, NULL);
	*opid = reinterpret_cast<stsp_opid_t>(task);
	return mngr->addTask(task);
}

int 
stspi_named_async_end_copy_image(
		stsp_image_handle_t image_handle, 
		const sts_opname_v11_t opname)
{
	EndCopyImageTask *task = new EndCopyImageTask(image_handle, opname.op_name);
	return mngr->addTask(task, opname.op_name);
}


int 
stspi_get_lsu_replication_prop_v11(
		const stsp_lsu_v7_t *lsu,
		sts_uint32_t nsource, 
		sts_lsu_spec_v11_t *source,
		sts_uint32_t ntarget, 
		sts_lsu_spec_v11_t *target)
{
	VALIDATE(lsu);
	
	/* Query StorageServer object to get LSU object for given LSU name */
	stsp_server_handle_t sh = lsu->sl_server_handle;
	StorageServerInfo *si = sh->sts->getInfo();
	
	LSUDefinition ldef;
	ldef.name = lsu->sl_lsu_name.sln_name;
	LSU *plsu = sh->sts->getLSU(&ldef);
	LSUInfo* plsu_info = plsu->getInfo();
	
	/* return the current lsu info */
	sts_lsu_info_t lsuInfo;
	int retval = stspi_get_lsu_prop_byname_v11(lsu, &lsuInfo);
	if ( 0 != retval ){
		return retval;
	}

	/* return the sources for this lsu */
	if ((NULL != source) && (lsuInfo.lsu_def.sld_flags & STS_LSUF_REP_TARGET)) {
		/* the source# will be the same as plsu_info->source_lsu.size(), and 
		same as lsuInfo.lsu_def.sld_reg_sources */
		if ( nsource != plsu_info->source_lsu.size()){
			return STS_EINVAL;
		}
		for (sts_uint32_t i = 0; i < nsource; i++ ){
			source[i] = plsu_info->source_lsu[i];
		}
	}
		
	/* return the targets for this lsu */
	if ((NULL != target) && (lsuInfo.lsu_def.sld_flags & STS_LSUF_REP_SOURCE)) {
		/* the target# will be the same as plsu_info->target_lsu.size(), and 
		same as lsuInfo.lsu_def.sld_reg_targets */
		if ( ntarget != plsu_info->target_lsu.size()){
			return STS_EINVAL;
		}
		for (sts_uint32_t i = 0; i < ntarget; i++ ){
			target[i] = plsu_info->target_lsu[i];
		}
	}
		
	return 0;
}

int
stspi_iocontrol_v11(
		stsp_server_handle_t server_handle, 
		int cmd,
		void *args, 
		int ioflag, 
		sts_uint32_t len)
{
	VALIDATE(server_handle);
	VALIDATE(args);

	ostringstream isinfo_stream;
	// STS_IOCTL_IN == 1
	if ( ioflag & STS_IOCTL_IN) {
		isinfo_stream <<"IN: cmd=" << cmd << ", args=" << (char*)args;
		isinfo_stream << ", ioflag=" << ioflag << ", len=" << len <<endl;

	}
	// STS_IOCTL_OUT == 2
	if ( ioflag & STS_IOCTL_OUT){
		const char ch[] = "plugin-args";
		strncpy((char*)args, ch, sizeof(ch) );

		isinfo_stream <<"OUT: cmd=" << cmd << ", args=" << ch;
		isinfo_stream << ", ioflag=" << ioflag << ", len=" << len <<endl;
	}

	LOG_INFO( server_handle->sd, isinfo_stream.str());
	return 0;
}


stspi_ep_v9_t PluginEntryPoints_v9 =
{
	NULL,
	NULL,
	NULL,
	NULL,
	stspi_claim,
	NULL,
	stspi_close_image,
	stspi_close_image_list,
	stspi_close_lsu_list,
	stspi_close_server,
	stspi_copy_extent,
	stspi_create_image_v9,
	NULL,
	NULL,
	stspi_delete_image_v9,
	NULL,
	NULL,
	NULL,
	stspi_get_image_prop_v9,
	stspi_get_image_prop_byname_v9,
	stspi_get_lsu_prop_byname_v9,
	stspi_get_server_prop,
	stspi_get_server_prop_byname,
	NULL,
	NULL,
	stspi_list_image_v9,
	stspi_list_lsu,
	NULL,
	stspi_open_lsu_list_v9,
	stspi_open_server,
	stspi_open_target_server,
	stspi_open_image_v9,
	NULL,
	stspi_open_image_list,
	stspi_read_image,
	stspi_terminate,
	stspi_write_image,
	NULL,
	stspi_label_lsu,
	stspi_read_image_meta,
	stspi_write_image_meta
};


stspi_ep_v10_t PluginEntryPoints_v10 =
{
	NULL,
	NULL,
	NULL,
	NULL,
	stspi_claim,
	NULL,
	stspi_close_image,
	stspi_close_image_list,
	stspi_close_lsu_list,
	stspi_close_server,
	stspi_copy_extent,
	stspi_create_image_v10,
	NULL,
	NULL,
	stspi_delete_image_v10,
	NULL,
	NULL,
	NULL,
	stspi_get_image_prop_v10,
	stspi_get_image_prop_byname_v10,
	stspi_get_lsu_prop_byname_v9,
	stspi_get_server_prop,
	stspi_get_server_prop_byname,
	NULL,
	NULL,
	stspi_list_image_v10,
	stspi_list_lsu,
	NULL,
	stspi_open_lsu_list_v9,
	stspi_open_server,
	stspi_open_target_server,
	stspi_open_image_v10,
	NULL,
	stspi_open_image_list,
	stspi_read_image,
	stspi_terminate,
	stspi_write_image,
	NULL,
	stspi_label_lsu,
	stspi_read_image_meta,
	stspi_write_image_meta
};

stspi_ep_v11_t PluginEntryPoints_v11 =
{
	NULL,
	stspi_async_read_image_v11,
	stspi_async_wait_v11,
	stspi_async_write_image_v11,
	stspi_claim,
	stspi_close_evchannel_v9,
	stspi_close_image,
	stspi_close_image_list,
	stspi_close_lsu_list,
	stspi_close_server,
	stspi_copy_extent,
	stspi_create_image_v10,
	stspi_delete_event_v11,
	NULL, 
	stspi_delete_image_v10,
	stspi_get_event_v11,
	NULL,
	NULL, 
	stspi_get_image_prop_v10,
	stspi_get_image_prop_byname_v10,
	stspi_get_lsu_prop_byname_v11,
	stspi_get_server_prop,
	stspi_get_server_prop_byname,
	NULL,
	NULL,
	stspi_list_image_v10,
	stspi_list_lsu,
	stspi_open_evchannel_v11,
	stspi_open_lsu_list_v11,
	stspi_open_server,
	stspi_open_target_server,
	stspi_open_image_v10,
	NULL,
	stspi_open_image_list,
	stspi_read_image,
	stspi_terminate,
	stspi_write_image,
	NULL,
	stspi_label_lsu,
	stspi_read_image_meta,
	stspi_write_image_meta,
	stspi_async_cancel_v11,
	stspi_async_copy_image_v11,
	stspi_copy_image_v11,
	stspi_get_event_payload_v11,
	stspi_named_async_cancel_v11,
	stspi_named_async_copy_image_v11,
	stspi_named_async_status_v11,
	stspi_named_async_wait_v11,
	stspi_get_server_config_v11,
	stspi_set_server_config_v11,
	stspi_begin_copy_image,
	stspi_end_copy_image,
	stspi_async_end_copy_image,
	stspi_named_async_end_copy_image,
	stspi_get_lsu_replication_prop_v11,
	stspi_iocontrol_v11
};


int
stspi_init(
	sts_uint64_t	masterVersion, 
	const char	*path,
	stspi_api_t	*stspAPI)
{
	VALIDATE(stspAPI);

	if(!mngr){
		try{
			mngr = new AsyncTaskManager;
		}
		catch(STSException & ex){
			return ex.error_code();
		}
	}

	/*
	 *  "path" argument tells the plugin what its own path is,
	 *  in case it needs to know that for some reason.
	 */
	
	switch ((sts_uint32_t)masterVersion)
	{
	case 9:
		/*
		*  This code is in place
		*  to show how a plugin might support a back rev of the core library.
		*/

		stspAPI->spx_version = 9;
		stspAPI->spx_def.spd_operating_version = 9;
		stspAPI->spx_ep.v9_ep = &PluginEntryPoints_v9;
		break;
	case 10:
		stspAPI->spx_version = 10;
		stspAPI->spx_def.spd_operating_version = 10;
		stspAPI->spx_ep.v10_ep = &PluginEntryPoints_v10;
		break;
	case 11:
		stspAPI->spx_version = 11;
		stspAPI->spx_def.spd_operating_version = 11;
		stspAPI->spx_ep.v11_ep = &PluginEntryPoints_v11;
		break;

	/*case xyz: */
		/*code to handle xyz here*/
	/*	break;*/
	default:
		return STS_EPLUGINVERSION;
	}

	stspAPI->spx_def.spd_npfx = 1;
	stspAPI->spx_def.spd_flags = STS_SPD_LOCALONLY;
	stspAPI->spx_def.spd_build_version = 11;
	stspAPI->spx_def.spd_build_version_minor = 1;
	strncpy(stspAPI->spx_def.spd_vendor_version,"v11.1 2009_11 sample plugin", STS_MAX_VENDOR_VERSION);
	strncpy(stspAPI->spx_def.spd_pfxdef[0].spp_pfx,SAMPLE_PREFIX,STS_MAX_PFXLEN );
	strncpy(stspAPI->spx_def.spd_pfxdef[0].spp_label,"SampleDisk Plugin",STS_MAX_PFX_LABEL);

	return STS_EOK;
}

