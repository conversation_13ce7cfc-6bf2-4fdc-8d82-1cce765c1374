/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

/*
 *	Object Oriented grouping of STS-P-I functions.
 *
 *	STS-P-I functions can be grouped around three objects - 
 *		StorageServer, LSU, Image.
 *	StorageServer objects represent the StorageServers known to the plugin. The plugin
 *	will create an instance of StorageServer for each StorageServer it knows. Each 
 *	StorageServer contains a group of LSUs. StorageServer has methods to get list
 *	of such LSUs. 
 *
 *	LSU object represents the 'lsu' storage entity. It has a collection of Images 
 *	stored in it. It has functions to perform open/create operations on its
 *	component images.
 *
 *	Image object represents the NetBackup image written to the storage. An image 
 *	object has read and write methods that can be performed at the times of backup,
 *	restore and other operations.
 *
 *	Each of the above objects has Info attribute. Calling getInfo returns the Info
 *	structure for the respective object.
 *
 *	LSU and Image objects have a 'Definition' object associated with them. 
 *	Definition holds properties that uniquely identify the LSU/Image object. Thus 
 *	they are subset of Info objects.
 */

#ifndef _STORAGESERVER_H_
#define _STORAGESERVER_H_

#include <string>
#include <vector>
#include <list>
#include <map>

#include "stsi.h"
#include "stspi.h"

/*
 *	Definition objects
 */
struct LSUDefinition {
	std::string name;
	sts_uint64_t max_transfer;
	sts_uint64_t block_size;
	
	sts_uint32_t lsu_flags;
	sts_uint32_t lsu_rep_sources;
	sts_uint32_t lsu_rep_targets;
};

struct ImageDefinition {
	std::string basename;
	std::string date;
	std::string fulldate;
	sts_uint64_t size;

	sts_image_def_v10_t buf;
};

/* 
 * Info objects 
 */
struct LSUInfo {
	LSUDefinition ldef;
	sts_uint64_t lsu_capacity;
	sts_uint64_t lsu_capacity_phys;
	sts_uint64_t lsu_used;
	sts_uint64_t lsu_used_phys;
	sts_uint64_t num_images;

	std::vector<sts_lsu_spec_v11_t > source_lsu;
	std::vector<sts_lsu_spec_v11_t > target_lsu;
};

struct StorageServerInfo {
	std::string name;
};

struct ImageInfo {
	ImageDefinition idef;
	std::string image_path;
};

/*
 * STS interfaces
 * These are the abstract interfaces. A typical C++ plugin code will 
 * derive classes from these interfaces and instantiate them
 * Check out SampleStorageServer class for a sample implementation of this interface
 */
class Image
{
public:
	virtual ~Image() = 0;

	virtual ImageInfo *getInfo() = 0;
	virtual int write(void *buf,
						 size_t length,
						 size_t offset,
						 sts_uint64_t *num_written) = 0;
	virtual size_t read(void *buf,
						 size_t length,
						 size_t offset) = 0;
	virtual int remove() = 0;
};

class LSU
{
public:
	virtual LSUInfo *getInfo() = 0;
	virtual int setLabel(std::string label_info) = 0;
	virtual std::string getLabel() = 0;
	virtual Image *createImage(ImageDefinition idef) = 0;
	virtual Image *openImage(ImageDefinition idef, int mode) = 0;
	virtual void deleteImage(ImageDefinition idef) = 0;
	virtual std::vector<Image *> *getImageList() = 0;
};

class StorageServer
{
public:
	virtual ~StorageServer() {};

	virtual StorageServerInfo *getInfo() = 0;
	virtual std::vector<LSU *> *getLSUList() = 0;
	virtual LSU *getLSU(LSUDefinition *lsudef) = 0;
	virtual int getConfig(char* buf, sts_uint32_t buflen, sts_uint32_t *maxlen) = 0;
	virtual int setConfig(const char *buf, char *msgbuf, sts_uint32_t msgbuflen) = 0;
	virtual int subscribeEvent(stsp_evc_handle_t eh) = 0;
	virtual int unsubscribeEvent(stsp_evc_handle_t eh) = 0;
	virtual int addEvent(sts_event_v11_t event) = 0;
	virtual int addEvent(sts_event_v11_t event, void* payload) = 0;
	virtual int getEvent(stsp_evc_handle_t eh, sts_event_v11_t *event) = 0;
	virtual int getEventPayload(const sts_event_v11_t *event, void* buf) = 0;
	virtual int delEvent(sts_event_v11_t *event) = 0;
};

#endif /* _STORAGESERVER_H_ */
