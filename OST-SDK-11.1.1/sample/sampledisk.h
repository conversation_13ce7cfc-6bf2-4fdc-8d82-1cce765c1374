/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */


#ifndef _SAMPLEDISK_H_
#define _SAMPLEDISK_H_

#include <vector>

#ifdef WIN32
#ifdef SAMPLE_PGN_EXPORTS
#define API_Export __declspec(dllexport)
#else
#define API_Export __declspec(dllimport)
#endif
#else	//UNIX
#ifdef SAMPLE_PGN_EXPORTS
#define API_Export
#else
#define API_Export extern
#endif 
#endif //WIN32

#ifdef __cplusplus
extern "C" {
#endif

/*********************************************************************************
 * Definition of handle structures for storage server, LSU list and Image
 *
 * The actual handle structure is defined as a pointer to these structures.
 * (refer to stspi.h)
 *
 * It is up to the plugin implementation to decide what will go in the handles.
 *
 * For example, in case of the sampledisk code we instantiate C++ objects. 
 * Therefore the handle definitions hold pointers to corresponding C++ objects. In
 * addition to object pointers, there exist pointers to the session for the purpose 
 * of carrying session information across STS-P-I service calls.
 *********************************************************************************/

struct stsp_server_handle_s{
	StorageServer *sts;
	const sts_session_def_t *sd;
};

struct stsp_lsu_list_handle_s{
	stsp_server_handle_t sh;
	std::vector<LSU *> *lsu_list;
	/* cursor is used while iterating over list of LSUs */
	int cursor;
};

struct stsp_image_handle_s{
	Image *iptr;
	stsp_lsu_t lsu;
	sts_image_def_v10_t img_def;
	const sts_session_def_t *sd;

	/* for begin copy image */
	bool need_copy_image;
	bool is_target_null;
	stsp_lsu_v7_t target_lsu;
	sts_image_def_v10_t target_image;
	sts_opname_v11_t imageset;
	int eventflag;
};

struct stsp_image_list_handle_s{
	stsp_server_handle_t sh;
	std::vector<Image *> *image_list;
	/* cursor is used while iterating over list of LSUs */
	int cursor;
};

struct stsp_evc_handle_s{
	stsp_server_handle_t sh;
	int flags;
	/* push mode = 1, pull mode = 0 */
	int mode;
	int pending;
	sts_evseqno_t seq;
	sts_evhandler_v11_t handler;
	sts_event_v11_t *event;
};

API_Export int
stspi_init(
	sts_uint64_t	 masterVersion, 
	const char	*path,
	stspi_api_t	*stspAPI);

API_Export int
stspi_claim(
	const sts_server_name_t serverName);

API_Export int 
stspi_get_server_prop_byname(
	const sts_session_def_t *session, 
	const sts_server_name_t  serverName, 
	sts_server_info_t	    *serverInfo);

API_Export int 
stspi_open_server(
	const sts_session_def_t *session, 
	const sts_server_name_t  sts_server_name, 
	const sts_cred_t 	    *credentials, 
	const sts_interface_t 	 stsInterface, 
	stsp_server_handle_t 	*sh);

API_Export int 
stspi_get_server_prop(
	stsp_server_handle_t  sh, 
	sts_server_info_t	 *serverInfo);


API_Export int 
stspi_get_lsu_prop_byname_v9(
	const stsp_lsu_t	*lsu, 
	sts_lsu_info_v9_t 		*lsuInfo);

API_Export int 
stspi_get_lsu_prop_byname_v11(
	const stsp_lsu_t	*lsu, 
	sts_lsu_info_v11_t 		*lsuInfo);

API_Export int
stspi_open_lsu_list_v9(
	stsp_server_handle_t sh,
	const sts_lsu_def_v9_t *lsudef,
	stsp_lsu_list_handle_t *lsu_list_handle);

API_Export int
stspi_open_lsu_list_v11(
	stsp_server_handle_t sh,
	const sts_lsu_def_v11_t *lsudef,
	stsp_lsu_list_handle_t *lsu_list_handle);

API_Export int 
stspi_list_lsu(
	stsp_lsu_list_handle_t  lsuListHandle, 
	sts_lsu_name_t	       *lsuName);

API_Export int
stspi_label_lsu(
	const stsp_lsu_t  	*lsu,
	sts_lsu_label_t		lsu_label);

API_Export int 
stspi_get_image_prop_byname_v9(
	const stsp_lsu_t      *lsu, 
	const sts_image_def_v7_t *imageDefinition, 
	sts_image_info_v7_t      *imageInfo);

API_Export int 
stspi_get_image_prop_byname_v10(
	const stsp_lsu_t      *lsu, 
	const sts_image_def_v10_t *imageDefinition, 
	sts_image_info_v10_t      *imageInfo);

API_Export int 
stspi_delete_image_v9(
	const stsp_lsu_t      *lsu, 
	const sts_image_def_v7_t *imageDefinition, 
    int                    asyncFlag);

API_Export int 
stspi_delete_image_v10(
	const stsp_lsu_t      *lsu, 
	const sts_image_def_v10_t *imageDefinition, 
    int                    asyncFlag);

API_Export int 
stspi_create_image_v9(
	const stsp_lsu_t 		      *lsu, 
	const sts_image_def_v7_t 	*imageDefinition, 
	int 			       pendingFlag, 
	stsp_image_handle_t   *imageHandle);

API_Export int 
stspi_create_image_v10(
	const stsp_lsu_t 		      *lsu, 
	const sts_image_def_v10_t *imageDefinition, 
	int 			       pendingFlag, 
	stsp_image_handle_t   *imageHandle);

API_Export int 
stspi_open_image_v9(
	const stsp_lsu_t      *lsu, 
	const sts_image_def_v7_t *imageDefinition, 
	int                    mode, 
	stsp_image_handle_t   *imageHandle);

API_Export int 
stspi_open_image_v10(
	const stsp_lsu_t      *lsu, 
	const sts_image_def_v10_t *imageDefinition, 
	int                    mode, 
	stsp_image_handle_t   *imageHandle);

API_Export int
stspi_open_image_list(
	const stsp_lsu_t *lsu,
	int type,
	stsp_image_list_handle_t *image_list_handle);

API_Export int 
stspi_list_image_v9(
	stsp_image_list_handle_t image_list_handle, 
	sts_image_def_v7_t *img);

API_Export int 
stspi_list_image_v10(
	stsp_image_list_handle_t image_list_handle, 
	sts_image_def_v10_t *img);

API_Export int 
stspi_get_image_prop_v9(
	stsp_image_handle_t  imageHandle, 
	sts_image_info_v7_t    *imageInfo);

API_Export int 
stspi_get_image_prop_v10(
	stsp_image_handle_t  imageHandle, 
	sts_image_info_v10_t    *imageInfo);

API_Export int 
stspi_read_image(
	stsp_image_handle_t  ih, 
	void			    *buf, 
	sts_uint64_t 		     length, 
	sts_uint64_t 		     offset, 
	sts_uint64_t 		    *bytesRead);

API_Export int 
stspi_write_image(
	stsp_image_handle_t  ih,  
	sts_stat_t 		    *stat, 
	void 			    *buf, 
	sts_uint64_t 		     length, 
	sts_uint64_t 		     offset, 
	sts_uint64_t 		    *bytesWritten);

API_Export int
stspi_write_image_meta(
	stsp_image_handle_t image_handle,
	void *buf, 
	sts_uint64_t len, 
	sts_uint64_t offset, 
	sts_uint64_t *byteswritten);

API_Export int 
stspi_read_image_meta(
	stsp_image_handle_t image_handle, 
	void *buf,
	sts_uint64_t len, 
	sts_uint64_t offset, 
	sts_uint64_t *bytesread);
	

API_Export int
stspi_close_image(
	stsp_image_handle_t ih, 
	int completeFlag, 
	int forceFlag);

API_Export int 
stspi_close_image_list(
	const stsp_image_list_handle_t image_list_handle);

API_Export int 
stspi_close_lsu_list(
	const stsp_lsu_list_handle_t lsuListHandle);

API_Export int 
stspi_close_server(
    stsp_server_handle_t sh);

API_Export int
stspi_terminate();

API_Export int
stspi_async_read_image_v11(
		stsp_image_handle_t image_handle,
		void *buf,
		sts_uint64_t len,
		sts_uint64_t offset,
		stsp_opid_t *opid);

API_Export int
stspi_async_wait_v11(
		const sts_session_def_v7_t *sd, 
		stsp_opid_t opid,
		int blockflag, 
		sts_aioresult_v11_t *result);

API_Export int
stspi_async_write_image_v11(
		stsp_image_handle_t image_handle,
		sts_stat_v7_t *stat,
		void *buf,
		sts_uint64_t len,
		sts_uint64_t offset,
		stsp_opid_t *opid);

API_Export int
stspi_close_evchannel_v9(
		stsp_evc_handle_t evc_handle);

API_Export int 
stspi_copy_extent(
	stsp_image_handle_t to_image,
	sts_uint64_t to_offset,
	stsp_image_handle_t from_image,
	sts_uint64_t from_offset,
	sts_uint64_t length,
	int flags,
	sts_uint64_t 	*bytesCopied);

API_Export int
stspi_delete_event_v11(
		const stsp_server_handle_t server_handle, 
		sts_event_v11_t *event);

API_Export int 
stspi_get_event_v11(
		stsp_evc_handle_t evc_handle,
		sts_event_v11_t *event);

API_Export int 
stspi_open_evchannel_v11(
		const sts_session_def_v7_t *sd,
		const sts_server_name_v7_t server, 
		const sts_cred_v7_t *cred,
		const sts_interface_v7_t iface, 
		sts_evhandler_v11_t handler,
		sts_event_v11_t *event, 
		int flags, 
		sts_evseqno_v11_t evseqno, 
		stsp_evc_handle_t *pevc_handle);

API_Export int 
stspi_async_cancel_v11(
		const sts_session_def_v7_t *sd, 
		stsp_opid_t opid);

API_Export int 
stspi_async_copy_image_v11(
		const stsp_lsu_v7_t *to_lsu, 
		const sts_image_def_v10_t *to_img,
		const stsp_lsu_v7_t *from_lsu, 
		const sts_image_def_v10_t *from_img, 
		stsp_opid_t *opid,
		const sts_opname_v11_t imageset,
		int eventflag);

API_Export int 
stspi_copy_image_v11(
		const stsp_lsu_v7_t *to_lsu, 
		const sts_image_def_v10_t *to_img,
		const stsp_lsu_v7_t *from_lsu, 
		const sts_image_def_v10_t *from_img, 
		const sts_opname_v11_t imageset,
		int eventflag);

API_Export int 
stspi_get_event_payload_v11(
		const stsp_server_handle_t server_handle, 
		void *plbuf, 
		const sts_event_v11_t *event);

API_Export int 
stspi_named_async_cancel_v11(
		stsp_server_handle_t server_handle,  
		const sts_opname_v11_t opname);

API_Export int 
stspi_named_async_copy_image_v11(
		const stsp_lsu_v7_t *target_lsu, 
		const sts_image_def_v10_t *target_image, 
		const stsp_lsu_v7_t *source_lsu, 
		const sts_image_def_v10_t *source_image, 
		const sts_opname_v11_t opname,
		const sts_opname_v11_t imageset,
		int eventflag);

API_Export int 
stspi_named_async_status_v11(
		stsp_server_handle_t server_handle,
		sts_opname_v11_t opname);

API_Export int 
stspi_named_async_wait_v11(
		stsp_server_handle_t server_handle, 
		const sts_opname_v11_t opname, 
		int blockflag, 
		sts_aioresult_v11_t *result);

API_Export int 
stspi_get_server_config_v11(
		stsp_server_handle_t server_handle,
		char *buf, 
		sts_uint32_t buflen, 
		sts_uint32_t *maxlen);

API_Export int 
stspi_set_server_config_v11(
		stsp_server_handle_t server_handle,
		const char *buf, 
		char *msgbuf, 
		sts_uint32_t msgbuflen);

API_Export int
stspi_open_target_server(
	stsp_server_handle_t     sh,
	const sts_server_name_t  sts_server_name, 
	const sts_cred_t         *credentials, 
	const sts_interface_t    stsInterface, 
	stsp_server_handle_t     *target_server_handle);

API_Export int 
stspi_begin_copy_image(
		const stsp_lsu_v7_t *target_lsu, 
		const sts_image_def_v10_t *target_image, 
		stsp_image_handle_t image_handle, 
		sts_opname_v11_t imageset,  
		int eventflag);

API_Export int
stspi_end_copy_image(
		stsp_image_handle_t image_handle);

API_Export int
stspi_async_end_copy_image(
		stsp_image_handle_t image_handle, 
		stsp_opid_t *opid);

API_Export int
stspi_named_async_end_copy_image(
		stsp_image_handle_t image_handle, 
		const sts_opname_v11_t opname);

API_Export int
stspi_get_lsu_replication_prop_v11(
		const stsp_lsu_v7_t *lsu,
		sts_uint32_t nsource, 
		sts_lsu_spec_v11_t *source,
		sts_uint32_t ntarget, 
		sts_lsu_spec_v11_t *target);

API_Export int
stspi_iocontrol_v11(
		stsp_server_handle_t server_handle, 
		int cmd,
		void *args, 
		int ioflag, 
		sts_uint32_t len);

#ifdef __cplusplus
}
#endif

#endif /* _SAMPLEDISK_H_ */
