/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

#include <sstream>
#include <algorithm>
#include "Common.h"
#include "Exception.h"
#include "EventServer.h"

#ifndef WIN32
extern "C" void*
#else
unsigned int WINAPI
#endif
eventMonitor(void* param)
{
	EventServer* srv = (EventServer*)param;

	while(srv->m_bExit == false){
		srv->refresh();
		commSleep(100);
	}

#ifndef WIN32
	return NULL;
#else
	return 0;
#endif
}

#ifndef WIN32
extern "C" void*
#else
unsigned int WINAPI
#endif
pushPastEvent(void* param)
{
	stsp_evc_handle_t eh = (stsp_evc_handle_t)param;

	/* wait until subscribeEvent function returns, 
	 * one second is recommendation */
	commSleep(1000);

	/* get and push the past event */
	while(eh->sh->sts->getEvent(eh, eh->event) == STS_EOK){
		eh->handler(eh->event);
	}
	eh->pending = 0;
#ifndef WIN32
	return NULL;
#else
	return 0;
#endif
}


EventServer::EventServer(std::string stsServerName):
	m_bExit(false),m_evseqno(0)
{
	m_evPath = commGenStorePath(stsServerName, "events");
	m_evSeqPath = m_evPath+"seq.dat";

	if(commAccess(m_evSeqPath.c_str(), 0)!= 0)
	{
		/* create the seq number touch file */
		FILE* fp = fopen(m_evSeqPath.c_str(), "w");
		fwrite(&m_evseqno, sizeof(m_evseqno), 1, fp);
		fclose(fp);
	}

	/* create event monitor thread */
	if(commThreadCreate(eventMonitor, this, &m_monitorThread) != 0)
		throw STSException("EventServer create monitor thread failed");
}

EventServer::~EventServer()
{
	/* wait for thread finishing */
	m_bExit = true;
	commThreadWait(m_monitorThread);
}

int
EventServer::subscribeEvent(stsp_evc_handle_t eh)
{
	m_lock.lock();
	if(find(m_subscribers.begin(), m_subscribers.end(), eh) == m_subscribers.end()){
		m_subscribers.push_back(eh);

		/* push the past stored event if the event's seq number is bigger than event channel's */
		eh->pending = 1;
		comm_thread_t thread_handle;
		int ret = commThreadCreate(pushPastEvent, eh, &thread_handle);

		m_lock.unlock();
		return STS_EOK;
	}
	else{
		m_lock.unlock();
		return STS_EINVAL;
	}
}

int
EventServer::unsubscribeEvent(stsp_evc_handle_t eh)
{
	m_lock.lock();
	m_subscribers.remove(eh);
	m_lock.unlock();
	/* wait until the pushPastEvent thread is terminated */
	while(eh->pending){
		commSleep(100);
	}
	return STS_EINVAL;
}

int
EventServer::addEvent(sts_event_v11_t event)
{
	bool needStore = true;

	event.version = STS_VERSION;

	m_eventlock.lock();
	/* set the event's sequence number, increase m_evseqno by 1 */
	event.ev_seqno = readSeqNumber() + 1;

	/* write event to store */
	int ret = writeEvent(event.ev_seqno, &event);

	/* write event seq number */
	writeSeqNumber(event.ev_seqno);

	m_eventlock.unlock();
	return ret;
}

int
EventServer::addEvent(sts_event_v11_t event, void* payload)
{
	bool needStore = true;

	m_eventlock.lock();
	/* put the payload into event store*/
	int ret = writeEventPayload(readSeqNumber()+1, event.ev_type.evt_detached.ed_len, payload);
	m_eventlock.unlock();

	if(ret != STS_EOK)
		return ret;

	return addEvent(event);
}

int
EventServer::getEvent(stsp_evc_handle_t eh, sts_event_v11_t *event)
{
	int ret = STS_ENOEVENT;
	m_eventlock.lock();
	sts_evseqno_t seqno = readSeqNumber();
	for(; seqno >= eh->seq; eh->seq++){
		if(readEvent(eh->seq, event) != STS_EOK)
			continue;
		/* delete the event from event store
		 * if it have inline payload and DELETE_ON_READ flag is set*/
		if((event->ev_flags & STS_EVF_INLINE) && 
			(eh->flags & STS_EVF_DELETE_ON_READ)){
			delEvent(eh->seq);
		}
		/* increase seq number of event channel */
		eh->seq++;
		ret = STS_EOK;
		break;
	}
	m_eventlock.unlock();
	return ret;
}

int
EventServer::getEventPayload(const sts_event_v11_t *event, void* buf)
{
	if(event->ev_flags&STS_EVF_INLINE || event->ev_type.evt_detached.ed_len == 0){
		return STS_EINVAL;
	}

	m_eventlock.lock();
	int ret = readEventPayload(event->ev_seqno, event->ev_type.evt_detached.ed_len, buf);
	m_eventlock.unlock();

	return ret;

}

int
EventServer::delEvent(sts_event_v11_t *event)
{
	m_eventlock.lock();
	delEvent(event->ev_seqno);
	delEventPayload(event->ev_seqno);
	m_eventlock.unlock();
	return STS_EOK;
}

void
EventServer::refresh()
{
	m_eventlock.lock();
	sts_evseqno_v11_t seqno = readSeqNumber();
	m_eventlock.unlock();
	for(; m_evseqno<=seqno; m_evseqno++){
		bool needDelete = false;
		sts_event_v11_t event;
		m_eventlock.lock();
		int ret = readEvent(m_evseqno, &event);
		m_eventlock.unlock();
		if(ret != STS_EOK)
			continue;
		std::list<stsp_evc_handle_t>::iterator it;
		/* push the event to each push mode event channel */
		m_lock.lock();
		for(it = m_subscribers.begin(); it != m_subscribers.end(); ++it){
			if((*it)->seq > m_evseqno)
				continue;
			(*it)->seq = m_evseqno+1;

			/* if DELETE_ON_READ flag is set, delete the event */
			if((*it)->flags & STS_EVF_DELETE_ON_READ){
				needDelete = true;
			}
			/* push the event */
			memcpy((*it)->event, &event, sizeof(sts_event_v11_t));
			/* fill server name into ev_server */
			StorageServerInfo* srvinfo = (*it)->sh->sts->getInfo();
			strncpy((*it)->event->ev_server, (std::string(SAMPLE_PREFIX)+":"+srvinfo->name).c_str(), sizeof((*it)->event->ev_server));
			/* push the event */
			(*it)->handler((*it)->event);
		}
		m_lock.unlock();
		if(needDelete){
			delEvent(m_evseqno);
		}
	}
}

sts_evseqno_v11_t
EventServer::readSeqNumber()
{
	sts_evseqno_v11_t seqno = 0;
	FILE* fp = fopen(m_evSeqPath.c_str(), "r");
	fread(&seqno, sizeof(seqno), 1, fp);
	fclose(fp);
	return seqno;
}

void
EventServer::writeSeqNumber(sts_evseqno_v11_t seqno)
{
	FILE* fp = fopen(m_evSeqPath.c_str(), "w");
	fwrite(&seqno, sizeof(seqno), 1, fp);
	fclose(fp);
}

int
EventServer::readEvent(sts_evseqno_v11_t n, sts_event_v11_t *event)
{
	int ret = STS_EINVAL;
	std::ostringstream ost;
	ost << m_evPath << n << ".evt";
	FILE* fp = fopen(ost.str().c_str(), "r");
	if(fp){
		if(fread(event, 1, sizeof(*event), fp) == sizeof(*event)){
			ret = STS_EOK;
		}
		fclose(fp);
	}
	return ret;
}

int
EventServer::readEventPayload(sts_evseqno_v11_t n, size_t len, void* buf)
{
	int ret = STS_EINVAL;
	std::ostringstream ost;
	ost << m_evPath << n << ".pld";
	FILE* fp = fopen(ost.str().c_str(), "r");
	if(fp){
		if(fread(buf, 1, len, fp) == len){
			ret = STS_EOK;
		}
		fclose(fp);
	}
	return ret;
}

int
EventServer::writeEvent(sts_evseqno_v11_t n, sts_event_v11_t *event)
{
	int ret = STS_EINVAL;
	std::ostringstream ost;
	ost << m_evPath << n << ".evt";
	FILE* fp = fopen(ost.str().c_str(), "w");
	if(fp){
		if(fwrite(event, 1, sizeof(*event), fp) == sizeof(*event)){
			ret = STS_EOK;
		}
		fclose(fp);
	}
	return ret;
}

int
EventServer::writeEventPayload(sts_evseqno_v11_t n, size_t len, void* buf)
{
	int ret = STS_EINVAL;
	std::ostringstream ost;
	ost << m_evPath << n << ".pld";
	FILE* fp = fopen(ost.str().c_str(), "w");
	if(fp){
		if(fwrite(buf, 1, len, fp) == len){
			ret = STS_EOK;
		}
		fclose(fp);
	}
	return ret;
}

void
EventServer::delEvent(sts_evseqno_v11_t n)
{
	std::ostringstream ost;
	ost << m_evPath << n << ".evt";
	remove(ost.str().c_str());
}

void
EventServer::delEventPayload(sts_evseqno_v11_t n)
{
	std::ostringstream ost;
	ost << m_evPath << n << ".pld";
	remove(ost.str().c_str());
}

