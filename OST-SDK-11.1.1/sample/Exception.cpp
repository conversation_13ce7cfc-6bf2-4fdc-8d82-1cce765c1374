/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

#include "Exception.h"

STSException::STSException(const char *str)
{
	m_exception_string = str;
	m_error_code = -1; 
}

STSException::STSException(const char *str, int error_code)
{
	m_exception_string = str;
	m_error_code = error_code;
}

const char *
STSException::toString()
{
	return m_exception_string.c_str();
}

int
STSException::error_code()
{
	return m_error_code;
}
