/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

#ifndef _SIMPLELOCK_H_
#define _SIMPLELOCK_H_

#ifndef WIN32
#include <pthread.h>
#else
#include <windows.h>
#endif

class SimpleLock{
	private:
#ifndef WIN32
		pthread_mutex_t m_hMutex;
#else
		CRITICAL_SECTION m_hMutex;
#endif

	public:
		SimpleLock();
		~SimpleLock();
		void lock();
		void unlock();
};

#endif /* _SIMPLELOCK_H_ */
