/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */
#include "stspi.h"

#include <algorithm>
#ifndef WIN32
#include <unistd.h>
#endif

#include "Exception.h"
#include "Common.h"
#include "SimpleLock.h"
#include "AsyncTaskManager.h"
#include "SampleStorageServer.h"
#include "sampledisk.h"

Task::Task()
{
	m_ar.aio_bytes_transferred = 0;
	/* set task status with STS_EBUSY */
	m_ar.aio_error = STS_EBUSY;
}

sts_aioresult_v11_t Task::getResult()
{
	return m_ar;
}

void Task::setResult(sts_aioresult_v11_t ar)
{
	m_ar = ar;
}

ReadImageTask::ReadImageTask(
		stsp_image_handle_t image_handle, 
		void* buf, 
		sts_uint64_t len, 
		sts_uint64_t offset):Task()
{
	m_image_handle = image_handle;
	m_buf = buf;
	m_len = len;
	m_offset = offset;
}

void ReadImageTask::run()
{
	sts_aioresult_v11_t ar;
	/* run read_image task, set task status with task result */
	ar.aio_error = stspi_read_image(m_image_handle, m_buf, m_len, m_offset, &ar.aio_bytes_transferred);
	setResult(ar);
}

WriteImageTask::WriteImageTask(
		stsp_image_handle_t image_handle,
		sts_stat_t *stat,
		void *buf,
		sts_uint64_t len,
		sts_uint64_t offset):Task()
{
	m_image_handle = image_handle;
	m_stat = stat;
	m_buf = buf;
	m_len = len;
	m_offset = offset;
}

void WriteImageTask::run()
{
	sts_aioresult_v11_t ar;
	/* run write image task, set task status with task result */
	ar.aio_error = stspi_write_image(m_image_handle, m_stat, m_buf, m_len, m_offset, &ar.aio_bytes_transferred);
	setResult(ar);
}

CopyImageTask::CopyImageTask(
		const stsp_lsu_v7_t *to_lsu,
		const sts_image_def_v10_t *to_img,
		const stsp_lsu_v7_t *from_lsu,
		const sts_image_def_v10_t *from_img,
		const char* opname,
		const sts_opname_v11_t imageset,
		int eventflag):Task()
{
	if(to_lsu){
		m_to_lsu = *to_lsu;
		m_is_target_null = false;
	}
	else
		m_is_target_null = true;
	m_to_img = *to_img;
	m_from_lsu = *from_lsu;
	m_from_img = *from_img;
	if(opname)
		m_opname = opname;
	m_imageset = imageset;
	m_eventflag = eventflag;
}

void 
CopyImageTask::run()
{
	sts_aioresult_v11_t ar;
	/* run copy image task, set task status with task result */
	ar.aio_error = stspi_copy_image_v11(m_is_target_null?NULL:&m_to_lsu, &m_to_img, &m_from_lsu, &m_from_img, m_imageset, m_eventflag);
	/* if it's a named async task, generate a STS_EVT_OP event */
	if(!m_opname.empty()){
		sts_event_v11_t event;
		event.ev_flags = STS_EVF_INLINE;
		event.ev_type.evt_inline = STS_EVT_OP;
		strncpy(event.ev_body.eb_op.e_name.op_name, m_opname.c_str(), sizeof(event.ev_body.eb_op.e_name.op_name));
		event.ev_body.eb_op.e_result = ar;
		/* send event to source storage server */
		m_from_lsu.sl_server_handle->sts->addEvent(event);
	}
	setResult(ar);
}


EndCopyImageTask::EndCopyImageTask(
		const stsp_image_handle_t image_handle,
		const char* opname):Task()
{
	m_image_handle = image_handle;
	if(opname)
		m_opname = opname;
}

void
EndCopyImageTask::run()
{
	sts_aioresult_v11_t ar;
	/* copy image to target lsu, set task status with task result */
	ar.aio_error = stspi_end_copy_image(m_image_handle);
	/* if it's a named async task, generate a STS_EVT_OP event */
	if(!m_opname.empty()){
		sts_event_v11_t event;
		event.ev_flags = STS_EVF_INLINE;
		event.ev_type.evt_inline = STS_EVT_OP;
		strncpy(event.ev_body.eb_op.e_name.op_name, m_opname.c_str(), sizeof(event.ev_body.eb_op.e_name.op_name));
		event.ev_body.eb_op.e_result = ar;
		/* send event to source storage server */
		m_image_handle->lsu.sl_server_handle->sts->addEvent(event);
	}
	setResult(ar);
}

int
ImageSet::addTask(sts_opname_v11_t imageset, int eventflag, const sts_image_def_v10_t *imgdef)
{
	imageset_map_t::iterator it;
	m_lock.lock();
	it = m_imageset_map.find(imageset.op_name);
	if(it == m_imageset_map.end()){
		imageset_t is;
		is.nworking = 1;
		is.eventflag = eventflag;
		m_imageset_map[imageset.op_name] = is;
	}
	else{
		imageset_t& is = it->second;
		if(is.eventflag | STS_EVFLAG_TRIGGER){
			m_lock.unlock();
			return STS_EBUSY;
		}
		if(is.eventflag | STS_EVFLAG_INCLUDE){
			it->second.imglist.push_back(*imgdef);
		}
		is.nworking += 1;
		is.eventflag = eventflag;
	}
	m_lock.unlock();
	return STS_EOK;
}

int
ImageSet::decTask(sts_opname_v11_t imageset)
{
	imageset_map_t::iterator it;
	m_lock.lock();
	it = m_imageset_map.find(imageset.op_name);
	if(it != m_imageset_map.end()){
		imageset_t& is = it->second;
		is.nworking -= 1;
	}
	m_lock.unlock();
	return STS_EOK;
}

void
ImageSet::wait(sts_opname_v11_t imageset)
{
	int n_working = 0;
	imageset_map_t::iterator it;
	while(1){
		m_lock.lock();
		it = m_imageset_map.find(imageset.op_name);
		if(it != m_imageset_map.end()){
			imageset_t& is = it->second;
			n_working = is.nworking;
		}
		m_lock.unlock();
		if(n_working <= 1)
			break;

		commSleep(100);
	}
}

vector<sts_image_def_v10_t>
ImageSet::getImageDefList(sts_opname_v11_t imageset)
{
	vector<sts_image_def_v10_t> imglist;
	imageset_map_t::iterator it;
	m_lock.lock();
	it = m_imageset_map.find(imageset.op_name);
	if(it != m_imageset_map.end()){
		imglist = it->second.imglist;
	}
	m_lock.unlock();
	return imglist;
}

void
ImageSet::release(sts_opname_v11_t imageset)
{
	imageset_map_t::iterator it;
	m_lock.lock();
	it = m_imageset_map.find(imageset.op_name);
	if(it != m_imageset_map.end()){
		m_imageset_map.erase(it);
	}
	m_lock.unlock();
}

/* for solaris compiler, function type need be extern "C" */
#ifndef WIN32
extern "C" void*
#else
unsigned int WINAPI
#endif
runTaskThread(void* param)
{
	AsyncTaskManager::run(param);
#ifndef WIN32
	return NULL;
#else
	return 0;
#endif
}

AsyncTaskManager::AsyncTaskManager():m_bExit(false)
{

	/* create a thread to run all async task */
	int ret = commThreadCreate(runTaskThread, this, &m_thread_handle);
	if(ret != 0)
		throw STSException("AsyncTaskManager create work thread failed", ret);
}

AsyncTaskManager::~AsyncTaskManager()
{
	/* wait for thread finishing */
	m_bExit = true;
	commThreadWait(m_thread_handle);
}

SimpleLock* AsyncTaskManager::getLock()
{
	return &m_lock;
}

void* AsyncTaskManager::run(void* param)
{
	AsyncTaskManager *mngr = (AsyncTaskManager*)param;
	while(1){
		if(mngr->m_bExit)
			break;
		mngr->getLock()->lock();
		Task* task = mngr->getTask();
		if(task){
			task->run();
		}
		mngr->getLock()->unlock();
		commSleep(10);
	}
	return NULL;
}

int AsyncTaskManager::addTask(Task* task)
{
	/* push the task into tasklist */
	m_lock.lock();
	m_task_list.push_back(task);
	m_work_task_list.push_back(task);
	m_lock.unlock();

	return STS_EOK;
}

int AsyncTaskManager::addTask(Task* task, string name)
{
	/* add the named task to <name,task> map*/
	m_lock.lock();
	if(m_name_map.find(name) != m_name_map.end()){
		m_lock.unlock();
		return STS_EINVAL;
	}
	m_name_map[name.c_str()] = task;
	m_lock.unlock();

	/* push the task into tasklist */
	return addTask(task);
}

Task* AsyncTaskManager::getTask()
{
	Task* task = NULL;
	if(!m_work_task_list.empty()){
		task = m_work_task_list.front();
		m_work_task_list.pop_front();
	}
	return task;
}

ImageSet& AsyncTaskManager::getImageSet()
{
	return m_imageset;
}

int AsyncTaskManager::waitTaskResult(
		Task* task, 
		int blockflag, 
		sts_aioresult_v11_t* result)
{
	int ret;
	for(;;){
		m_lock.lock();
		if(find(m_task_list.begin(), m_task_list.end(), task) != m_task_list.end()){
			*result = task->getResult();
			/* if the task is finished, remove it from task list */
			if(result->aio_error != STS_EBUSY){
				m_task_list.remove(task);
				delete task;
			}
			ret = STS_EOK;
		}
		else{
			ret = STS_EINVAL;
		}
		m_lock.unlock();

		/* if blockflag isn't set or task is finished, return the result */
		if(!(blockflag && ret == STS_EOK && result->aio_error == STS_EBUSY)){
			break;
		}
		commSleep(10);
	}
	return ret;
}

int AsyncTaskManager::waitTaskResult(
		string name,
		int blockflag, 
		sts_aioresult_v11_t* result)
{
	int ret;
	Task* task;

	/* get the task from <name,task> map by its "name" */
	m_lock.lock();
	name_map_t::iterator it = m_name_map.find(name);
	if(it == m_name_map.end()){
		m_lock.unlock();
		return STS_EINVAL;
	}
	task = it->second;
	m_lock.unlock();

	/* wait the task and get its result */
	ret = waitTaskResult(task, blockflag, result);

	/* if the task is finished, then remove the name entry from the map */
	if(ret == STS_EOK && result->aio_error != STS_EBUSY){
		m_lock.lock();
		m_name_map.erase(name);
		m_lock.unlock();
	}

	return ret;
}

int AsyncTaskManager::cancelTask(Task* task)
{
	int ret = STS_EINVAL;
	sts_aioresult_v11_t ar;

	m_lock.lock();
	if(find(m_task_list.begin(), m_task_list.end(), task) != m_task_list.end()){
		ar = task->getResult();
		if(ar.aio_error == STS_EBUSY){
			m_task_list.remove(task);
			m_work_task_list.remove(task);
			delete task; 
			ret = STS_EOK;
		}
	}
	m_lock.unlock();
	return  ret;
}

int AsyncTaskManager::cancelTask(string name)
{
	int ret;
	Task* task;

	m_lock.lock();
	name_map_t::iterator it = m_name_map.find(name);
	if(it == m_name_map.end()){
		m_lock.unlock();
		return STS_EINVAL;
	}
	task = it->second;
	m_lock.unlock();

	ret = cancelTask(task);

	if(ret == STS_EOK){
		m_lock.lock();
		m_name_map.erase(name);
		m_lock.unlock();
	}

	return ret;
}

int AsyncTaskManager::requestTaskStatus(stsp_server_handle_t sh, string name)
{
	Task* task;
	sts_aioresult_v11_t ar;
	sts_event_v11_t event;

	m_lock.lock();
	name_map_t::iterator it = m_name_map.find(name);
	if(it == m_name_map.end()){
		m_lock.unlock();
		return STS_EINVAL;
	}
	task = it->second;
	ar = task->getResult();
	m_lock.unlock();

	/* generate a STS_EVT_OP event for the named task */
	event.ev_flags = STS_EVF_INLINE;
	event.ev_type.evt_inline = STS_EVT_OP;
	strncpy(event.ev_body.eb_op.e_name.op_name, name.c_str(), sizeof(event.ev_body.eb_op.e_name.op_name));
	event.ev_body.eb_op.e_result = ar;
	return sh->sts->addEvent(event);
}


