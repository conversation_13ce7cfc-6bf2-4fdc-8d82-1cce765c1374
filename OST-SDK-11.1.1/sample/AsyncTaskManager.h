/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

#ifndef _ASYNC_TASK_MANAGER_H_
#define _ASYNC_TASK_MANAGER_H_

#include <vector>
#include <list>
#include <map>

#include <stspi.h>

#include "Common.h"
#include "SimpleLock.h"

using namespace std;

class Task{
	private:
		sts_aioresult_v11_t m_ar;

	public:
		Task();
		virtual ~Task(){};
		virtual sts_aioresult_v11_t getResult();
		virtual void setResult(sts_aioresult_v11_t ar);
		virtual void run()=0;
};

class ReadImageTask: public Task{
	private:
		stsp_image_handle_t m_image_handle;
		void *m_buf;
		sts_uint64_t m_len;
		sts_uint64_t m_offset;
	public:
		ReadImageTask(
				stsp_image_handle_t image_handle, 
				void* buf, 
				sts_uint64_t len, 
				sts_uint64_t offset);
		virtual void run();
};

class WriteImageTask: public Task{
	private:
		stsp_image_handle_t m_image_handle;
		sts_stat_t *m_stat;
		void *m_buf;
		sts_uint64_t m_len;
		sts_uint64_t m_offset;
	public:
		WriteImageTask(
				stsp_image_handle_t image_handle,
				sts_stat_t *stat,
				void *buf,
				sts_uint64_t len,
				sts_uint64_t offset);
		virtual void run();
};

class CopyImageTask: public Task{
	private:
		bool m_is_target_null;
		stsp_lsu_v7_t m_to_lsu;
		sts_image_def_v10_t m_to_img;
		stsp_lsu_v7_t m_from_lsu;
		sts_image_def_v10_t m_from_img;

		string m_opname;

		sts_opname_v11_t  m_imageset;
		int m_eventflag;

	public:
		CopyImageTask(
				const stsp_lsu_v7_t *to_lsu,
				const sts_image_def_v10_t *to_img,
				const stsp_lsu_v7_t *from_lsu,
				const sts_image_def_v10_t *from_img,
				const char* opname,
				const sts_opname_v11_t imageset,
				int eventflag);
		virtual void run();
};


class EndCopyImageTask: public Task{
	private:
		stsp_image_handle_t m_image_handle;
		string m_opname;

	public:
		EndCopyImageTask(
				const stsp_image_handle_t image_handle,
				const char* opname);
		virtual void run();
};

class ImageSet{
	private:
		typedef struct imageset_s{
			int nworking;
			vector<sts_image_def_v10_t> imglist;
			int eventflag;
		}imageset_t;
		typedef map<string, imageset_t> imageset_map_t;
		imageset_map_t m_imageset_map;

		SimpleLock m_lock;
		
	public:
		int addTask(sts_opname_v11_t imageset, int eventflag, const sts_image_def_v10_t *imgdef);
		int decTask(sts_opname_v11_t imageset);
		vector<sts_image_def_v10_t> getImageDefList(sts_opname_v11_t imageset);
		void wait(sts_opname_v11_t imageset);
		void release(sts_opname_v11_t imageset);
};

class AsyncTaskManager{
	private:
		bool m_bExit;

		typedef list<Task*> task_list_t;
		task_list_t m_task_list;
		task_list_t m_work_task_list;

		typedef map<string, Task*> name_map_t;
		name_map_t m_name_map;

		ImageSet m_imageset;

		SimpleLock m_lock;

		comm_thread_t m_thread_handle;

		Task* getTask();
	public:
		AsyncTaskManager();
		~AsyncTaskManager();
		SimpleLock* getLock();
		static void* run(void* param);
		int addTask(Task* task);
		int addTask(Task* task, string name);
		ImageSet& getImageSet();
		int waitTaskResult(
				Task* task, 
				int blockflag, 
				sts_aioresult_v11_t* result);
		int waitTaskResult(
				string name,
				int blockflag, 
				sts_aioresult_v11_t* result);
		int cancelTask(Task* task);
		int cancelTask(string name);
		int requestTaskStatus(stsp_server_handle_t sh, string name);
};


#endif /*_ASYNC_TASK_MANAGER_H_*/
