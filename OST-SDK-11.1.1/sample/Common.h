/*
 *************************************************************************
 * $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $
 *************************************************************************
 */

#ifndef _SAMPLECOMMON_H_
#define _SAMPLECOMMON_H_

#include <string>

#include "stsi.h"
#include "stspi.h"

#ifndef WIN32
#include <pthread.h>
#else
#include <windows.h>
#include <process.h>
#endif

#define SAMPLE_PREFIX "sampledisk"

#ifndef WIN32
#define BASE_DIR	"/tmp/"
#define DIR_SEPARATOR	"/"
#else
#define BASE_DIR	"C:\\Temp\\"
#define DIR_SEPARATOR	"\\"
#endif

void commSleep(int msec);
int commAccess(const char* path, int mode);

#ifndef WIN32
typedef pthread_t comm_thread_t;
extern "C" typedef void* (*comm_thread_func_t)(void*);
#else
typedef HANDLE comm_thread_t;
typedef unsigned (*comm_thread_func_t)(void*);
#endif
int commThreadCreate(comm_thread_func_t func, void* args, comm_thread_t* thread_handle);
void commThreadWait(comm_thread_t thread_handle);

std::string commGenStorePath(std::string serverName, std::string subPath);

#endif//_SAMPLECOMMON_H_

