<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE TS
[	
	<!ENTITY dpa_server "dpa_server">
	<!ENTITY stsmanager "/opt/stsmanager">
	<!ENTITY stype "sampledisk">
	<!ENTITY storage_server "storage_server">
	<!ENTITY stu_example "stype:server_name:lsu_name">
	<!ENTITY stu1 "&stype;:&storage_server;:vol1">
	<!ENTITY stu2 "&stype;:&storage_server;:vol2">
	<!ENTITY stu3 "&stype;:&storage_server;:vol3">
	<!ENTITY file1 "/tmp/test1.txt">
	<!ENTITY file2 "/tmp/test2.txt">
	<!ENTITY file3 "/tmp/test3.txt">
]>
<TS xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<testcase id='1'>
		<host>&dpa_server;</host>
		<port>9000</port>
		<test>&stsmanager; -backup -stu &stu1; -file &file1;</test>
	</testcase>
	<testcase id='2'>
		<host>&dpa_server;</host>
		<port>9000</port>
		<test>&stsmanager; -backup -stu &stu2; -file &file2;</test>
	</testcase>
	<testcase id='3'>
		<host>&dpa_server;</host>
		<port>9000</port>
		<test>&stsmanager; -backup -stu &stu3; -file &file3;</test>
		<depends>2</depends>
	</testcase>
</TS>