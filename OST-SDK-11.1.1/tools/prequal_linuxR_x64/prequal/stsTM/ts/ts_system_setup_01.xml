<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE TS
[	
	<!ENTITY verbose "3">
]>
<TS xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<description>Setup the storage server and LSUs on each Media Server</description>
	<testcase id='0'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -cleardb</test>
	</testcase>
	<testcase id='1'>
		<host>${media_server_B}</host>
		<port>${port_B}</port>
		<test>${prequal_bin_path_B}stsconfig -cleardb</test>
	</testcase>
	<testcase id='2'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -creatests -stype ${stype_A} -storage_server ${storage_server_A} -verbose &verbose;</test>
		<depends>0</depends>
	</testcase>
	<testcase id='3'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -addcred -stype ${stype_A} -storage_server ${storage_server_A} -user_id ${user_id_A} -password ${password_A} -verbose &verbose;</test>
		<depends>2</depends>
	</testcase>
	<testcase id='4'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -listlsu -stype ${stype_A} -storage_server ${storage_server_A} -verbose &verbose;</test>
		<depends>3</depends>
	</testcase>
	<testcase id='5'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -creatests -stype ${stype_B} -storage_server ${storage_server_B} -verbose &verbose;</test>
		<depends>4</depends>
	</testcase>
	<testcase id='6'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -addcred -stype ${stype_B} -storage_server ${storage_server_B} -user_id ${user_id_B} -password ${password_B} -verbose &verbose;</test>
		<depends>5</depends>
	</testcase>
	<testcase id='7'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -listlsu -stype ${stype_B} -storage_server ${storage_server_B} -verbose &verbose;</test>
		<depends>6</depends>
	</testcase>
	<testcase id='8'>
		<host>${media_server_B}</host>
		<port>${port_B}</port>
		<test>${prequal_bin_path_B}stsconfig -creatests -stype ${stype_A} -storage_server ${storage_server_A} -verbose &verbose;</test>
		<depends>1</depends>
	</testcase>
	<testcase id='9'>
		<host>${media_server_B}</host>
		<port>${port_B}</port>
		<test>${prequal_bin_path_B}stsconfig -addcred -stype ${stype_A} -storage_server ${storage_server_A} -user_id ${user_id_A} -password ${password_A} -verbose &verbose;</test>
		<depends>8</depends>
	</testcase>
	<testcase id='10'>
		<host>${media_server_B}</host>
		<port>${port_B}</port>
		<test>${prequal_bin_path_B}stsconfig -listlsu -stype ${stype_A} -storage_server ${storage_server_A} -verbose &verbose;</test>
		<depends>9</depends>
	</testcase>
	<testcase id='11'>
		<host>${media_server_B}</host>
		<port>${port_B}</port>
		<test>${prequal_bin_path_B}stsconfig -creatests -stype ${stype_B} -storage_server ${storage_server_B} -verbose &verbose;</test>
		<depends>1</depends>
	</testcase>
	<testcase id='12'>
		<host>${media_server_B}</host>
		<port>${port_B}</port>
		<test>${prequal_bin_path_B}stsconfig -addcred -stype ${stype_B} -storage_server ${storage_server_B} -user_id ${user_id_B} -password ${password_B} -verbose &verbose;</test>
		<depends>11</depends>
	</testcase>
	<testcase id='13'>
		<host>${media_server_B}</host>
		<port>${port_B}</port>
		<test>${prequal_bin_path_B}stsconfig -listlsu -stype ${stype_B} -storage_server ${storage_server_B} -verbose &verbose;</test>
		<depends>12</depends>
	</testcase>
</TS>