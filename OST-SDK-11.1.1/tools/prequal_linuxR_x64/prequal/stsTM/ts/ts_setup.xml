<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE TS
[	
	<!ENTITY dpa_server "dpa_server">
	<!ENTITY stsconfig "/opt/stsconfig">
	<!ENTITY stype "sampledisk">
	<!ENTITY storage_server "storage_server">
	<!ENTITY user_id "user">
	<!ENTITY password "password">
	<!ENTITY verbose "3">
]>
<TS xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<testcase id='1'>
		<host>&dpa_server;</host>
		<port>9000</port>
		<test>&stsconfig; -creatests -stype &stype; -storage_server &storage_server; -verbose &verbose;</test>
	</testcase>
	<testcase id='2'>
		<host>&dpa_server;</host>
		<port>9000</port>
		<test>&stsconfig; -addcred -stype &stype; -storage_server &storage_server; -user_id &user_id; -password &password; -verbose &verbose;</test>
		<depends>1</depends>
	</testcase>
	<testcase id='3'>
		<host>&dpa_server;</host>
		<port>9000</port>
		<test>&stsconfig; -listlsu -stype &stype; -storage_server &storage_server; -verbose &verbose;</test>
		<depends>2</depends>
	</testcase>
</TS>