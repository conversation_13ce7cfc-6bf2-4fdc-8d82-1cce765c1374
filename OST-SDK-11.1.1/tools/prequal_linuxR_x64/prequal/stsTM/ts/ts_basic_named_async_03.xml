<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE TS
[	
	<!ENTITY verbose "3">
]>
<TS xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<testcase id='0'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -cleardb</test>
	</testcase>
	<testcase id='1'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -creatests -stype ${stype_A} -storage_server ${storage_server_A} -verbose &verbose;</test>
		<depends>0</depends>
	</testcase>
	<testcase id='2'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -addcred -stype ${stype_A} -storage_server ${storage_server_A} -user_id ${user_id_A} -password ${password_A} -verbose &verbose;</test>
		<depends>1</depends>
	</testcase>
	<testcase id='3'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -listlsu -stype ${stype_A} -storage_server ${storage_server_A} -verbose &verbose;</test>
		<depends>2</depends>
	</testcase>
	<testcase id='4'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsmanager -backup -stu ${stype_A}:${storage_server_A}:${lsu_A_1} -file ${file_A_1}</test>
		<depends>3</depends>
	</testcase>
	<testcase id='5'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsmanager -named_async_opdup -backupid ${4::backupid} -target ${stype_A}:${storage_server_A}:${lsu_A_1} -event-mode get-status</test>
		<depends>4</depends>
	</testcase>
	<testcase id='6'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsmanager -restore -backupid ${4::backupid} -file ${file_A_1_RESTORE} -copy ${5::copy}</test>
		<depends>5</depends>
	</testcase>
	<testcase id='7'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${ststm_bin_path_A}stsTMFileCompare.pl -file ${file_A_1} -file ${file_A_1_RESTORE}</test>
		<depends>6</depends>
	</testcase>
	<testcase id='8'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -cleardb</test>
		<depends>7</depends>
	</testcase>
</TS>
