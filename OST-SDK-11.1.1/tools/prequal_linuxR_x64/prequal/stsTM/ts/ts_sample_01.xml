<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE TS
[	
	<!ENTITY verbose "3">
]>
<!--
	<TS> contains a series of XML <testcase> tags identified by the "id" attribute.
-->
<TS xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<!--
		The start of the testcase.  Each testcase MUST have a unique id.
	-->
	<testcase id='0'>
		<!--
			The host is a variable set in the environment file under XML tag <media_server_A>
		-->
		<host>${media_server_A}</host>
		<!--
			The port is a variable set in the environment file under XML tag <port_A>
		-->
		<port>${port_A}</port>
		<!--
			The path to the stsconfig executable is a variable set in the
			environment file under XML tag <prequal_bin_path_A>
		-->
		<test>${prequal_bin_path_A}stsconfig -cleardb</test>
	</testcase>
	<!--
		The testcase unique id needs to be in alpha-numeric
		order in order for the tescases to execute in the proper order
	-->
	<testcase id='1'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<!--
			&verbose; is an XML ENTITY set in the above document prolog !ENTITY tag
		-->
		<test>${prequal_bin_path_A}stsconfig -creatests -stype ${stype_A} -storage_server ${storage_server_A} -verbose &verbose;</test>
		<!--
			This testcase "depends" on testcase id=0 to have completed and testcase id=0 
			test result must == PASSED in order for this testcase to execute. 
		-->
		<depends>0</depends>
	</testcase>
	<testcase id='2'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -addcred -stype ${stype_A} -storage_server ${storage_server_A} -user_id ${user_id_A} -password ${password_A} -verbose &verbose;</test>
		<depends>1</depends>
	</testcase>
	<testcase id='3'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -listlsu -stype ${stype_A} -storage_server ${storage_server_A} -verbose &verbose;</test>
		<depends>2</depends>
	</testcase>
	<testcase id='4'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsmanager -backup -stu ${stype_A}:${storage_server_A}:${lsu_A_1} -file ${file_A_1} -checkpoints 4 -fragcount 2 -exit 2:2</test>
		<depends>3</depends>
		<!--
			The OSTSDK_EXIT_STATUS value for this testcase should be "55".
			Any value otehr than 55 will FAIL this test.  If an <expect> XML tag
			is not present for any given testcase, the expected OSTSDK_EXIT_STATUS
			is "0".
		-->
		<expect>55</expect>
	</testcase>
	<testcase id='5'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<!-- 
			The backupid is a variable that is parsed from the cmdout from tesstcase id=4.
			The syntax for a variable parsed from a previous testcase is ${<testcaseid>::<data_to_get>.
			Currently, only backupids are parsed.
		-->
		<test>${prequal_bin_path_A}stsmanager -backup -stu ${stype_A}:${storage_server_A}:${lsu_A_1} -file ${file_A_1} -checkpoints 4 -fragcount 2 -resume -backupid ${4::backupid}</test>
		<depends>4</depends>
	</testcase>
	<testcase id='6'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsmanager -restore -backupid ${4::backupid} -file ${file_A_1_RESTORE}</test>
		<depends>5</depends>
	</testcase>
	<testcase id='7'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<!-- 
			Use the stsTMFileCompare.pl utility to comapare the original file and the restored file.
		-->
		<test>${ststm_bin_path_A}stsTMFileCompare.pl -file ${file_A_1} -file ${file_A_1_RESTORE}</test>
		<depends>6</depends>
	</testcase>
	<testcase id='8'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsconfig -cleardb</test>
		<depends>7</depends>
	</testcase>
</TS>