<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE TS
[	
	<!ENTITY verbose "3">
]>
<TS xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<description>Run a backup that spans two LSUs</description>
	<testcase id='0'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsmanager -backup -stu ${stype_A}:${storage_server_A}:${lsu_A_1} -span ${stype_A}:${storage_server_A}:${lsu_A_2} -forcespan -file ${file_A_1}</test>
	</testcase>
	<testcase id='1'>
		<host>${media_server_A}</host>
		<port>${port_A}</port>
		<test>${prequal_bin_path_A}stsmanager -listimages -stu ${stype_A}:${storage_server_A}:${lsu_A_1}</test>
		<depends>0</depends>
	</testcase>
</TS>
