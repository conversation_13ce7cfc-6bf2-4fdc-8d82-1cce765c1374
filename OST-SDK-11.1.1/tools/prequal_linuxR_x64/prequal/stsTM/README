
                       README 

           OpenStorage Test Manager (stsTM)

   NetBackup OpenStorage Software Development Kit 11.0.0



CONTENTS
---------------------------------------------------------------------
 - About the OpenStorage Test Manager
 - System Requirements
 - Installation
 - Components
 - Setup
 - Usage
 - Known Issues
 - Recent Changes
 - Future Enhancements
 - Terms and Conditions
 - Symantec Contact Information


ABOUT THE OPEN STORAGE TEST MANAGER	
---------------------------------------------------------------------

The stsTM is a multi-threaded test harness that executes commands
concurrently for the purpose of emulating a NetBackup environment.

sts<PERSON> calls the stsmanager and stsconfig test tools that make up the
OpenStorage Plug-in Pre-Qualification Test Tool.  The commands can be 
executed on a remote host (or hosts) or on the local host.  In addition,
several commands can be executed simultaneously.  In this way, the 
NetBackup Master/Media Server relationship can be simulated.

SYSTEM REQUIREMENTS
---------------------------------------------------------------------

The stsTM supports any operating system that the OpenStorage Plug-in 
Pre-Qualification Test Tool supports.  Please refer to the OpenStorage 
Plug-in Pre-Qualification Test Tool README.

In addition, stsTM requires Perl 5.8.8.


INSTALLATION
---------------------------------------------------------------------

Each of these installation steps must be performed on all hosts that
will be running tests with the exception of Step 2.  Step 2 only 
needs to be performed on the host running the stsTMServer.  Please
refer to the COMPONENTS section for more details on the role of
the stsTMServer.

Step 1 - Install Perl 5.8.8

Perl can either be built from scratch or a pre-built binary
for the paltform can sometimes be obtained.


--Building Perl

The source for Perl 5.8.8 can be found at:

   www.cpan.org.

Perl needs to be built with thread support. Follow the instructions
for building Perl that come with the Perl source package.  Other than
the thread support, Perl can be built using the default options. 

To build perl on various Unix platforms, run the following from
within the Perl source package directory:

   sh Configure -Dusethreads -de
   make
   make test
   make install


--Using Pre-Built Perl Binaries

For Windows systems, a pre-built version of Perl 5.8.8 with thread
support can be found at:

   www.activestate.com

If you are using a pre-built binary distribution of Perl, you need to 
make sure that binary was build with threads support.  To verify
the Perl binary has threads support, run the following command:

   perl -V

This will show Perl configuration information.  If threads support is
enabled, you should see the following information:

   usethreads=define use5005threads=undef useithreads=define


Step 2 - Install additional Perl modules 

stsTM requires some additional Perl modules that are not 
distributed with the standard package.

How to get and install the modules->

After Perl 5.8.8 is installed, run the following command:

   perl -MCPAN -e "install XML::Simple"

This will get the XML::Simple from the CPAN site and all the modules it 
depends on, and will install everything in the proper locations. 


If you are running a pre-built binary distribution of Perl, you can see 
in the XML::Simple module is installed by running the following command:

   perl -e 'use XML::Simple;'

If nothing is returned, Perl was able to locate the module. Otherwise, 
you will see Can't locate XML::Simple.pm in @INC.


Step 3 - Unpack the prequal package

Follow the installation instructions located in the OpenStorage Plug-in 
Pre-Qualification Test Tool README.


Step 4 - Read the OpenStorage SDK Test Plan

The OpenStorage SDK Test Plan provides details on the purpose of
each testsuite to be executed.



COMPONENTS
---------------------------------------------------------------------

EXECUTABLES

Located in <prequal-install-dir>/prequal/stsTM/bin/

There are two main executables that make up stsTM:
   1.  stsTMServer.pl
   2.  stsTMClient.pl

stsTMServer issues the tests to the stsTMClients.  The stsTMClients 
execute the tests and returns the command output to the stsTMServer.
The server then logs the output and determines the test result.
When all tests have been executed, the stsTMServer generates a final
report.

The stsTMServer and the stsTMClient communicate through ports.  The
stsTMClients' default port is 9000.  The stsTMServer default port 
is 9001.  The stsTMServer and the stsTMClient can run on the same host
or can be on different hosts.  The host running the stsTMServer is
acting in the capacity of a NetBackup master server.  The host running
the stsTMClient acts in the capacity of a NetBackup media server.


             -----------------   test   -----------------
env file --> |               |  ----->  |               |  ---> stsconfig
             |  stsTMServer  |          |  stsTMClient  |  ---> stsmanager
ts file ---> |               |  <-----  |               |
             -----------------  cmdout  -----------------
               |          |
               |          |
               +          +
            log file    results

The stsTMServer reads in Environment files and TestSuite files
and outputs Log files and Results.


ENVIRONMENT FILES

Located in stsTM/envs/

The environment files are XML based files that are read in to stsTMServer
and are used to set up input variables.  This is where the stype and
the storage server name, as well as other vendor specific variable data,
are set.

The default environment file is stsTM/envs/env.xml


TESTSUITE FILES

Located in stsTM/ts/

The testsuite files are XML based files that contain a series of test cases
that will be executed on the media servers.

Please refer to the OpenStorage SDK Test Plan for a detailed description 
of the included testsuites.


TESTPLAN FILES

Located in stsTM/tp/

The testplan files are XML based files that contain a series of test suites
to execute.


LOG FILES

Located in stsTM/logs/

The log files contain the stdout of the stsmanager and stsconfig commands
as well as log data from stsTMServer.  

There are 5 log levels:
0 - No logging
1 - ERROR
2 - WARNING|ERROR
3 - INFO|WARNING|ERROR
4 - DEBUG|INFO|WARNING|ERROR

The verbosity to stdout can be set using the -verbose <log level> command
line option.


REPORTS

Located in stsTM/reports

There are two final reports generated
   1.  An ascii based final report
   2.  An XML based final report

Both contain the same information.  The ascii report is human readable.  The 
XML based report can be imported to a spreadsheet application for analysis.


USAGE
---------------------------------------------------------------------

This section describes the usage of stsTM.  Calling either CLI with 
the -help option will also display and describe all options.


SET UP THE ENVIRONMENT FILE

Open the stsTM/env/env.xml file and edit the XML fields to reflect the
correct data for your environment. If you are modifying the file with
an editor that sees the XML as raw text, do not delete the enclosing
"<![CDATA]" and "]]>" text in the file fields; place the pathname
between as the sample env.xml shows.


RUN THE STSTMCLIENT ON ALL MEDIA SERVERS

Prior to running the stsTMServer, the stsTMClient MUST be running on
all media servers with plugins that are part of the test.

 
   perl stsTMClient.pl 

This will start the listening port on the media server.


RUN THE STSTMSERVER

The stsTMServer must be provided a testsuite file or a
testplan file to execute:

   perl stsTMServer.pl -ts ts_basic_backup_01.xml

The example above will execute ts_basic_backup_01.xml using the default 
environment file (stsTM/envs/env.xml).

Optionally, you can pass in an environment file:

   perl stsTMServer.pl -ts ts_basic_backup_01.xml -env env_new.xml

Alternatively, you can pass in a testplan file that will execute a series
of testsuites:

   perl stsTMServer.pl -tp tp_basic_01.xml

The example above will execute tp_basic_01.xml using the default 
environment file (stsTM/envs/env.xml).


KNOWN ISSUES
---------------------------------------------------------------------

1. Execution on Windows systems is noticeably slower than UNIX systems.


2. stsTMServer.pl generates the following error:

'No _parse_* routine defined on this driver'

This is a known issue with the Perl XML:SAX parser.  To fix this error,
edit the /usr/local/lib/perl5/site_perl/5.8.8/XML/SAX/ParserDetails.ini
file.  Remove all entries in the file.  Be sure to make a copy of 
ParserDetails.ini before saving your modifications.

Making these modifications won't affect any other the perl feature.


RECENT CHANGES
---------------------------------------------------------------------
Changes in 11.0.0:

No changes.

Changes in 10.0.0:

No changes.

Changes in 9.4.3:

No changes.


Changes in 9.4.2:
1. Added support in stsTMServer.pl for the opdup command to capture
   the Copy number returned.

2. Added test for Optimized Duplication



Changes in 9.4.1:

No changes. 


Changes in 9.4.0:

1. New testsuites to test checkpoint restart have been added.

2. Support for running test plans has been added.

3. Support for running a test suite repeatedly has been added.

4. Negative tests can be run by setting the <expect> value for
   the OST_EXIT_STATUS output.  If a testcase has no <expect>
   tag, the expected return value is 0.

5. File comparison is now executed after restores using the
   included stsTMFileCompare.pl utility.


FUTURE ENHANCEMENTS
---------------------------------------------------------------------

The stsTM currently does limited data verification.  Data verification 
will be enhanced in future revs of the test harness.


TERMS AND CONDITIONS
---------------------------------------------------------------------

SDK materials are made available under non-disclosure agreement only.
Material provided within the SDK is governed by the OpenStorage API
(also referred to as the STS API) NDA currently in place between the
partner and Symantec.  By downloading the SDK, you acknowledge this
agreement either for yourself or on behalf of your employer and agree
to be bound by its terms and conditions.


SYMANTEC CONTACT INFORMATION
---------------------------------------------------------------------

For questions specific to the OpenStorage Test Manager functionality,
or comments on how to improve the tool, send email to:

    <EMAIL>.

Your feedback will be used when planning future enhancements.

