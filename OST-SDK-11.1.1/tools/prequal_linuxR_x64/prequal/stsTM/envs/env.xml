<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE TS
[	
	<!ENTITY prequal_bin_path_A "/opt/OST-SDK-10.0.0/tools/prequal/bin/">
	<!ENTITY prequal_bin_path_B "&quot;C:\Program Files\VERITAS\NetBackup\bin&quot;\">
	<!ENTITY ststm_bin_path_A "/opt/OST-SDK-10.0.0/tools/prequal/stsTM/bin/">
	<!ENTITY ststm_bin_path_B "&quot;C:\OST-SDK-10.0.0\tools\prequal\stsTM\bin&quot;\">
	<!ENTITY stype "sampledisk">
]>
<ENV xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" >
	<!--Fragment Size -->
	<fragmentSize>65536</fragmentSize>

	<!--Media Server A -->
	<media_server_A>media_server</media_server_A>
	<port_A>9000</port_A>
	<prequal_bin_path_A>&prequal_bin_path_A;</prequal_bin_path_A>
	<ststm_bin_path_A>&ststm_bin_path_A;</ststm_bin_path_A>
	<file_A_1><![CDATA[/tmp/test1.txt]]></file_A_1>
	<file_A_2><![CDATA[/tmp/test2.txt]]></file_A_2>
	<file_A_1_RESTORE><![CDATA[/tmp/test1.txt.1]]></file_A_1_RESTORE>
	<file_A_2_RESTORE><![CDATA[/tmp/test2.txt.1]]></file_A_2_RESTORE>

	<!--Media Server B -->
	<media_server_B>media_server</media_server_B>
	<port_B>9000</port_B>
	<prequal_bin_path_B>&prequal_bin_path_B;</prequal_bin_path_B>
	<ststm_bin_path_B>&ststm_bin_path_B;</ststm_bin_path_B>
	<file_B_1><![CDATA[/tmp/test3.txt]]></file_B_1>
	<file_B_2><![CDATA[/tmp/test4.txt]]></file_B_2>
	<file_B_1_RESTORE><![CDATA[/tmp/test3.txt.1]]></file_B_1_RESTORE>
	<file_B_2_RESTORE><![CDATA[/tmp/test4.txt.1]]></file_B_2_RESTORE>


	<!--Storage Server A -->
	<stype_A>&stype;</stype_A>
	<storage_server_A>storage_server</storage_server_A>
	<user_id_A>root</user_id_A>
	<password_A>password</password_A>
	<lsu_A_1>vol1</lsu_A_1>
	<lsu_A_2>vol2</lsu_A_2>

	<!--Storage Server B -->
	<stype_B>&stype;</stype_B>
	<storage_server_B>storage_server</storage_server_B>
	<user_id_B>root</user_id_B>
	<password_B>password</password_B>
	<lsu_B_1>vol1</lsu_B_1>
	<lsu_B_2>vol2</lsu_B_2>
</ENV>
