#!/usr/bin/perl

#
# $Revision: 1.3 $
# $Date: 2007/04/05 18:54:44 $
# $Source_Device: QE/VxTAS/Test_Library/Adc/Change_Dataset.tst,v $
# $Author: jwartnic $
#***************************************************************************
#* $VRTScprght:  Copyright 1993 - 2007 Symantec Software Corporation, 
#* All Rights Reserved 
#***************************************************************************
#ecpyrght
#*****************************************************************
# This material is proprietary of VERITAS Software Corporation.  *
# Possession and use of this material shall be strictly in       *
# accordance with a license agreement between user and VERITAS   *
# Sofware Corporation, and receipt or possession does not convey *
# any rights to divulge, reproduce, or allow others to use this  *
# material without specific written authorization of VERITAS     *
# Software Corporation.  Copyright 2000-2004, an unpublished     *
# work by VERITAS Software Corporation.  All Rights Reserved.    *
#*****************************************************************
# end_header

BEGIN {
   #require minimum Perl version of 5.8.8
   #require 5.8.8;


   use Config;
   use File::Basename;

   my $prog = basename($0);
}


## perl mods
use strict;
use Getopt::Long;
use Sys::Hostname;
use File::Compare;
use File::Spec;




#----------------------------------------------
#                  main
#----------------------------------------------
## variables
our $prog = basename($0);
my @file;
my @dirs;
my $dir;
my $retval;
our $verbose  = 3;
my $help;



my $STS_EMAX          = 128;
my $OSTSDK_EOK			 = 0;
my $OSTSDK_EPARAM		 = ($STS_EMAX + 1);	#command opt error
my $OSTSDK_EOP			 = ($STS_EMAX + 2);	#operational error
my $OSTSDK_ECATALOG	 = ($STS_EMAX + 3);	#catalog database error
my $OSTSDK_ECONFIG	 = ($STS_EMAX + 4);	#config database error
my $OSTSDK_EPERM		 = ($STS_EMAX + 5);	#permissions error
my $OSTSDK_ESETUP		 = ($STS_EMAX + 6);	#prequal tool setup error
my $OSTSDK_EUNDEF		 = ($STS_EMAX + 6);	#unknown error


## Get the Cmd Line options.
Getopt::Long::GetOptions('file=s'         => \@file,
                         'verbose=s'      => \$verbose,
                         'help'           => \$help);

if ($help) {
   _help();
}

if(scalar(@file) != 2)  {
   _logMsg("ERROR", "2 file arguments are required.");
   _ostSDKError($OSTSDK_EOP);
}

foreach  (@file) {
   if (!-e $_) {
      _logMsg("ERROR", "File \[$_\] does not exist.");
      _ostSDKError($OSTSDK_EOP);
   }
}


#if a DIRECTORY
if(-d $file[0])  {

   if(! -d $file[1])  {
      _logMsg("ERROR", "Can only compare a directory to another directory");
      _ostSDKError($OSTSDK_EOP);
   }

   @dirs = File::Spec->splitdir($file[0]);
   $file[1] = File::Spec->catdir(($file[1], pop @dirs));
   #print "DIR:$file[1]\n";

   opendir(DH, $file[0]);
   my @allfiles = grep {$_ ne '.' and $_ ne '..'} readdir DH;
   closedir DH;

   foreach  (@allfiles)  {

      if(-f "$file[0]/$_")  {
         print "Comparing file:@file[0]/$_ to $file[1]/$_\n";

         if(! -e "$file[1]/$_")  {
            _logMsg("ERROR", "File \[$file[1]/$_\] does not exist.");
            _ostSDKError($OSTSDK_EOP);
         }

         $retval = _fileCompare("$file[0]/$_","$file[1]/$_");
         if(!$retval)  {
            _ostSDKError($OSTSDK_EOP);
         }
      }
   }

   _ostSDKError($OSTSDK_EOK);

}else  {
   $retval = _fileCompare($file[0],$file[1]);
   if(!$retval)  {
      _ostSDKError($OSTSDK_EOP);
   }

   _ostSDKError($OSTSDK_EOK);

}

_ostSDKError($OSTSDK_EOP);


## End main


#----------------------------------------------
#                  _fileCompare
#----------------------------------------------
sub _fileCompare {
   my ($filea, $fileb) = @_;

   if (compare($filea,$fileb) == 0) {
      return 1;
   }

   return 0;
}


#----------------------------------------------
#                  _ostSDKError
#----------------------------------------------
sub _ostSDKError {
   my ($err) = @_;

   print ("OST_EXIT_STATUS=$err\n");
   exit(0);
}

#----------------------------------------------
#                  _logMsg
#----------------------------------------------
sub _logMsg {
   my ($level, $msg) = @_;

   if($level !~ /^(INFO|WARNING|ERROR|DEBUG)$/)  {
      $level = "UNKNOWN";
   }

   if(($verbose < 4) && ($level eq "DEBUG"))  {
      return;
   }elsif(($verbose < 3) && ($level eq "INFO"))  {
      return;
   }elsif(($verbose < 2) && ($level eq "WARN"))  { 
      return;
   }elsif(($verbose < 1) && ($level eq "ERROR"))  {
      return;
   }elsif($verbose == 0)  {
      return;
   }

   print "${prog}::${level}::${msg}\n";
   return;
}


#----------------------------------------------
#                  _usage
#----------------------------------------------
sub _usage {
   print "Usage: $prog -filea <file> -fileb <file> [-verbose 1-3][-help]\n";
}



#----------------------------------------------
#                  _help
#----------------------------------------------
sub _help {
   _usage();
   print "\nThis program listen for incoming connections and executes test cases.


Options:
   -help                      print this help message and exit.
   -verbose <level>           The level of verbosity <1-3>. Default is 3.
   -fileA <file>              The file to compare.
   -fileB <file>              The file to compare.

";

   exit 0;
}


