#!/usr/bin/perl

#
# $Revision: 1.3 $
# $Date: 2007/02/14 21:28:30 $
# $Source_Device: QE/VxTAS/Test_Library/Adc/Change_Dataset.tst,v $
# $Author: jwartnic $
#***************************************************************************
#* $VRTScprght:  Copyright 1993 - 2007 Symantec Software Corporation, 
#* All Rights Reserved 
#***************************************************************************
#ecpyrght
#*****************************************************************
# This material is proprietary of VERITAS Software Corporation.  *
# Possession and use of this material shall be strictly in       *
# accordance with a license agreement between user and VERITAS   *
# Sofware Corporation, and receipt or possession does not convey *
# any rights to divulge, reproduce, or allow others to use this  *
# material without specific written authorization of VERITAS     *
# Software Corporation.  Copyright 2000-2004, an unpublished     *
# work by VERITAS Software Corporation.  All Rights Reserved.    *
#*****************************************************************
# end_header

BEGIN {
   #require minimum Perl version of 5.8.8
   require 5.8.8;


   use Config;
   use File::Basename;

   my $prog = basename($0);

   #check for threads support
   unless($Config{useithreads})  {
      print "${prog}:ERROR:Threads support required.  This version of Perl was compiled without threads support.\n";
      exit 0;
   }
}


## perl mods
use strict;
use threads;
use threads::shared;
use Getopt::Long;
use Sys::Hostname;
use IO::Socket::INET;
use Socket;



#----------------------------------------------
#                  main
#----------------------------------------------
## variables
our $prog = basename($0);
my $localHost = Sys::Hostname->hostname;
my $localPort = 9000;
our $verbose  = 3;
my $help;
my $ns;
my $buf;
my $cmdOut;
my %cmdOutArr : shared;
my $cmd;
my $tcid;                     # testcase ID
my $tmServer;
my $tmServerPort;



## Get the Cmd Line options.
Getopt::Long::GetOptions('port=s'         => \$localPort,
                         'verbose=s'      => \$verbose,
                         'help'           => \$help);

if ($help) {
   _help();
}

_logMsg("INFO", "Listening on [$localHost:$localPort]");


## Create the listening socket
my $s = new IO::Socket::INET (
                              Listen    => 16,
                              LocalAddr => $localHost,
                              LocalPort => $localPort,
                              Proto     => 'tcp',
                              ReuseAddr => 1,
                              );
die "Could not create socket: $!\n" unless $s;



while( $ns = $s->accept() ) { # wait for and accept a connection
   while( defined( $buf = <$ns> ) ) { # read from the socket

      ($tmServer, $tmServerPort, $tcid, $cmd) = split /;/, $buf, 4;

       ## create a new thread for the process
      _logMsg("INFO", "Received cmd [$buf]");
      threads->create(sub {$cmdOut = `$cmd`;
                           $cmdOutArr{$tcid} = $cmdOut; 
                           _logMsg('INFO',"[tcid:" . ($tcid) . "]cmdOut: " . $cmdOutArr{$tcid} . "\n");
                           _sendTestCaseResults($tmServer, $tmServerPort, $tcid, $cmdOut);
                          }
                     );
      $tcid++;
   }
   #_logMsg('INFO',"[tcid:" . ($tcid-1) . "]cmdOut: " . $cmdOutArr{$tcid-1} . "\n");
}

close($s);



## End main




#----------------------------------------------
#                  _sendTestCaseResults
#----------------------------------------------
sub _sendTestCaseResults {
   my ($host, $port, $tcid, $cmdOut) = @_;


   _logMsg("INFO", "Connecting on [$host:$port]");

   ## Create the sending socket
   my $s = new IO::Socket::INET (
                                 PeerAddr => $host,
                                 PeerPort => $port,
                                 Proto     => 'tcp',
                                 ReuseAddr => 1,
                                 );
   die "${prog}:ERROR:Could not create socket: $!\n" unless $s;


   $cmdOut =~ s/\n/----/g;          # format the cmdOut.  remove all \n.

   print $s "$tcid;$cmdOut";        # send the data
   close($s);                       # close the port
}



#----------------------------------------------
#                  _logMsg
#----------------------------------------------
sub _logMsg {
   my ($level, $msg) = @_;

   if($level !~ /^(INFO|WARNING|ERROR|DEBUG)$/)  {
      $level = "UNKNOWN";
   }

   if(($verbose < 4) && ($level eq "DEBUG"))  {
      return;
   }elsif(($verbose < 3) && ($level eq "INFO"))  {
      return;
   }elsif(($verbose < 2) && ($level eq "WARN"))  { 
      return;
   }elsif(($verbose < 1) && ($level eq "ERROR"))  {
      return;
   }elsif($verbose == 0)  {
      return;
   }

   print "${prog}::${level}::${msg}\n";
   return;
}


#----------------------------------------------
#                  _usage
#----------------------------------------------
sub _usage {
   print "Usage: $prog [-port #][-verbose 1-3][-help]\n";
}



#----------------------------------------------
#                  _help
#----------------------------------------------
sub _help {
   _usage();
   print "\nThis program listen for incoming connections and executes test cases.


Options:
   -help                      print this help message and exit.
   -verbose <level>           The level of verbosity <1-3>. Default is 3.
   -port <port>               The port to listen on. Default is port 9000.
";

   exit 0;
}


