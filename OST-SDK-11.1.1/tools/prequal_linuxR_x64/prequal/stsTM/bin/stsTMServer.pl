#!/usr/bin/perl

#
# $Revision: 1.24 $
# $Date: 2007/09/12 18:48:26 $
# $Source_Device: QE/VxTAS/Test_Library/Adc/Change_Dataset.tst,v $
# $Author: jwartnic $
#***************************************************************************
#* $VRTScprght:  Copyright 1993 - 2007 Symantec Software Corporation, 
#* All Rights Reserved 
#***************************************************************************
#ecpyrght
#*****************************************************************
# This material is proprietary of VERITAS Software Corporation.  *
# Possession and use of this material shall be strictly in       *
# accordance with a license agreement between user and VERITAS   *
# Sofware Corporation, and receipt or possession does not convey *
# any rights to divulge, reproduce, or allow others to use this  *
# material without specific written authorization of VERITAS     *
# Software Corporation.  Copyright 2000-2004, an unpublished     *
# work by VERITAS Software Corporation.  All Rights Reserved.    *
#*****************************************************************
# end_header

BEGIN {
   #require minimum Perl version of 5.8.8
   require 5.8.8;


   use Config;
   use File::Basename;

   my $prog = basename($0);

   #check for threads support
   unless($Config{useithreads})  {
      print "${prog}:ERROR:Threads support required.  This version of Perl was compiled without threads support.\n";
      exit 0;
   }
}


## perl mods
use strict;
use threads;
use threads::shared;
use Getopt::Long;
use Sys::Hostname;
use IO::Socket::INET;
require XML::Simple;
use Data::Dumper;
use File::Basename;
use File::Spec;
use Cwd;


## stsTM defines
my $line = "=========================\n";
my $stsTMBasePath = dirname($0);
my $stsTMLogFilesDir    = File::Spec->catdir(($stsTMBasePath, "..", "logs"));   # can't use realpath because the dir does not exist.  error will occur.
my $stsTMReportFilesDir = File::Spec->catdir(($stsTMBasePath, "..", "reports"));
my $stsTMEnvFilesDir    = Cwd::realpath(File::Spec->catdir(($stsTMBasePath, "..", "envs")));
my $stsTMTestSuitesDir  = Cwd::realpath(File::Spec->catdir(($stsTMBasePath, "..", "ts")));
my $stsTMTestPlansDir   = Cwd::realpath(File::Spec->catdir(($stsTMBasePath, "..", "tp")));

print "stsTM Information:\n";
print $line;
print "stsTM Environment Files Directory \[$stsTMEnvFilesDir\]\n";
print "stsTM Testsuites Directory \[$stsTMTestSuitesDir\]\n";
print "stsTM Testplan Directory \[$stsTMTestPlansDir\]\n";
print "stsTM Log Directory \[$stsTMLogFilesDir\]\n";
print "stsTM Reports Directory \[$stsTMReportFilesDir\]\n\n";



#----------------------------------------------
#                  main
#----------------------------------------------
my $who = "main";

## variables
our $prog = basename($0);
my $localHost = Sys::Hostname->hostname;
my $localPort = 9001;
my $repeat = 1;
our $verbose  = 1;
my $help;
my $envFile = "env.xml";            # default env file
my $envVars;
my $testPlan;
my $testSuite;
my @ts;
my %tsData;
my $testCases;
my $testCaseId;
my $testSuiteId = 0;
my %tcDB : shared;                  # hash table using tcid as keys for storing test results and output
my $tmServerListenThread;           # the thread that runs to listen for clients returning results
my $tmServerRunTCThread;            # the thread that sned the test cases to the clients
my $tmLogFile;
my $tmReportBaseName;
my $runTime = time;


## Get the Cmd Line options.
Getopt::Long::GetOptions('tp=s'           => \$testPlan,
                         'ts=s'           => \$testSuite,
                         'env=s'          => \$envFile,
                         'repeat=s'       => \$repeat,
                         'port=s'         => \$localPort,
                         'verbose=s'      => \$verbose,
                         'help'           => \$help);

if ($help) {
   _help();
}

if (!defined($testSuite) && !defined($testPlan)) {
   _logMsg("ERROR", "-tp or -ts are required.");
   _help();
}

if (defined($testSuite) && defined($testPlan)) {
   _logMsg("ERROR", "-tp and -ts are mutually exclusive.");
   _help();
}


## set up env if needed
_createTestOutputDirs();


if (defined($testSuite)) {
   push @ts, $testSuite;

   if($repeat > 1)  {
      my $i;

      for($i = 1; $i < $repeat; $i++)  {
         push @ts, $testSuite;
      }
   }
}elsif ($testPlan) {
    ## get list of testsuites from the testplan file
   _getTestSuites(File::Spec->catfile($stsTMTestPlansDir,$testPlan), \@ts);
}

print "stsTM Executing TestSuites:\n";
print $line;

foreach  (@ts) {
   print "$_\n";
   $tsData{$_}{"result"} = "__NOT_RUN__";
}


## execute the testsuites
foreach $testSuite (@ts) {
   print "Running Testsuite \[$testSuite\]>>>>>>>>>>\n";


   ##open the log file
   ($tmLogFile = basename($testSuite)) =~ s/\.xml$/_${runTime}\.${testSuiteId}\.txt/i;         # generate the log file name
   ($tmReportBaseName = basename($testSuite)) =~ s/\.xml$/_${runTime}\.${testSuiteId}/i;       # generate the Reports base name

   $testSuiteId++;

   print "$stsTMLogFilesDir/$tmLogFile\n";

   open(LOGFILE, ">> $stsTMLogFilesDir/$tmLogFile")
      or die "${prog}:ERROR:Could not open file: $!\n";


   ## get the environment variables from the environment file
   _getEnvironment(File::Spec->catfile($stsTMEnvFilesDir, $envFile), \$envVars);

   ## get list of testcases from the testsuite file
   _getTestCases(File::Spec->catfile($stsTMTestSuitesDir, $testSuite), \$testCases, $envVars);


   ## because threads by default only share one ref level,
   ## we have to declare the multi-dimentional hash
   ## in the following manner prior to using it in any thread.
   foreach $testCaseId (sort(keys %{$testCases->{'testcase'}})) {
      $tcDB{$testCaseId} = &share({});
      ($tcDB{$testCaseId}{'cmdOut'} = &share({})) = "NULL";    # store the cmdout from the test
      ($tcDB{$testCaseId}{'result'} = &share({})) = "NULL";    # store the result parsed by _handleResult()
      $tcDB{$testCaseId}{'test'}    = &share({});              # required to report back execution-time var substituiton
      $tcDB{$testCaseId}{'data'}    = &share({});              # store any data captured from the stdout in _parseForData()
      $tcDB{$testCaseId}{'host'}    = &share({});              # store the host data
   }


   ## start the listening thread
   ## to get the results from the tmClients
   $tmServerListenThread = threads->create("_startListeningSocket", ($localHost, $localPort, \%tcDB));


   ## start the main test thread
   $tmServerRunTCThread = threads->create("_runTestCaseMain", ($testCases, $localHost, $localPort, \%tcDB));


   ## wait for all results to return
   my $cmdOutNullCnt;

   while(1)  {
      $cmdOutNullCnt = 0;

      foreach $testCaseId (keys %tcDB) {
          ($cmdOutNullCnt++, next) if ($tcDB{$testCaseId}{'cmdOut'} eq "NULL");

         if (!defined($testCases->{'testcase'}{$testCaseId}{'result'})) {
            $testCases->{'testcase'}{$testCaseId}{'cmdOut'} = $tcDB{$testCaseId}{'cmdOut'};      # add the cmdOut to the $testCases
            $testCases->{'testcase'}{$testCaseId}{'test'} = $tcDB{$testCaseId}{'test'};          # reset the test.  needed because of execution-time var substituiton

            _logMsg("INFO","CMDOUT START >>>>>>>>>>>>>>>>>>>>>>>>>");
            _logMsg("INFO", "$tcDB{$testCaseId}{'host'}:testCaseID:${testCaseId}:CMD] $tcDB{$testCaseId}{'test'}");
            _logMsg("INFO", "$tcDB{$testCaseId}{'host'}:testCaseID:${testCaseId}:CMDOUT] $tcDB{$testCaseId}{'cmdOut'}");
            _logMsg("INFO","CMDOUT END <<<<<<<<<<<<<<<<<<<<<<<<<<<");

            _handleResult($testCases->{'testcase'}{$testCaseId}, $tcDB{$testCaseId}, $testCaseId);
         }
      }
      next if ($cmdOutNullCnt);
      last;
   }

   ## Terminate the listening socket by sending NULL
   my $s = new IO::Socket::INET (
                                 PeerAddr => $localHost,
                                 PeerPort => $localPort,
                                 Proto     => 'tcp',
                                 ReuseAddr => 1,
                                 );
   die "${prog}:ERROR:Could not create socket: $!\n" unless $s;

   print $s "NULL";
   close($s);

   ## get results for the testsuite execution summary
   $tsData{$testSuite}{"result"} = "PASSED";
   foreach (keys %{$testCases->{'testcase'}}) {
      if ($testCases->{'testcase'}{$_}{'result'} eq 'FAILED')  {
         $tsData{$testSuite}{"result"} = "FAILED";
         last;
      }
   }

   ## generate final reports
   _generateReport($testSuite, $testCases);
   close LOGFILE;
}

print "\nTESTSUITE EXECUTION SUMMARY:\n";
print "Testsuite\t\t\t\t\tResult\n";
print "-----------------------------------------------------------------\n";
foreach (@ts)  {
   print "$_\t\t\t$tsData{$_}{'result'}\n";
}

## End main




##
##  validate stsTM environment
##
sub _createTestOutputDirs {
   my $who = "_createTestOutputDirs";

   ## create logs and reports dirs if they don't exist
   my @requiredDirs = ($stsTMLogFilesDir, $stsTMReportFilesDir);

   foreach  (@requiredDirs)  {
      (! -d $_) && mkdir $_, 0777;
   }

   ## create unique subdirs for the log and report files
   $stsTMLogFilesDir = File::Spec->catdir(($stsTMLogFilesDir, $runTime));
   $stsTMReportFilesDir = File::Spec->catdir(($stsTMReportFilesDir, $runTime));

   (! -d $stsTMLogFilesDir) && mkdir $stsTMLogFilesDir, 0777;
   (! -d $stsTMReportFilesDir) && mkdir $stsTMReportFilesDir, 0777;

mkdir $stsTMLogFilesDir;
   print "$stsTMLogFilesDir\n";
   print "$stsTMReportFilesDir\n";
   return;
}


##
##  start the listening socket to listen
##  for return test results.
##
sub _startListeningSocket {
   my $who = "startListeningSocket";

   my ($localHost, $localPort, $tcDB) = @_;
   my $buf;
   my $ns;
   my $tcid;
   my $cmdOut;

   _logMsg("INFO", "Listening on [$localHost:$localPort]");

   ## Create the listening socket
   my $s = new IO::Socket::INET (
                                 Listen    => 25,
                                 LocalAddr => $localHost,
                                 LocalPort => $localPort,
                                 Proto     => 'tcp',
                                 ReuseAddr => 1,
                                 );
   die "Could not create the listening socket: $!\n" unless $s;


   while( $ns = $s->accept() ) {                            # wait for and accept a connection
      while( defined( $buf = <$ns> ) ) {                    # read from the socket
         ($buf =~ /^NULL/) && {close($s), return};          # terminate socket on NULL

         $buf =~ s/----/\n/g;
         ($tcid, $cmdOut) = split /;/, $buf, 2;

         $$tcDB{$tcid}{'cmdOut'} = $cmdOut;
         _logMsg("DEBUG", "listenSocket::Received [testCaseId:${tcid}:cmdOut]\n[$$tcDB{$tcid}{'cmdOut'}]");
      }
   }
}



##
##  parse the stsTM test suite xml file
##
sub _getTestSuites {
   my $who = "_getTestSuites";

   my ($tp, $ts) = @_;
   my ($testSuites, $key, $i);


   #set the variables here
    my $xs = XML::Simple->new(ContentKey=>'testsuite',ForceContent=>1);
    $$testSuites = $xs->XMLin($tp);

   ## set any default values for the testcases
   if(defined($$testSuites->{'testsuites'}{'ts'}))  {
      foreach  $key (@{$$testSuites->{'testsuites'}{'ts'}}) {
         push @$ts, $key->{testsuite};

         if(defined($key->{repeat})  && ($key->{repeat} > 1))  {
            for($i = 1; $i < $key->{repeat}; $i++)  {
               push @$ts, $key->{testsuite};
            }
         }
      }
   }

   _logMsg("DEBUG", Dumper($testSuites));

   return;
}


##
##  parse the stsTM test suite xml file
##
sub _getTestCases {
   my $who = "_getTestCases";

   my ($ts, $tc, $env) = @_;
   my $tcid;

   undef($$tc);

   #set the variables here
    my $xs = XML::Simple->new(Variables=>$env);
    $$tc = $xs->XMLin($ts);

   ## set any default values for the testcases
   foreach  $tcid (sort(keys %{$$tc->{'testcase'}})) {
      if(!defined($$tc->{'testcase'}{$tcid}{'expect'}))  {
         $$tc->{'testcase'}{$tcid}{'expect'} = 0;                 ## set the default expect value to 0
      }

      ## no other default values currently needed
   }

    _logMsg("DEBUG", Dumper($tc));

   return;
}


##
##  parse the stsTM environment xml file
##
sub _getEnvironment {
   my $who = "_getEnvironment";

   my ($env, $variables) = @_;

    my $xs = XML::Simple->new();
    $$variables = $xs->XMLin($env);

    _logMsg("DEBUG", Dumper($variables));

   return;
}


##
##  main thread for issuing tests.  spawn a thread 
##  for each test keeping in mind the test dependenices.
##  resolve tests who's dependencies are FAILED or SKIPPED
##
sub _runTestCaseMain {
   my $who = "runTestCaseMain";

   my ($testCases, $localHost, $localPort, $tcDB) = @_;
   my $testCaseId;
   my $tmClient;
   my $tmClientPort;
   my $tmClientTest;

   while(1)  {
      last if (! keys %{$testCases->{'testcase'}});

      TESTCASE: foreach $testCaseId (sort(keys %{$testCases->{'testcase'}})) {
         my $tc = $testCases->{'testcase'}{$testCaseId};                      # the testcase pool
         #_logMsg('DEBUG', "[testCaseID:${testCaseId}:TESTCASE]  Executing testCaseId [$testCaseId]");
         
         ## check for dependencies
         if(defined($tc->{depends}))  {
            #_logMsg('DEBUG', "[testCaseID:${testCaseId}:DEPENDS] Doing dependency check for testCaseId [$testCaseId]");
            next TESTCASE if($tcDB{$tc->{depends}}{'result'} eq "NULL");               # move on to the next test if dependency has not been resolved

            ## handle if the dependency resolved to a FAILED or SKIPPED result
            if($tcDB{$tc->{depends}}{'result'} =~ /^(FAILED|SKIPPED)/)  {
               $$tcDB{$testCaseId}{'result'} = "SKIPPED";
               $$tcDB{$testCaseId}{'cmdOut'} = "NO DATA";
               $tcDB{$testCaseId}{'test'} = $tc->{'test'};

               delete $testCases->{'testcase'}{$testCaseId};                  # test resolved.  remove this test from the testcase pool

               _logMsg('WARNING', "[testCaseID:${testCaseId}:DEPENDS] Dependency testCaseID [$tc->{depends}] resolved to $tcDB{$tc->{depends}}{'result'}");
               next TESTCASE;
            }
         }

         ## set host, port, and test
         if(!defined($tc->{host}))  {
            $tmClient = $localHost;
         }else  {
            $tmClient = $tc->{host};
         }

         $tcDB{$testCaseId}{'host'} = $tc->{'host'};
         (!defined($tc->{port})) ? ($tmClientPort = $localPort) : ($tmClientPort = $tc->{port});
         (!defined($tc->{test})) ? (_logMsg("ERROR", "<test> is a required tag for testcase [$testCaseId]"), exit) : ($tmClientTest = $tc->{test});

         ##make any execution-time variable 
         ##substitutions in the test if necessary
         if(_isSubstitutionRequired($tmClientTest))  {
            _substitueData($tcDB, \$tmClientTest);       ## substitue the data
         }

         $tcDB{$testCaseId}{'test'} = $tmClientTest;  ## report the test ran.  includes any execution-time var substition.


         ## start the test threads
         threads->create("_runTestCase", ($tmClient, $tmClientPort, $tmClientTest, $testCaseId, $localHost, $localPort, ));  #->join();
         delete $testCases->{'testcase'}{$testCaseId};                        # test resolved.  remove this test from the testcase pool
      }
   }
}


##
##  create a sending socket and send the test
##  off to the stsTMClient
##
sub _runTestCase {
   my $who = "_runTestCase";

   my ($tmClient, $tmClientPort, $tmClientTest, $tcid, $localHost, $localPort) = @_;

   _logMsg("INFO", "Connecting on [$tmClient:$tmClientPort]");

   ## Create the sending socket
   my $s = new IO::Socket::INET (
                                 PeerAddr => $tmClient,
                                 PeerPort => $tmClientPort,
                                 Proto     => 'tcp',
                                 ReuseAddr => 1,
                                 );
   die "${prog}:ERROR:Could not create socket: $!\n" unless $s;

   print $s "$localHost;$localPort;$tcid;$tmClientTest";
   close($s);

   _logMsg("INFO", "Host\[$tmClient] Issued TestCase\[$tcid:$tmClientTest\]");
   return;
}


##
##  handle logging levels.
##  format messages.
##
sub _logMsg {
   my ($level, $msg) = @_;

   if($level !~ /^(INFO|WARNING|ERROR|DEBUG)$/)  {
      $level = "UNKNOWN";
   }

   print LOGFILE "${prog}::${level}::${msg}\n";

   if(($verbose < 4) && ($level eq "DEBUG"))  {
      return;
   }elsif(($verbose < 3) && ($level eq "INFO"))  {
      return;
   }elsif(($verbose < 2) && ($level eq "WARN"))  { 
      return;
   }elsif(($verbose < 1) && ($level eq "ERROR"))  {
      return;
   }elsif($verbose == 0)  {
      return;
   }

   print "${prog}::${level}::${msg}\n";
   return;
}


##
##  parse the cmd out for the results. 
##  if NO DATA, set to SKIPPED.
##
sub _handleResult {
   my $who = "_handleResult";

   my ($testCase, $tcDB, $testCaseId) = @_;
   my $cmdOut = $testCase->{'cmdOut'};
   my $line;
   my $lenCmdOut;
   my $cntCmdOut = 1;

   ## handle a skipped command
   if($cmdOut =~ /^NO DATA$/)  {
      $testCase->{'result'} = 'SKIPPED';
      _logMsg('ERROR', "[testCaseID:${testCaseId}:RESULT] $testCase->{'result'}");
      return;
   }

   $lenCmdOut = scalar(split /\n/, $cmdOut);
   _logMsg('DEBUG', "LENCMDOUT: [$lenCmdOut]\n");

   ## parse the cmdOut for Errors or for Passing
   foreach (split /\n/, $cmdOut) {
      /OST_EXIT_STATUS=(.*)/ && do {($1 == $testCase->{'expect'}) ? ($testCase->{'result'}='PASSED') : ($testCase->{'result'}='FAILED'); last;};  # PASS if OST_EXIT_STATUS == OSTSDK_EOK
      $cntCmdOut++;
   }

   (!defined($testCase->{'result'})) && ($testCase->{'result'} = 'FAILED');

   ##call _parseForData if PASSED before setting $tcDB->{'result'}
   ##to avoid a potential race condition where a testcase depends
   ##on the parsed data, but begins to exectue as soon as $tcDB->{'result'}
   ##is set to PASSED.
   if($testCase->{'result'} =~ /PASSED/)  {
      _parseForData($testCase, $tcDB, $testCaseId);
   }

   $tcDB->{'result'} = $testCase->{'result'};
   if($testCase->{'result'} eq 'PASSED')  {
      _logMsg('INFO', "[testCaseID:${testCaseId}:RESULT] $testCase->{'result'}");
   }else  {
      _logMsg('ERROR', "[testCaseID:${testCaseId}:RESULT] $testCase->{'result'}");
   }

   return;
}



##
##  parse the cmd out for data needed for 
##  subsequent cmds
##
sub _parseForData {
   my $who = "_parseForData";

   my ($testCase, $tcDB, $testCaseId) = @_;
   my $cmdOut = $testCase->{'cmdOut'};
   my $test = $testCase->{'test'};
   my %data;

   ## handle a skipped command
   if($cmdOut =~ /^NO DATA$/)  {
      $testCase->{'result'} = 'SKIPPED';
      _logMsg('ERROR', "[testCaseID:${testCaseId}:RESULT] $testCase->{'result'}");
      return;
   }

   ## parse the cmdOut for Data
   SWITCH:  {
      ($test =~ /stsmanager\s+-backup/)  && do  {
                                                   foreach (split /\n/, $cmdOut) {
                                                      /Successfully\s+executed\s+the\s+backup\s+(.*)\s+\*\*\*/ && ($data{'backupid'} = $1);
                                                      /Exiting.*?backup\s+(.*)\s+\*\*\*/ && ($data{'backupid'} = $1);
                                                      /Suspended.*?backup\s+(.*)\s+\*\*\*/ && ($data{'backupid'} = $1);
                                                   }
                                                   last SWITCH;
                                                 };
      ($test =~ /stsmanager\s+-opdup/)  && do  {
                                                   foreach (split /\n/, $cmdOut) {
                                                      /Successfully\s+executed\s+the\s+duplication\.\s+Created\s+copy\s+(.*)\s+\*\*\*/ && ($data{'copy'} = $1);
                                                   }
                                                   last SWITCH;
                                                 };
   }


   foreach (keys %data) {
      _logMsg('DEBUG', "[ $testCaseId:DATA::$_]=>$data{$_};");
      $tcDB->{'data'}{$_} = $data{$_};


   }

   return;
}



##
##  determing if variable substitution is requred for the test
##
sub _isSubstitutionRequired {
   my $who = "_isSubstitutionRequired";

   my $test = @_[0];
   my $isRequired = 0;

   ($test =~ /\$\{(.*?)::(.*?)\}/)  && ($isRequired = 1);
   _logMsg('DEBUG', "[$who:Substitution Required] $isRequired");

   return $isRequired;
}


##
##  substitute the data
##
sub _substitueData {
   my $who = "_substituteData";

   my ($tcDB, $tmClientTest) = @_;

   $$tmClientTest =~ s/\$\{(.*?)::(.*?)\}/$tcDB{$1}{'data'}{$2}/g;

   ## this will only print out the last substitution
   _logMsg('DEBUG', "[$who:SUBSTITUTING DATA] $$tmClientTest");

}


##
##  entry point for final report generation
##
sub _generateReport  {
    my $who = "_generateReport";

   my ($testSuite, $testCases) = @_;
   my $tmReportData;
   my $tmReportFile;

   ## print out the text-style report
   $tmReportData = _generateTextReport($testSuite, $testCases, \$tmReportFile);
   print STDOUT $tmReportData;

   ## write out the xml-style report
   $tmReportData = _generateXMLReport($testSuite, $testCases, \$tmReportFile);
   _logMsg('INFO', "Writing report [$stsTMReportFilesDir\/$tmReportFile]");

   open(REPORT, "+> $stsTMReportFilesDir/$tmReportFile")
      or die "${prog}:ERROR:Could not open file: $!\n";

   print REPORT $tmReportData;

   close REPORT;
   return;
}


##
##  create an ascii based report
##
sub _generateTextReport  {
   my $who = "_generateTextreport";

   my ($testSuite, $testCases, $tmReportFile) = @_;
   my $passed = 0;
   my $failed = 0;
   my $skipped = 0;
   my $total;
   my $tcid;
   my $tcInfo;

   ## generate the report file name
   $$tmReportFile = $tmReportBaseName . "_FinalReport\.txt";

   ## format the contents of the file
   my $format = "$testSuite Final Report\n\n";
   $format .= "TCID\tRESULT\tHOST\tTEST\n";
   $format .= "-----------------------------------------------------------------\n";

   foreach $tcid (sort keys %{$testCases->{'testcase'}}) {
      $tcInfo = $testCases->{'testcase'}{$tcid};

      $passed++ if ($tcInfo->{'result'} eq 'PASSED');
      $failed++ if ($tcInfo->{'result'} eq 'FAILED');
      $skipped++ if ($tcInfo->{'result'} eq 'SKIPPED');


      $format .= "$tcid\t$tcInfo->{'result'}\t$tcInfo->{'host'}\t$tcInfo->{'test'}\n";
   }

   $total = $passed + $failed + $skipped;
   $format .= "
TEST STATS:
============================
Total:   $total
Passed:  $passed
Failed:  $failed
Skipped: $skipped\n";

   return $format;
}


##
##  create a xml based report
##
sub _generateXMLReport  {
   my $who = "_generateXMLReport";

   my ($testSuite, $testCases, $tmReportFile) = @_;
   my $passed = 0;
   my $failed = 0;
   my $skipped = 0;
   my $total;
   my $tcid;
   my $tcInfo;

   ## generate the report file name
   $$tmReportFile = $tmReportBaseName . "_FinalReport\.xml";

   ## format the contents of the file
   my $format = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
   $format .= "<TestResults xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">";

   foreach $tcid (keys %{$testCases->{'testcase'}}) {
      $tcInfo = $testCases->{'testcase'}{$tcid};

      $passed++ if ($tcInfo->{'result'} eq 'PASSED');
      $failed++ if ($tcInfo->{'result'} eq 'FAILED');
      $skipped++ if ($tcInfo->{'result'} eq 'SKIPPED');

      $format .= 
"
<TestCase>
    <TestCaseID>$tcid</TestCaseID>
    <StartTime></StartTime>
    <EndTime></EndTime>
    <TotalTime></TotalTime>
    <Test>$tcInfo->{'test'}</Test>
    <Host>$tcInfo->{'host'}</Host>
    <Result>$tcInfo->{'result'}</Result>
</TestCase>";
   }

   $total = $passed + $failed + $skipped;
   $format .= 
"
<TestStats>
    <TotalTests>$total</TotalTests>
    <TotalPass>$passed</TotalPass>
    <TotalFail>$failed</TotalFail>
    <TotalSkipped>$skipped</TotalSkipped>
</TestStats>
</TestResults>";

   return $format;
}


##
##  print the usage
##
sub _usage {
   print "Usage: $prog -ts <file> [-env <file>][-port <port>][-verbose <1-3>][-help]\n";
}


##
##  print the help
##
sub _help {
   _usage();
   print "\nThis program reads the testsuite files and issues the testcases
out to the media servers that will execute the tests.


Options:
   -help                      print this help message and exit.
   -verbose <level>           The level of verbosity <1-3>. Default is 1.
   -port <port>               The port to listen on. Default is port 9001.
   -ts <ts>                   The testsuite to execute.
   -env <env>                 The environment file to load.  Default is env.xml.
   -repeat <n>                Execute the testsuite <n> number of times.
   -tp                        The testplan to execute.
";

   exit 0;
}


