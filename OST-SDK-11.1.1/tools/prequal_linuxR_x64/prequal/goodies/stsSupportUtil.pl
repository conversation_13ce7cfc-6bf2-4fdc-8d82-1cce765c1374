#!/usr/bin/perl

#
# $Revision: 1.1 $
# $Date: 2007/04/11 20:23:59 $
# $Source_Device: QE/VxTAS/Test_Library/Adc/Change_Dataset.tst,v $
# $Author: jwartnic $
#***************************************************************************
#* $VRTScprght:  Copyright 1993 - 2007 Symantec Software Corporation, 
#* All Rights Reserved 
#***************************************************************************
#ecpyrght
#*****************************************************************
# This material is proprietary of VERITAS Software Corporation.  *
# Possession and use of this material shall be strictly in       *
# accordance with a license agreement between user and VERITAS   *
# Sofware Corporation, and receipt or possession does not convey *
# any rights to divulge, reproduce, or allow others to use this  *
# material without specific written authorization of VERITAS     *
# Software Corporation.  Copyright 2000-2004, an unpublished     *
# work by VERITAS Software Corporation.  All Rights Reserved.    *
#*****************************************************************
# end_header

BEGIN {
   #require minimum Perl version of 5.8.8
   #require 5.8.8;


   use Config;
   use File::Basename;

   my $prog = basename($0);
   my $OS = $^O;
   if($OS =~ /Win/i)  {
      require Win32;
   }
}


## perl mods
use strict;
use Getopt::Long;
use Sys::Hostname;
use File::Spec;
use File::Copy;


#----------------------------------------------
#                  main
#----------------------------------------------
## variables
our $prog = basename($0);
my $localHost = Sys::Hostname->hostname;
my $logDir = $localHost;
my $OS = $^O;
our $verbose  = 3;
my $help;
my $retval;
my $prequalBin = File::Spec->curdir();
my $stsTMLogDir;
my $stsTMReportDir;
my $copyTo = File::Spec->curdir();
my $cmdOut;
my $logVolume;
my $logPath;
my $logFile;
my $logDate;
my @logFiles = ('stsmanager', 'stsconfig');
my @reportFiles;
my %servers;
my $stype;
my $server;



my $STS_EMAX			 = 128;
my $OSTSDK_EOK			 = 0;
my $OSTSDK_EPARAM		 = ($STS_EMAX + 1);	#command opt error
my $OSTSDK_EOP			 = ($STS_EMAX + 2);	#operational error
my $OSTSDK_ECATALOG	 = ($STS_EMAX + 3);	#catalog database error
my $OSTSDK_ECONFIG	 = ($STS_EMAX + 4);	#config database error
my $OSTSDK_EPERM		 = ($STS_EMAX + 5);	#permissions error
my $OSTSDK_ESETUP		 = ($STS_EMAX + 6);	#prequal tool setup error
my $OSTSDK_EUNDEF		 = ($STS_EMAX + 6);	#unknown error


## Get the Cmd Line options.
Getopt::Long::GetOptions('bin=s'          => \$prequalBin,
                         '-tmlog=s'       => \$stsTMLogDir,
                         'copyto=s'       => \$copyTo,
                         'verbose=s'      => \$verbose,
                         'help'           => \$help);

if ($help) {
   _help();
}


## run stsconfig to put basic data in the logs
$cmdOut = `$prequalBin/stsconfig -plugininfo`;

## get the log file name
foreach (split /\n/, $cmdOut) {
   /Log\s+file\s+(.*)/  && ($logFile = $1);
}

if(!$logFile)  {
   _logMsg("ERROR", "Log file name not discovered.");
   _ostSDKError($OSTSDK_EOP);
}

_logMsg("DEBUG", "Logfile[$logFile]");
($logVolume,$logPath,$logFile) = File::Spec->splitpath($logFile);
#($logDate = $logFile) =~ s/stsconfig_//;
($logFile =~ /stsconfig_(.*?)\.log/) && ($logDate = $1);

$cmdOut = `$prequalBin/stsconfig -queryserver`;
foreach (split /\n/, $cmdOut) {
   /Server\s+\[(.*?):(.*?)\]/  && ($servers{$1} = $2, _logMsg("DEBUG", "Stype[$1]:Storage_Server[$2]"));
}

foreach $stype (keys %servers) {

   $server = $servers{$stype};
   $cmdOut = `$prequalBin/stsconfig -querylsu -stype $stype -storage_server $server`;
}

#check for the logs dir
if(!-d File::Spec->catdir(("$copyTo", "stslogs_$logDate")))  {
   $retval = mkdir File::Spec->catdir(("$copyTo", "stslogs_$logDate"));

   if(!$retval)  {
      _logMsg("ERROR", "Could not create the stslogs dir \[" . File::Spec->catdir(("$copyTo", "stslogs_$logDate")) . "\]");
      _ostSDKError($OSTSDK_EOP);
   }
}


foreach (@logFiles) {
   $logFile = $_ . "_$logDate\.log";

   copy(File::Spec->catpath( $logVolume, $logPath, $logFile), File::Spec->catfile(("$copyTo", "stslogs_$logDate"), $logFile));
}


if($stsTMLogDir)  {
   opendir STSTMLOG, $stsTMLogDir
      || {logMsg("ERROR", "Could not open the stsTM log dir \[$stsTMLogDir\]"),
      _ostSDKError($OSTSDK_EOP)};

   @logFiles = grep {/\.txt$/} readdir STSTMLOG;
   closedir STSTMLOG;

   foreach (@logFiles) {
      $logFile = $_;

      $retval = copy(File::Spec->catfile( $stsTMLogDir, $logFile), File::Spec->catfile(("$copyTo", "stslogs_$logDate"), $logFile));
      if (!$retval) {
         _logMsg("ERROR", "Could not copy the file \[" . File::Spec->catpath( $stsTMLogDir, $logFile) . "\]");
         _ostSDKError($OSTSDK_EOP);
      }
   }

   ## stsTM/reports is derived
   ## from $stsTMLogDir
   ($stsTMReportDir = $stsTMLogDir) =~ s/logs/reports/i;

   opendir STSTMREPORT, $stsTMReportDir
      || {logMsg("ERROR", "Could not open the stsTM reports dir \[$stsTMReportDir\]"),
      _ostSDKError($OSTSDK_EOP)};

   @reportFiles = grep {/\.xml$/} readdir STSTMREPORT;
   closedir STSTMREPORT;

   foreach (@reportFiles) {
      $logFile = $_;

      $retval = copy(File::Spec->catfile( $stsTMReportDir, $logFile), File::Spec->catfile(("$copyTo", "stslogs_$logDate"), $logFile));
      if (!$retval) {
         _logMsg("ERROR", "Could not copy the file \[" . File::Spec->catpath( $stsTMReportDir, $logFile) . "\]");
         _ostSDKError($OSTSDK_EOP);
      }
   }
}

#gather basic system info
$logFile = File::Spec->catfile(("$copyTo", "stslogs_$logDate"), 'sts_system_info.txt');

open FH, "+> $logFile" 
   || {logMsg("ERROR", "Could not create the file \[" . File::Spec->catfile(("$copyTo", "stslogs_$logDate"), 'sts_system_info.txt') . "\]"),
      _ostSDKError($OSTSDK_EOP)};

if($OS !~ /Win/i)  {
   $cmdOut = `uname -a`;
   print FH "$cmdOut\n";
}else  {
   my @osInfo = Win32::GetOSVersion();
   print FH "@osInfo";

}
close FH;

_ostSDKError($OSTSDK_EOK);


## End main





#----------------------------------------------
#                  _ostSDKError
#----------------------------------------------
sub _ostSDKError {
   my ($err) = @_;

   print ("OST_EXIT_STATUS=$err\n");
   exit(0);
}

#----------------------------------------------
#                  _logMsg
#----------------------------------------------
sub _logMsg {
   my ($level, $msg) = @_;

   if($level !~ /^(INFO|WARNING|ERROR|DEBUG)$/)  {
      $level = "UNKNOWN";
   }

   if(($verbose < 4) && ($level eq "DEBUG"))  {
      return;
   }elsif(($verbose < 3) && ($level eq "INFO"))  {
      return;
   }elsif(($verbose < 2) && ($level eq "WARN"))  { 
      return;
   }elsif(($verbose < 1) && ($level eq "ERROR"))  {
      return;
   }elsif($verbose == 0)  {
      return;
   }

   print "${prog}::${level}::${msg}\n";
   return;
}


#----------------------------------------------
#                  _usage
#----------------------------------------------
sub _usage {
   print "Usage: $prog -filea <file> -fileb <file> [-verbose 1-3][-help]\n";
}



#----------------------------------------------
#                  _help
#----------------------------------------------
sub _help {
   _usage();
   print "\nThis program collates all the sts log information in one locale.


Options:
   -help                      print this help message and exit.
   -verbose <level>           The level of verbosity <1-3>. Default is 3.
   -bin <path>                The path to the prequal binaries.  Defaults to current dir.
   -tmlog <path>              The path to the stsTM log dir (../stsTM/logs/nnn).
   -copyto                    The path to copy the log files.  Defaults to current dir.
";

   exit 0;
}


