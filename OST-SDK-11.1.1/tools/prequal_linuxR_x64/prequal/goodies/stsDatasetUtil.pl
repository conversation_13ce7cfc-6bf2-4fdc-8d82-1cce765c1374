#!/usr/bin/perl

#
# $Revision: 1.3 $
# $Date: 2007/04/24 13:57:31 $
# $Source_Device: QE/VxTAS/Test_Library/Adc/Change_Dataset.tst,v $
# $Author: jwartnic $
#***************************************************************************
#* $VRTScprght:  Copyright 1993 - 2007 Symantec Software Corporation, 
#* All Rights Reserved 
#***************************************************************************
#ecpyrght
#*****************************************************************
# This material is proprietary of VERITAS Software Corporation.  *
# Possession and use of this material shall be strictly in       *
# accordance with a license agreement between user and VERITAS   *
# Sofware Corporation, and receipt or possession does not convey *
# any rights to divulge, reproduce, or allow others to use this  *
# material without specific written authorization of VERITAS     *
# Software Corporation.  Copyright 2000-2004, an unpublished     *
# work by VERITAS Software Corporation.  All Rights Reserved.    *
#*****************************************************************
# end_header

BEGIN {
   #require minimum Perl version of 5.8.8
   #require 5.8.8;


   use Config;
   use File::Basename;

   my $prog = basename($0);
}


## perl mods
use strict;
use Getopt::Long;
use Sys::Hostname;
use File::Compare;
use File::Spec;




my $STS_EMAX          = 128;
my $OSTSDK_EOK			 = 0;
my $OSTSDK_EPARAM		 = ($STS_EMAX + 1);	#command opt error
my $OSTSDK_EOP			 = ($STS_EMAX + 2);	#operational error
my $OSTSDK_ECATALOG	 = ($STS_EMAX + 3);	#catalog database error
my $OSTSDK_ECONFIG	 = ($STS_EMAX + 4);	#config database error
my $OSTSDK_EPERM		 = ($STS_EMAX + 5);	#permissions error
my $OSTSDK_ESETUP		 = ($STS_EMAX + 6);	#prequal tool setup error
my $OSTSDK_EUNDEF		 = ($STS_EMAX + 6);	#unknown error




#----------------------------------------------
#                  main
#----------------------------------------------
## variables
our $prog = basename($0);
my $filePath;
my $fileCount = 1;
my $fileSize = 1024;
my $overwrite = 0;
my $retval;
our $verbose  = 3;
my $help;
my $i;
my $file;



## Get the Cmd Line options.
Getopt::Long::GetOptions('verbose=s'      => \$verbose,
                         'path=s'         => \$filePath,
                         'count=i'        => \$fileCount,
                         'size=i'         => \$fileSize,
                         'no_overwrite'   => \$overwrite,
                         'help'           => \$help);

if ($help) {
   _help();
}

if(!$filePath)  {
   _logMsg("ERROR", "[path] is a required cmd line param.");
   _ostSDKError($OSTSDK_EPARAM);
}


if(! -d $filePath)  {
   _logMsg("ERROR", "Path \[$filePath\] does not exist.");
   _ostSDKError($OSTSDK_EPARAM);
}

for($i=0; $i<$fileCount; $i++)  {
   $file = "test${fileSize}\.${i}\.txt";
   $file = File::Spec->catfile( $filePath, $file);

   _logMsg("INFO", "Creating file \[$file\]");

   $retval = _createFile($file, $fileSize, $overwrite);
   if(!$retval)  {
      _logMsg("ERROR", "Could not create the file.");
      _ostSDKError($OSTSDK_EOP);
   }
}

## End main


#----------------------------------------------
#                  _createFile
#----------------------------------------------
sub _createFile {

   my ($fileName, $fileSize, $overwrite)  = @_;
   my $buffer;
   my $retval;
   my $size;
   my $fBuffer = 0;

   ## see if the file exists
   if (-e $fileName) {

      if ($overwrite) {
         _logMsg("ERROR", "The file exists and overwrite is not allowed.");
         return 0;
      } else {
         unlink $fileName;
      }

   }

   ##create the file
   open (FILE, ">>$fileName") or do {
      _logMsg("ERROR", "Could not open the file.");
      return 0;
   };

   ## if fileSize > 10 MB, process in chunks
   while($fileSize)  {

      if($fileSize >= 1024000)  {
         $size = 1024000;
      }else  {
         $size = $fileSize;
      }

      if(($size < 1024000) || (!$fBuffer)) {
         $buffer = "";
         $retval = _createBuffer(\$buffer, $size);
         if(!$retval)  {
            _logMsg("ERROR", "Could not create the buffer data.");
            _ostSDKError($OSTSDK_EOP);
         }

         $fBuffer = 1;        # flag is for efficiency.  Only create a 10MB buffer once, the reuse.
      }

      print FILE $buffer;
      $fileSize -= $size;     # set the fileSize remaining
   }

   close(FILE) or do {
      _logMsg("ERROR", "Could not close the file.");
      return 0;
   };

   return 1;

}


#----------------------------------------------
#                  _createBuffer
#----------------------------------------------
sub _createBuffer {
   my ($buffer, $bufferSize) = @_; 
   my @ascii_character_numbers;
   my $i;


   ## Any 0-255 number converted to character
   for ($i=0; $i < $bufferSize; $i++) {
      push(@ascii_character_numbers, (int (rand (256))) );
   }

   ## pack will convert @ascii_charater_numbers into string.
   $$buffer .= pack("C*", @ascii_character_numbers);

   ## \n is 2byte on Windows.  replace \n with 0
   if($^O =~ /Win/i)  {
       $$buffer  =~ s/\n/0/g;          # !!Change this to get a random character 
   }

   return 1;

}


#----------------------------------------------
#                  _ostSDKError
#----------------------------------------------
sub _ostSDKError {
   my ($err) = @_;

   print ("OST_EXIT_STATUS=$err\n");
   exit(0);
}

#----------------------------------------------
#                  _logMsg
#----------------------------------------------
sub _logMsg {
   my ($level, $msg) = @_;

   if($level !~ /^(INFO|WARNING|ERROR|DEBUG)$/)  {
      $level = "UNKNOWN";
   }

   if(($verbose < 4) && ($level eq "DEBUG"))  {
      return;
   }elsif(($verbose < 3) && ($level eq "INFO"))  {
      return;
   }elsif(($verbose < 2) && ($level eq "WARN"))  { 
      return;
   }elsif(($verbose < 1) && ($level eq "ERROR"))  {
      return;
   }elsif($verbose == 0)  {
      return;
   }

   print "${prog}::${level}::${msg}\n";
   return;
}


#----------------------------------------------
#                  _usage
#----------------------------------------------
sub _usage {
   print "Usage: $prog  [-verbose 1-3][-help]\n";
}



#----------------------------------------------
#                  _help
#----------------------------------------------
sub _help {
   _usage();
   print "\nThis program builds a dataset for test puproses.
The files are populated with random character data.

Options:
   -help                      print this help message and exit.
   -verbose <level>           The level of verbosity <1-3>. Default is 3.
   -path <path>               The directory to create the file(s) in.
   -count <n>                 The number of files to create.
   -size <n>                  The size of the file to create.
   -no_overwrite              Overwrite of existing files not allowed.
";

   exit 0;
}