
                       README 

     OpenStorage Plugin Pre-Qualification Test Tool

   NetBackup OpenStorage Software Development Kit 11.1.0



CONTENTS
---------------------------------------------------------------------
 - About the Plugin Pre-Qualification Test Tool
 - System Requirements
 - Installation and Setup
 - System Components
 - Plugin Interoperability Notes
 - Usage for stsconfig and stsmanager utilities
 - Usage for ststm Test Manager utilities
 - Helper Utilities
 - Uninstall
 - Known Issues
 - Troubleshooting
 - Recent Changes
 - Future Enhancements
 - Terms and Conditions
 - Symantec Contact Information


ABOUT THE PLUGIN PREQUALIFICATION TEST TOOL  
---------------------------------------------------------------------

The Plugin Pre-Qualification Test Tool is a utility for plugin
developers to use to test and validate their implementation of the an
OpenStorage plugin.  Note: Plugins should first be exercised against
the Plugin Tester (the other tool provided in the SDK) before being
run against the prequal tool.  It is important that your plugin passes
the individual API calls verification tests (and the simple API call
sequences ) in the Plugin Tester before moving to the next stage of
testing.  The prequal tool verifies plugins at a higher level,
exercising the plugin in the context of test cases such as backup,
restore, and duplication.

Our intent with the prequal tool is to provide plugin writers with a
highly automated, lightweight stand-in for NetBackup.  Essentially,
the prequal tool simulates NetBackup usage of your plugin without
requiring you to learn and understand the complexities of NetBackup
configuration, management and administration.  Wherever possible, the
Symantec SDK developers will try to hide configuration details of
backup methodologies by assuming set defaults.

Another goal of the prequal tool is to confirm that plugins are ready
for formal qualification.  You will be expected to submit your
successful output results to Symantec to demonstrate readiness for
official Symantec OpenStorage plugin qualification testing.


SYSTEM REQUIREMENTS
---------------------------------------------------------------------

The tool has been built for the platforms listed below.  All but
Windows are 32-bit version only.  For Windows we have packaged both
32-bit and 64-bit binaries.  Other platforms will be added in future
SDK releases, depending upon time and OpenStorage plugin developer
interest.

  Platform(s): RedHat Linux, SUSE Linux, Solaris, Windows 2003



INSTALLATION AND SETUP
---------------------------------------------------------------------

The Pre-Qualification test tool package must be installed on the
system where your plugin resides.  Note: If your test environment
intends to mimic a multi-system NetBackup environment (e.g., master
server and one or more media servers), then install the prequal
package on all systems.  At some point in your testing you will need
to run prequalification tests in a multi-system scenario in order to
prepare your plugin for the successful qualification with NetBackup.
NetBackup's qualification process includes test scenarios where
multiple NetBackup media servers (each with their own plugin library)
access the same storage server.

Step 1 - Uninstall NetBackup if it is pre-6.5

The prequal tool has been built to look for the STS core library in
the NBU directory space.  We have done this to enable running the
prequal tool against the core library packaged in the SDK or the core
library delivered in an official NBU 6.5 package.  If a pre-6.5
version of NBU resides on the test system, the prequal will use the
earlier version of the core library, which has not been built to the
latest API (v9).  We do not recommend unpacking the SDK's core library
to the directories of a pre-6.5 NBU install, overwriting the prior
libraries, as this breaks that NetBackup install.  The following files
will be overwritten:
1. libsts.so
2. libstsMT.so
3. libvxACE.so.3
4. libvxACEST.so.3
It is advisable to create backup copies of these files prior to
overwriting if you think you will need them later.

Step 2 - Unpack the prequal package

The prequal tool consists of a number of binaries that you install on
the system where your plugin resides. The binaries are bundled in
platform-specific tar files.  Copy the platform appropriate
prequal_*.tar file to the system and unpack.

    tar xvf prequal_*.tar

Several directories will be created -- prequal, ststm and usr (UNIX
only). Inside the prequal directory is a README (this file) with
instructions on usage.  Instructions can also be found in the Plugin
Writer's Guide which is included in the SDK's docs directory. Inside
the ststm will be a README with instructions on using the Test Manager
harness.


Step 3 - (UNIX only) Create the prequal database directory

The prequal tool must persist configuration and 'catalog' information
to remember between test executions.  Note: The prequal uses the
registry on Windows to install configuration and catalog information.

      mkdir -p /usr/openv/db/prequal


Step 4 - (UNIX only) Move the <prequal-install-dir>/usr/openv/ directory

This step is not needed if NBU 6.5 has been installed.

   cp <prequal-install-dir>/usr/openv /usr/


Step 5 - Copy your plugin libraries to the NetBackup plugin directory:
        On Unix the directory is /usr/openv/lib/ost-plugins and
        on Windows it is %PROGRAMFILES%\Veritas\NetBackup\bin\ost-plugins

Your plugin libraries must begin with the libstspi prefix and be built 
multithreaded.

On Unix you need to also build a single threaded version of the library.
To avoid the naming conflict the multithreaded version of the UNIX library
should be decorated with MT at the end.
For instance vendor ABC would have the following libraries:
        On Unix libstspiABCMT.s* (MultiThreaded) and libstspiABC.s*
        and on Windows libstspiABC.dll


You are ready to begin running the prequal tool. 



SYSTEM COMPONENTS
---------------------------------------------------------------------

The prequal tool consists of two main sets of components -- one set
serves as the stand-in for the NetBackup application (aka the
'Data Proction Application (DPA) Simulator') while another upper 
layer of components (aka the 'Test Manager') act as a test harness
to execute test cases consisting of sequences of operations performed
by the simulated backup application.  In addition, some helper 
utilities have been included.

DPA SIMULATOR 

Two command-line utilities, stsconfig and stsmanager, encapsulate the
behavior of a subset of NetBackup operations.  NetBackup's actual
command line utilities run in the dozens, each taking many arguments.
We have limited what is exposed in the prequal tool by eliminating
unnecessary complexities such as schedule and backup policy settings
and configuration entities for managing backup environments.  These
two utilities can be called directly in an incremental fashion to step
through operations.    

The stsconfig utility is responsible for creating and managing the
storage server configuration information.  Configuration settings
specified via stsconfig are stored in a binary file on UNIX
(/usr/openv/db/prequal/systems.conf) and in the registry on Windows
(HKLM\Software\Veritas\OpenStorage\Prequal\). The stsmanager utility
is responsible for moving data between primary source and the storage
server. It is a standalone program that is started upon request
(either by direct invocation or by the test harness) to perform
backup, restore, duplication, exiting upon completing the operation.
An explanation of how to use stsconfig and stsmanager is included in
the next section.

TEST MANAGER 

The test manager consists of several components that are implemented
to execute stsconfig and stsmanager calls for the purpose of
initiating multiple plugin instances running on the same system or
several different systems, all accessing the same storage server and
LSUs.  Calls are grouped into test cases, which are defined in
configuration files. In addition to configuration files, the test
manager relies upon two continuously running processes - the test
manager server and the test manager agent.  The server's main role is
to start and manage jobs, as specified via a test case configuration
file. It is responsible for initiating a job locally or on a remote
machine and creating the final execution report.  The agent is tasked
with waiting for requests from the test manager, executing the command
and reporting the results.  The output from initiation of test jobs is
returned in a execution report file.  The report is what you submit
to Symantec for demonstrating that your plugin is ready for formal
qualification.

HELPER UTILITIES

Helper utilities have been included in the goodies directory.  Please
refer to the Helper Utilities section in this README for more information.


PLUGIN INTEROPERABILITY NOTES
---------------------------------------------------------------------

OPERATIONAL VERSION

stsmanager and stsconfig version 11.n.n will only operate with 
OpenStorage v11 plugins.

To test OpenStorage v9 functionality, use a OST-SDK-9.n.n sdk package.


DPAID AND DPA SPECIFIC ISINFO_T

stsmanager and the stsconfig both use OSTSDK_PREQUAL as the dpaid.
stsmanager uses the dpa specific sts_isinfo_t from the stsnbu.h include
file.


USAGE FOR STSTM TEST MANAGER UTILITIES
---------------------------------------------------------------------

Using the Test Manager will require setting up an environment file,
starting the stsTMClient process on all media servers, running the
stsTMServer with testsuites, and examining the results in the test
execution reports. Refer to the prequal/stsTM/README for detailed
instructions.


USAGE FOR STSCONFIG AND STSMANAGER UTILITIES
---------------------------------------------------------------------

This section describes the usage of stsconfig and stsmanager.  Calling
either CLI with the -help option will also display and describe all
options.

First, a word on output results: By default, execution results are
sent to the utility's logfile and not to stdout.  To see results in
stdout, you must specify the -verbose option with a value of 1, 2 or
3.  Level 1 verbosity will send error messages to stdout, level 2 will
send error and warning messages, level 3 will send error, warning and
informational messages.  If verbosity is not specified, stsconfig
assumes a default value of 1 and stsmanager assumes a default value of
0 (no messages to stdout).  


DISPLAYING PLUGIN INFO

To list information regarding all plugins available, use the following
command:

   stsconfig -plugininfo [-stype <stype>]

If -stype is specified, only the plugin information for that plugin will
be displayed.


REGISTERING A STORAGE SERVER

The command below loads the plugin associated with the vendor prefix,
opens a connection to the server, queries the server for its
properties and saves the information in the prequal tool's
configuration database.  

  stsconfig -creatests -storage_server <name> -stype <vendor-prefix>


ADDING A CREDENTIAL

Credentials should be added after a storage server has been
registered. Should you make a mistake, credentials can be deleted with
the -removecred option.

  stsconfig -addcred -storage_server <name> -stype <vendor-prefix> 
            -user_id <id> -password <password>


RETRIEVING THE LIST OF LSUS

The command below loads the plugin associated with the vendor prefix,
opens a connection to the server, queries the server for its LSUs and
their properties and saves the information in the prequal tool's
configuration database.  The information is used when executing
stsmanager calls to perform backups.  Note: Invoking the CLI a second
time will not cause the database to be updated.  This is a known
limitation that will be addressed in a pending enhancement. 
 
  stsconfig -listlsu -storage_server <name> -stype <vendor-prefix>


LABELING AN LSU

Providing a lsu with a label is optional:

   stsconfig -labellsu -stype <stype> -storage_server <name>
             -lsuname <lsuname> -label <label>



PERFORMING A BACKUP

The command below creates a backup.  The stsmanager utility will choose
which LSU on which to store the image. If an argument is not specified,
a default value is assumed.

  stsmanager -backup -file <backupset> -stu <stu>[,<stu>]
   -sched <sched-type>
   [-numCopy <n>]
   [-log <logfilename>]
   [[-fragsize <fragmentsize>] | [-span <spanstu> [-forcespan]]]
   [-checkpoints <n> [-fragcount <n>] [-suspend <n:n>] [-exit <n:n>]]
   [-resume [-backupid <backupid>]]

  -stu indicates destination in the form of <stype>:<storage_server>:<lsu>
  -sched indicates schedule type; valid option values are FULL or INCR.  
         Default is FULL
  -numCopy indicates number of backup copies to make; 2 copies requires 
         2 stu values
  -file indicates the data set to back up.  The <backupset> can be the  
      	name of a file or a directory containing multiple files.  In the
      	case of a directory, only the top level directory will be backed
      	up.  stsmanager does not currently back up sub-directories.
  -fragsize indicates the size (in bytes) at which a backup set should
      	break down into separate STS images.  For example, if a backup 
   	set size is greater than the specified fragment size, but less 
   	than 2 times the fragment size, then the prequal tool will create
   	two STS images. 
  -span indicates the second lsu to write to should conditions require the
      	backup set to span multiple LSUs.  The prime example is if the LSU
      	full condition is hit.  The backup set will be split into two
   	STS images - one residing on the initial LSU, the other residing
   	on the span LSU.  
  -forcespan forces a span to occur even if conditions do not require it.
   	This option exists to facilitate testing.
  -checkpoints indicates the number of checkpoints to take during the backup
  -fragcount indicates the number of fragments between checkpoints to take
  -suspend the coordinates to suspend the backup.  A suspend is closes the 
      last image with a 0 flag.  A coordinate of n1:n2 will suspend the backup 
      at fragment n2 after checkpoint n1.
  -exit the coordinates to exit the backup.  An exit does not properly close
      the image handle.  A coordinate of n1:n2 will exit the backup 
      at fragment n2 after checkpoint n1.
  -resume resume a backup that was suspended or exited.  The resume will begin
      at the offset of the last checkpointed image.
  -backupid  the backupid to resume

Examples:
   stsmanager -backup -stu <stype:storage server:lsu> -file <path/to/file>
   stsmanager -backup -stu <stype:storage server:lsu> \
             -file </path/to/directory>
   stsmanager -backup -stu <stype:storage server:lsu> \
             -file </path/to/directory> -fragsize <fragmentSize>
   stsmanager -backup -stu <stype:storage server:lsu> \
             -file </path/to/file> -span <stype:storage server:lsu> \
             -forcespan
   stsmanager -backup -stu <stype:storage server:lsu> \
             -file </path/to/file> -checkpoints 4 fragcount 2 \
             -suspend 2:2
   stsmanager -backup -stu <stype:storage server:lsu> \
             -file </path/to/file> -checkpoints 4 fragcount 2 \
             -exit 2:2
   stsmanager -backup -stu <stype:storage server:lsu> \
             -file </path/to/file> -checkpoints 4 fragcount 2 \
             -resume -backupid <backupid>

Regarding the size of the backup creates by stsmanager: The backup
will append a 512byte header block to each file that contains file
metadata.  Additionally, stsmanager will write the backup out ending
at a 512byte boundary. A file 1000 bytes in size will be written out
to a 1536byte file.  (512byte header + 1000byte file size + 24byte
boundary padding) If it is a directory getting backed up, an
additional 512byte header with directory metadata will be appended to
the backup.

Windows backups:
   1.  The absolute path to the file is required.  Relative paths are 
       not supported.
   2.  If you have spaces in your path, you need to have your path in 
       double quotes if it contains space.  

Example:
stsmanager -backup -stu <stype:storage server:lsu> -file \
"C:\Program Files\test.txt"



LISTING IMAGES ON AN LSU

The command below queries the specified LSU for the list of images
residing upon it.  

  stsmanager -listimages -stu <stu> [-image_type <image_type>]

  -stu indicates destination in the form of <stype>:<storage_server>:<lsu>
  -image_type is for listing only FULL or INCR images.  If not specified, 
     both types are listed.


PERFORMING A RESTORE

The command below results in the specified backup image being
restored.  Backup IDs can be found in the output from the "stsmanager
-query_catalog". The -file option specifies the destination.  For
Windows restores, the absolute path to the restore location is
required and double quotes required if there are spaces in the path.

The optional -copy param will direct the restore to restore from
a duplication.  Otherwise, the restore is derived from the primary
copy.

  stsmanager -restore -backupid <name> -file <whereigo> [-copy <n>]


PERFORMING AN OPTIMIZED DUPLICATION

The command below results in the specified backup image being
duplicated.  Backup IDs can be found in the output from the "stsmanager
-query_catalog". The -target option specifies the destination lsu.  An
optional -span parameter can be provided to allow the duplication
to span to another lsu if the target lsu is full.

If the storage server does not support optimized duplication,
stsmanager will error.

  stsmanager -opdup -backupid <name> -target <target stu>
         [-span <span stu>]

PERFORMING A NAMED ASYNCHRONOUS OPTIMIZED DUPLICATION

The command below results in the specified backup image being
duplicated in named asynchronous way. The arguments "event-mode" is 
saying the way to wait for the copy finish: push event; pull event;
call sts_named_async_status to get the status; call sts_named_async_wait 
to wait in block mode until the job finishes. The other arguments are 
the same with those in "OPTIMIZED DUPLICATION".

If the storage server does not support optimized duplication,
stsmanager will error.

  stsmanager -named_async_opdup 
         -event-mode [push|pull|get-status|async-wait]
		 -backupid <name> -target <target stu>
         [-span <span stu>]

PERFORMING AN OPTIMIZED SYNTHETIC

The command below results a new image which is a syntetic of the source images.
The -target option specifies the destination lsu. The -source option specifies
the source images you want to include in the new synthetic image. You can include
a whole image or just some files in the image. If you didn't specify any files, the
whole image will be included. The newly created synthetic image can be later used
for restore. Due to the inherited limitation of this tool, the source images must
contain the same directory, that is the source images are backup of the same directory.
Each can contain different files.
    
  stsmanager -synthetic -target <stype:server_name:lsu_name>
         -source <backupID1:file1:file2:...:fileN,backupID2:file1:file2:...fileN>

DELETING AN IMAGE 

The command below looks up the location of the image in the prequal's
database, and contacts the storage server to remove it from the LSU on
which it resides. Image names can be found in the output from
"stsmanager -query_catalog".  

  stsmanager -deleteimage -image <image_name>


QUERYING THE DATABASE

To examine the information stored in the simulator's database, you
call the following commands:

  stsconfig -queryserver
  stsconfig -querylsu -storage_server <server> -stype <type>
  stsmanager -query_catalog 

  -queryserver will return the storage servers stored in the database.
  -querylsu will return the LSUs stored in the database.
  -query_catalog will return the backup IDs listed in the catalog.


CLEARING THE DATABASE

You may find it useful to purge the information stored in the
prequal's database and starting from a clean slate.  The command below
deletes the entire contents of the prequal's database.

   stsconfig -cleardb      (clears the configuration database)
   stsmanager -cleardb     (clears the backup catalog database)

The command below deletes the record of the indicated storage server.

   stsconfig -deletests -storage_server <storage_server> -stype <stype>


CLEARING THE LOGS

You may find it useful to clear the logs when troubleshooting.

   stsconfig -clearlog      (clears stsconfig's log)
   stsmanager -clearlog     (clears stsmanager's log)


DISPLAYING VERSION INFORMATION

To get the version of the prequal tool.

   stsconfig -version
   stsmanager -version


HELPER UTILITIES
---------------------------------------------------------------------

Some scripts have been included to aid plugin testing efforts.  These
scripts are located in prequal/goodies.

GATHERING LOG FILES

stsSupportUtil.pl will gather stsconfig/stsmanager logs and stsTM logs 
and reports, and capture basic system information.  It will  collate 
these files in a single directory, which can then be archived or provided
to the OST SDK support team for analysis.

Example:

   stsSupportUtil.pl -bin ../bin -tmlog ../stsTM/logs/1112243  \
         -copyto /tmp/data

For more information on the cmd line parameters, use:

   stsSupportUtil.pl -help

stsSupportUtil.pl requires Perl.

CREATING A DATASET

stsDatasetUtil.pl will generate character files with random data.
These files can be used with stsmanager.

Example:

   stsDatasetUtil.pl -path /tmp/testdir -count 100 -size 1024

This will create 100 files each 1024 bytes in size in directory
/tmp/testdir. For more information on the cmd line parameters, use:

   stsDatasetUtil.pl -help

stsDatasetUtil.pl requires Perl.


UNINSTALL
---------------------------------------------------------------------
To uninstall the Prequal tool:

If Netbackup is installed:
If you created backup copies of:
1. libsts.so
2. libstsMT.so
3. libvxACE.so.3
4. libvxACEST.so.3
You should copy them back into (unix) /usr/openv/lib
1. Remove the directory usr/openv/db/prequel
2. Remove your plugin libraries from /usr/openv/lib/ost-plugins
3. Remove log files from /usr/openv/netbackup/logs beginning with
   stsconfig* and stsmanager*
4. Remove the directory where you have extracted the Prequal directory
   [SDK directory]/OST-SDK-9.4.3/tools/prequel

If Netbackup is not installed:

Unix
1. If you know you do not need any of the files contained under /usr/openv,
   you can remove the directory /usr/openv.  Alternatively, you can remove 
   the subdirectories individually as follows.
2. Remove the directory usr/openv/db
3. Remove the directory usr/openv/lib/ost-plugins
4. Remove the directory usr/openv/netbackup
5. Remove the directory where you have extracted the Prequal directory 
   [SDK directory]/(OST-SDK-11.1.0)/tools/prequel



KNOWN ISSUES
---------------------------------------------------------------------

1. Please be aware that the prequal's backup and restore capabilities 
   are primitive when compared to NetBackup.  The prequal's directory 
   backup is merely a means of specifying a set of files in the 
   directory.  The prequal will not back up the directory node, soft
   linked files or the contents of directories nested underneath.  
   File level permissions are not saved and restored.  

2. Use a sufficiently large backup set when calling stsmanager -forcespan.
   The induced spanning occurs after a successful write of storage
   block size * 128.

3. On Windows, both programs are built multi-threaded and dynamically
   linked with the runtime and C++ standard library (MSVCR71.dll
   and MSVCP71.dll). Therefore if these libraries are not installed
   on your test system, you may need to download them from Microsoft.

4.  The number of fragments that can be stored in the prequal tool's 
    catalog is limited.  The limit is somewhere around 1000 fragments.
    Backups may fail if the catalog is over populated.  If this occurs, 
    either clear the database or delete images from the database.


TROUBLESHOOTING
---------------------------------------------------------------------

ISSUE: "CatalogManager::queryBackupRecord failed to open the
backupSection for .." is seen in output for stsmanager -restore call.

EXPLANATION: The catalog has no record of the backup record.

RECOMMENDED ACTION: Verify that the backup ID specified on the CLI is
correct.  The stsmanager -query_catalog will print legitimate backups,
listed by their ID.


RECENT CHANGES
---------------------------------------------------------------------
Changes in 11.1.0:
1. none. 

Changes in 11.0.0:
1. Support for OST v11 added.
2. Support for the new sts_named_async_copy_image has been added.  
3. Open event channel and add one event source have been added. 
4. Support for waiting the copy finish has been added. And it is 
   implemented in four ways: push event; pull  event; call 
   sts_named_async_status to get the status; and call 
   sts_named_async_wait to wait in block mode.  
5. Testing support for named asynchronous copy image has been added.

Changes in 10.0.0:
1. Support for OST v10 added.  OST v9 support has been deprecated.
2. Support for the new sts_image_def_v10_t has been added.  stsmanager
   populates the Image Set ID fields using the NetBackup specific
   stsnbu_isinfo_v65_t.
3. The prequal tool provided the DPAID OSTSDK_PREQUAL
4. Testing support for Optimized Synthetics has been added.
5. Support for HP platforms to be added in later release.

Changes in 9.4.3:
1.  Support added for HP-UX PA-RISC, HP-UX IA64, and AIX

Changes in 9.4.2:
1.  Optimized Duplication functionality was added to stsmanager.
2.  stsmanager -restore now takes a -copy param so you can restore
    from duplications.  If the copy param is not provided, the restore
    will happen from the primary copy.
3.  The stsmanager catalog now keeps track of the primary copy and the
    total copies.

Changes in 9.4.1:
1.  stsmanager now sets all buflen and offsets of sts_read_image
    and sts_write_image to be multiples of STS_BLOCK_SIZE.
2.  max_transfer_size of the storage server is now honored on 
    restores.
3.  stsmanager no longer restricts -stu length to 32 characters
    per lsu.
4.  The process id of the stsmanager instance is now added to the
    backupid to ensure greater image name uniqueness.
5.  The credential string now adheres to the API and uses '/0' as 
    the delineator, between userid and password, instead of ':'.

Changes in 9.4.0:
1.  New stsmanager options for testing checkpoint backup restart
    (-checkpoints, -suspend, -exit, -resume, -fragcount).
2.  Added additional testsuites for running checkpoint backup restart
    use cases; duplication.
3.  Renamed stsmanager -fragment option to -fragsize.
4.  Changed the TestManager harness to get success/failure from exit
    codes instead of logged output from the CLI.
5.  Renamed stsmanager -forceSpan option to -forcespan.
6.  Added -plugininfo command to stsconfig.
7.  Added -clearlog command to stsconfig
8.  Added -cleardb command to stsmanager
9.  Added -clearlog command to stsmanager
10. stsconfig can now store LSU names which contain path delineators.
11. Added -labellsu command to stsconfig.
12. Added version command to stsconfig and stsmanager.

Changes in 9.3.1:
1. The Pre-Qualification test tool has been significantly expanded to
   include a test harness for executing an automated sequence of tests
   that produce a submittable report to the Symantec qualification team.  
2. New -span <stu> option to the stsmanager command-line utility
3. New -fragment <size> option to the stsmanager CLI
4. The stsmanager -backup can back up multiple files, by using
   -file <name> with a directory name instead of a filename.
5. New -deletests option to stsconfig CLI for deleting a storage 
   server from the prequal database.
6. Bug fix to stsmanager to enable refreshing the configuration
   database with additional calls to "stsmanager -listlsu".


FUTURE ENHANCEMENTS
---------------------------------------------------------------------

The prequal tool in the current package is not intended to be the
final version.  The next several SDK releases will contain
enhancements to the prequal tool.  These will include support for 
more complex backup data sets (e.g.,precanned data) and backup 
strategies (e.g., true image restore).


TERMS AND CONDITIONS
---------------------------------------------------------------------

SDK materials are made available under non-disclosure agreement only.
Material provided within the SDK is governed by the OpenStorage API
(also referred to as the STS API) NDA currently in place between the
partner and Symantec.  By downloading the SDK, you acknowledge this
agreement either for yourself or on behalf of your employer and agree
to be bound by its terms and conditions.


SYMANTEC CONTACT INFORMATION
---------------------------------------------------------------------

For questions specific to Pre-Qualification Test Tool functionality,
or comments on how to improve the tool, send email to:

    <EMAIL>.

Your feedback will be used when planning future enhancements.

