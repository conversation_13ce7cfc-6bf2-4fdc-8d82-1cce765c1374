<?xml version="1.0" encoding="ISO-8859-1"?>
<!--
  Copyright 2004 The Apache Software Foundation

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<web-app xmlns="http://java.sun.com/xml/ns/j2ee"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd"
    version="2.4">

  <display-name>Welcome to Tomcat</display-name>
  <description>
     Welcome to Tomcat
  </description>

	<!-- PluginDriver's TestEngine -->
    <servlet>
         <servlet-name>com.symantec.server.TestEngineImpl</servlet-name>
         <servlet-class>com.symantec.server.TestEngineImpl</servlet-class>
   </servlet>
   <servlet-mapping>
         <servlet-name>com.symantec.server.TestEngineImpl</servlet-name>
         <url-pattern>/TestEngineImpl</url-pattern>
   </servlet-mapping>

	<!-- End - PluginDriver's TestEngine -->
<!-- JSPC servlet mappings end -->

</web-app>
