
                       README 

         OpenStorage Plugin Tester (pgntester)

   NetBackup OpenStorage Software Development Kit 11.1.0



CONTENTS
--------------------------------------------------------------------------------
 - About the Plugin Tester
 - System Requirements
 - Installation
 - Uninstall
 - Plugin Interoperability Notes
 - Usage - Web Interface
 - Usage - Command Line Utility
 - Troubleshooting
 - Known Issues
 - Terms and Conditions
 - Recent Changes
 - Licenses
 - Symantec Contacts


ABOUT THE PLUGIN TESTER
--------------------------------------------------------------------------------

The Plugin Tester is a tool for first level testing of your plugin.

The tool can be used as a web application that is accessed with a web
browser or via the command line.

When used as a web application, once you install and run the Plugin Tester
on the test machine, you can initiate the tests from a web browser on any
host that has network connectivity with the test machine.


SYSTEM REQUIREMENTS
--------------------------------------------------------------------------------

The Plugin Tester must be installed on the machine where your plugin
resides and will be tested.  The Plugin Tester has been built for the
platforms listed below. The browser can be run from any system.  We
have verified the Plugin Tester against the browsers listed below.

  Platform(s): RedHat Linux, SUSE Linux, Solaris, Windows 2005,
               HP-UX, AIX
  Browser(s): Internet Explorer 6.0+, Mozilla 1.5+


INSTALLATION
--------------------------------------------------------------------------------

Note: If you have a previous version of the Plugin tester installed,
you should make sure that the previously running process has been
terminated (see the troubleshooting section).  The Plugin Tester needs
to be installed on the system where your plugin resides.

Copy the pgntester.tar file to the system and unpack.

   tar xvf pgntester.tar

A pgntester directory will be created.  Inside the directory is a
README (this file) with instructions on usage.


Web Interface Prerequisites:

The Plugin Tester Web Interface requires the Java Runtime Environment.
To complete the installation, you must have the SUN JRE v1.5.0 (or later)
installed.  If your system does not have the JRE installed, go to:

   http://java.com/en/download/manual.jsp 

and download the JRE for your platform.  Make sure your PATH variable
includes <JRE-installation>/bin.

For Unix systems:

To determine what version of Java your system is referencing, run:

   java -version 

If it does not return a SUN version of java 1.5.0 or later, then you
will not be able to successfully operate the pgntester.


Command Line Utility Prerequisites:

The Plugin Tester Command Line Utility requires Perl 5.6.1 or newer.
If your system does not have Perl installed, go to:

   http://www.activestate.com/downloads/

and download the latest version of ActivePerl.

Or, you can get the Perl source from:

   http://www.cpan.org/


UNINSTALL
--------------------------------------------------------------------------------
Note: different versions of the plugin tester can coexist on the
same system.  The most important consideration is to ensure that the
currently running java process represents the version you have
intended to run.

Following this procedure is optional, and may be used to clean up 
your system by removing unnecessary files.

To uninstall the Plugin tester, first ensure any running processes are
terminated.  You can manually terminate this process as follows:

   ps -ef |grep java  (Unix)

You should see an output similar to this (the PID will be different)
Root 3545 1 0 16:56 pts/2 00:00:00 java -cp .:./lib...

In the list of paths and assemblies, you should see:
.bin com.symantec.emtomcat.EmbeddedTomcat 60000

If you see this assembly under the PID, then you can issue a kill command: 
kill 3545

Remove the following files or directories:
1. [pgntester sdk directory] (example: /opt/STSSDK/OST-11.x.x/tools/pgntester)
2. [LSU directories] (example: /tmp/vol1, /tmp/vol2, /tmp/vol3)
3. [plugin files] 
   (example:
   /usr/openv/lib/ost-plugins/libstspisampledisk.so
   /usr/openv/lib/ost-plugins/libstspisamplediskMT.so)
4. /root/config.txt


PLUGIN INTEROPERABILITY NOTES
---------------------------------------------------------------------

OPERATIONAL VERSION

pgntester version 11.n.n will only operate with OpenStorage v11 plugins.
To test OpenStorage v10 functionality, use a OST-SDK-10.n.n sdk package.


DPAID AND DPA SPECIFIC ISINFO_T

The pgntester uses OSTSDK_PGNTESTER as the dpaid.

pgntester does not populate any fields in the sts_isid_t structure when
creating images other than is_dpaid.  There is no dpa specific sts_isinfo_t. 


USAGE - Web Interface
--------------------------------------------------------------------------------

The Plugin Tester's backend server is started from the command line using:

        $ pgntester [port]

        Example:
        $ ./pgntester 60000

Open a web browser from any system (e.g. your desktop) and enter the URL:

    http://hostname:port/com.symantec.PgnDriver/
        
where 'hostname' is the system running pgntester and 'port' is the
number you provided on the CLI when you started pgntester.  Note: If
you did not provide a port number, pgntester assumes the default port
of 8080.  For example, if your system name is dev-laptop-jsmithxp and
you configured the port to 60000 as indicated above, then you would
type this in the web browser address bar:

    http://dev-laptop-jsmithxp:60000/com.symantec.PgnDriver

In the form that appears, enter values into the text input fields at
the top of the page.  The 'Path to the library' is the location of
your plug-in library as visible from the machine on which pgntester is
running. The LSUs field should be a comma-separated list of all LSUs
that the storage server will report back through the relevant get LSU
API calls.  Failure to list all LSUs will lead the pgntester to report
an error upon execution of those APIs.

OpenStorage Vendor Example:

Suppose your vendor name is "Trident" and that you are using the
string "Trident" as the storage server prefix for all of your storage
servers.  This means that your plugin will claim the "Trident" prefix
and should be the value entered in the STS Prefix field.  Suppose
also that you have two devices against which you are testing the
plugin, which you have assigned storage server names of "tx2000a" and
"tx2000b".  To test the plugin against the first device, you would
enter "tx2000a" in the Storage Server Name field.  Additionally, enter
in a comma separated list of all the LSUs that are available on the 
Storage Server into the LSUs field.  Assume "lsu1" and "lsu2" are
located on "tx2000a". 

    Path to library = /usr/openv/lib/ost-plugins/libstspitrident.so
         STS prefix = Trident
Storage Server Name = tx2000a
               LSUs = lsu1,lsu2


Sampledisk Example:

The information you enter into the input fields must come from the
config.txt.  Assuming the configuration file includes the lines below:

   STORAGE_SERVER=leafie
   VOLUMES=lsu1,lsu2,lsu3

...you would enter the following values in the input fields on the
Plugin Tester page:

    Path to library = /usr/openv/lib/ost-plugins/libstspisampledisk.so
         STS prefix = sampledisk
Storage Server Name = leafie
               LSUs = lsu1,lsu2,lsu3

(Note: When you load your browser for the first time, you may see
configuration values already filled in.  This is an indication that
the java processes for the Plugin Tester were previously running. If
you have recently updated to a newer version of the Plugin Tester, you
should terminate the previously running process and re-launch the new
version of the Plugin Tester (see the troubleshooting section.)

Saving the configuration (the "Save Configuration" button) will cause
the entered values to be remembered between tests.  If you close the
browser and open it later, the Plugin Tester will have the input field
values pre-filled.

Selecting the 'RUN All Tests' button will cause the entire set of tests to
be run.  Alternatively you can selectively choose individual 'RUN'
buttons to test a specific case.  

Next to the 'RUN All Tests ' button is a checkbox ('Generate Report')
for saving the results of the full test run to a log file.  The
results are placed in an NBUReport*.txt file in the directory where
the pgndriver executable resides. 


USAGE - Command Line Utility
--------------------------------------------------------------------------------

The Plugin Tester can be run from the command line as a Perl script.  Unlike
the Web Interface, the CLI utility needs to be executed on the test host.

        Example:
        $ ./pgntestercli.pl -h

        Usage: pgntestercli.pl [-l /path/to/libstspi<plugin>.so][-f claim]
                [-sts sts prefix][-storage_server storage_server name]
                [-lsu lsu name][-help][-print]

        This program will execute pgntester tesetcases

        Options:
           -help                      print this help message and exit.
           -print                     Print all the testcases and exit.
           -printplat                 Print all the platforms that the
                                         -plat parameter accepts.
           -printapi                  Print all the APIs that the
                                         -f parameter accepts.
           -verbose <level>           The level of verbosity <1-3>. Default is 1.
           -l <path>                  The path to the plugin.  Required.
           -f <api>                   The API to test.  Default is all.
           -sts <sts>                 The OpenStorage Plugin prefix. Required.
           -storage_server  <server>  The OpenStorage storage server.  Required.
           -lsu <lsu>                 The OpenStorage LSU.  Required.
           -plat <platform>           The host platform. Required.

The -l parameter needs to be the full path to the OpenStorage Plug-in, including
the file name of the plugin.

To get a list of all the platforms supported, run:

        $ ./pgntestercli.pl -printplat

        Platforms Supported
        --------------------------------
        Solaris sparc64 = solaris_SPARC64
        Solaris = solaris_x64
        RedHat = linuxR_x64
        SuSE = linuxS_x64
        Win32 = x86
        WinX64 = AMD64
        WinIA64 = IA64
        HP-UX PA-RISC = hp_ux64
        HP-UX IA64 = hpia64
        AIX = rs6000


Sampledisk Example:

To run all the tests against the sampledisk plugin located in 
/usr/openv/lib/ost-plugins on a RedHat linux host, run:

        $./pgntestercli.pl -l /usr/openv/lib/ost-plugins/libstspisampledisk.so
             -sts sampledisk -storage_server leafie -lsu vol1 -plat linuxR_x64

To run just the sts_claim tests, run:

        $./pgntestercli.pl -l /usr/openv/lib/ost-plugins/libstspisampledisk.so
             -sts sampledisk -storage_server leafie -lsu vol1 -plat linuxR_x64
             -f claim

All the test output list logged to the ../pgntester/logs directory.  If you want
verbose output, run with the '-verbose 3' parameter.


TROUBLESHOOTING
--------------------------------------------------------------------------------

ISSUE: When I enter the URL to the Plugin Tester utility, my browser
displays a 'page not found' error message.

EXPLANATION: The URL is incorrect or the web application is not
running on the host system.

RECOMMENDED ACTION: Confirm that you have entered the correct URL,
that the hostname and port number are correct and that you have no
typos.  If the URL is correct, the problem is likely with the web
application not running.  Before going to the host system, try a
simple test -- truncate and modify the URL to be just the
"http://hostname:port/foo".  If the web application is running, you
will get a different type of message, coming from the web application
that the "foo" resource is not found.  This outcome isolates the
problem to a typo in the latter part of the URL.  However, if you get
the same message as before, then the web application is not running.
Go to the system and restart the pgntester from the command line.  You
might want to look at the logfile to see if any problems occurred on
start-up.

-----

ISSUE: I see "java: command not found" in the log file.

EXPLANATION: The JRE package has not been installed or your PATH does
not have <JRE-installation>/bin.

RECOMMENDED ACTION: Verify that you installed the required JRE v1.5
(or later) and modified the PATH variable to include
<JRE-installation>/bin.

-----

ISSUE: I see "Exception in..." stack traces in the log file and the
line "Caused by: java.lang.ClassNotFoundException".

EXPLANATION: An old version of the JRE is being used.

RECOMMENDED ACTION: Verify that you installed the required JRE v1.5
(or later).  You can either change the PATH variable to place the
<JRE-installation>/bin first so that it is the first java binary
found, or you can edit the pgntester script to explicitly specify the
full path to the java 1.5 (or later) binary.

-----

ISSUE: When I run pgntester it fails to start.  The log file includes
a message "java.netBindException: Address already in use:PORT"

EXPLANATION: You already have another pgntester instance or another
server bound to PORT.

RECOMMENDED ACTION: Kill the process that is using the PORT or use
another port number.
(Unix)

ps -ef |grep java

You should see an output similar to this (the PID will be different)
Root 3545 1 0 16:56 pts/2 00:00:00 java -cp .:./lib

In the list of paths and assemblies, you should see:
.bin com.symantec.emtomcat.EmbeddedTomcat 60000

If you see this assembly under the PID, then you can issue a kill command: 
kill 3545


-----

ISSUE: I started the pgntester and typed the correct URL in the
browser, but I get "HTTP Status 503 - This application is not
currently available" response.

EXPLANATION: The pgntester's Tomcat servlet engine takes time to load
the servlet contexts.

RECOMMENDED ACTION: Wait a little while and try again.  Look at the
log file and watch for the message "Ready to accept connection!".
When you see that message appear it means that Tomcat is ready.

-----

ISSUE: When testing the plugin on Windows I see a "load library"
failure or a library not found message pop-up.

EXPLANATION: Your plugin library requires a second library that cannot
be found by the native pgndriver binary.

RECOMMENDED ACTION: Copy the secondary library to the directory where
the pgndriver binary resides, e.g.,
<pgntester-install>\webapps\ROOT\bin\x86\


KNOWN ISSUES
--------------------------------------------------------------------------------
ISSUE:  Clicking buttons additional times my result in duplicate entries
Clicking the 'RUN All Tests' button after individual 'RUN' buttons will
cause duplicate entries to appear in the results table beneath the
invoked tests.  If you have executed individual tests, do a page
refresh before clicking the 'RUN All Tests' button to clear the prior
results.

-----

ISSUE:  sts_claim test fails when claiming sampledisk stype

EXPLANATION: There is a negative test that sends the 'sampledisk' stype
to the plug-in with the expectation that the plug-in should not claim the
storage server and return an error.  This is a test to make sure that
vendor plug-ins do not try to claim that reserved stype.  However, for the
sampledisk, the claim will succeed.  But, since it is a negative test,
the test will fail.

-----

ISSUE:  Composite Test : Listing LSUs fails

EXPLANATION:  For this test, the LSUs (VOLUMES)in the config.txt must 
match the LSUs defined in the LSUs browser field

Examples of when the test will fail:
--
Path to Library = /usr/openv/lib/ost-plugins/libstspisampledisk.so
STS prefix = sampledisk
Storage Server Name = server_name
LSUs = vol1 

config.txt
STORAGE_SERVER=server_name
VOLUMES=vol1,vol2,vol3
--
Path to Library = /usr/openv/lib/ost-plugins/libstspisampledisk.so
STS prefix = sampledisk
Storage Server Name = server_name
LSUs = vol1,vol2,vol3,vol4

config.txt
STORAGE_SERVER=server_name
VOLUMES=vol1,vol2,vol3

Example of when the test will pass:
--
Path to Library = /usr/openv/lib/ost-plugins/libstspisampledisk.so
STS prefix = sampledisk
Storage Server Name = server_name
LSUs = vol1,vol2,vol3
 
config.txt
STORAGE_SERVER=server_name
VOLUMES=vol1,vol2,vol3


TERMS AND CONDITIONS
--------------------------------------------------------------------------------

SDK materials are made available under non-disclosure agreement only.
Material provided within the SDK is governed by the OpenStorage API
(also referred to as the STS API) NDA currently in place between the
partner and Symantec.  By downloading the SDK, you acknowledge this
agreement either for yourself or on behalf of your employer and agree
to be bound by its terms and conditions.

Additional software distributed with the SDK is provided under other
licenses, separate from these terms and conditions.

Package: Apache Tomcat
License: Apache License v2.0, reproduced in Apache_License_v2.0.txt


RECENT CHANGES
---------------------------------------------------------------------
Changes in 11.0.0:
1.  Support for OST v11 added.  OST v10 support has been deprecated.

Changes in 10.0.0:
1.  Support for OST v10 added.  OST v9 support has been deprecated.
2.  Support for HP platforms to follow in later release.

Changes in 9.4.3:
1.  Support added for HP-UX PA-RISC, HP-UX IA64, and AIX
2.  Command Line Utility (pgntestercli.pl) added. 

Changes in 9.4.2:
1. Added additional tests: to verify that the plugin returns the right
   code when a server name is not valid is not found; to verify that the
   plugin does not claim a pre-existing well known prefix; to verify that
   the plugin does not claim a wrong prefix; to verify that the plugin
   returns the right error code when looking for an object that does not
   exist.

Changes in 9.4.1:
1.  Added tests for stspi_get_image_prop
2.  Added tests for stspi_get_image_prop_byname
3.  Added tests for stspi_list_image
4.  Added tests for stspi_open_image_list
5.  Added tests for stspi_close_image_list
6.  Added tests for stspi_read_image_meta
7.  Added tests for stspi_write_image_meta
8.  Fixed issue where a comma was required if setting a single lsu in
    the test configuration
9.  Fixed issue where create_image would sometimes fail because image
    name already exists on the lsu.  Made image name more unique.
10. Fixed issue where stspi_read_image and stspi_write_image were not
    sending buflen/offset as multiple of STS_BLOCK_SIZE
11. Added tests to sts_write_image to check if plugin errors if
    buflen/offset not multiple of STS_BLOCK_SIZE
12. Added tests to sts_read_image to check if plugin errors if
    buflen/offset not multiple of STS_BLOCK_SIZE
13. Added call to get_image_prop in the Composite Test: Image APIs
14. Fixed a buffer issue that caused pgndriver to deadlock.  This error
    exhibited itself most prominently on Windows.

SYMANTEC CONTACTS
--------------------------------------------------------------------------------

For comments or questions on the Plugin Tester, send email to:

    <EMAIL>

Your feedback will be used when planning future enhancements.

