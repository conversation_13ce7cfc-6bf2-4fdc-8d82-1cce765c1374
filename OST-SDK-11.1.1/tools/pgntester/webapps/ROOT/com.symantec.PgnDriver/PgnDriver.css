body {
  background-color: white;
  color: black;
  font-family: Arial, sans-serif;
  font-size: medium;
  margin: 20px 20px 20px 20px;
}

code {
  font-size: small;
}

a {
  color: darkblue;
}

a:visited {
  color: darkblue;
}

.gwt-BorderedPanel {
}

.gwt-Button {
}

.gwt-Canvas {
}

.gwt-CheckBox {
  font-size: smaller;
}

.gwt-DialogBox {
  border: 2px outset;
  background-color: white;
}

.gwt-DialogBox .Caption {
  background-color: #C3D9FF;
  padding: 3px;
  margin: 2px;
  font-weight: bold;
  cursor: default;
}

.gwt-FileUpload {
}

.gwt-Frame {
}

.gwt-HorizontalSplitter .Bar {
  width: 8px;
  background-color: #C3D9FF;
}

.gwt-VerticalSplitter .Bar {
  height: 8px;
  background-color: #C3D9FF;
}

.gwt-HTML {
  font-size: large;
}

.gwt-Hyperlink {
}

.gwt-Image {
}

.gwt-Label {
  font-size: smaller;
}

.gwt-ListBox {
}

.gwt-MenuBar {
  background-color: #C3D9FF;
  border: 1px solid #87B3FF;
  cursor: default;
}

.gwt-MenuBar .gwt-MenuItem {
  padding: 1px 4px 1px 4px;
  font-size: smaller;
  cursor: default;
}

.gwt-MenuBar .gwt-MenuItem-selected {
  background-color: #E8EEF7;
}

.gwt-PasswordTextBox {
}

.gwt-RadioButton {
  font-size: smaller;
}

.gwt-TabPanel {
  background-color: #FFFFEF;
}

.gwt-TabPanelBottom {
}

.gwt-TabBar {
  background-color: #FFFF00;
  font-family: courier;
  font-size: medium;
}

.gwt-TabBar .gwt-TabBarFirst {
  height: 100%;
}

.gwt-TabBar .gwt-TabBarRest {
  padding-right: 3px;
}

.gwt-TabBar .gwt-TabBarItem {
  padding: 2px;
  cursor: pointer;
}

.gwt-TabBar .gwt-TabBarItem-selected {
  font-weight: bold;
  background-color: #FFFF00;
  border-top: 1px solid #666666;
  border-left: 1px solid #666666;
  border-right: 1px solid #000000;
  padding: 4px;
  cursor: default;
}

.gwt-TextArea {
}

.gwt-TextBox {
}

.gwt-Tree {
}

.gwt-Tree .gwt-TreeItem {
  font-size: smaller;
}

.gwt-Tree .gwt-TreeItem-selected {
  background-color: #C3D9FF;
}

.gwt-StackPanel {
}

.gwt-StackPanel .gwt-StackPanelItem {
  background-color: #C3D9FF;
  cursor: pointer;
}

.gwt-StackPanel .gwt-StackPanelItem-selected {
}

/* -------------------------------------------------------------------------- */
.ks-Bar {
  font-family: courier;
  font-weight: bold;
  font-size: medium;
}

.ks-Row {
  font-family: 'trebuchet ms',helvetica,sans-serif;
  font-size: smaller;
}

.ks-ResultRow {
  font-family: 'trebuchet ms',helvetica,sans-serif;
  font-size: smaller;
  background-color: #99FFCC;
}

.ks-FailedResultRow {
  font-family: 'trebuchet ms',helvetica,sans-serif;
  font-size: smaller;
  background-color: #FF0033;
}

.ks-Cell {
  font-size: large;
  cursor: pointer;
}

.ks-Info {
  background-color: #C3D9FF;
  padding: 10px 10px 2px 10px;
  font-size: smaller;
}

.ks-List {
  margin-top: 8px;
  margin-bottom: 8px;
  font-size: smaller;
}

.ks-List .ks-SinkItem {
  width: 100%;
  padding: 0.3em;
  padding-right: 16px;
  cursor: pointer;
}

.ks-List .ks-SinkItem-selected {
  background-color: #C3D9FF;
}

.ks-images-Image {
  margin: 8px;
}

.ks-images-Button {
  margin: 8px;
  cursor: pointer;
}

.ks-layouts {
  margin: 8px;
}

.ks-layouts-Label {
  background-color: #C3D9FF;
  font-weight: bold;
  margin-top: 1em;
  padding: 2px 0px 2px 0px;
  width: 100%;
}

.ks-layouts-Scroller {
  height: 128px;
  border: 2px solid #C3D9FF;
  padding: 8px;
  margin: 8px;
}

.ks-popups-Popup {
  background-color: white;
  border: 1px solid #87B3FF;
  padding: 4px;
}

.infoProse {
  margin: 8px;
}

