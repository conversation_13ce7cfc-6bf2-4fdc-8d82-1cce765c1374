<?xml version="1.0" encoding="UTF-8"?>
<cache-entry>
	<generated-type-hash class="com.symantec.client.TestEngine_Proxy" hash="52337BBB0FB0635A84BFA6B559C490C3"/>
	<generated-type-hash class="com.symantec.client.ScenarioInfo_FieldSerializer" hash="E7CB202DCA5BC90E455B7F54AA8109B5"/>
	<generated-type-hash class="com.symantec.client.TestEngine_TypeSerializer" hash="3C9A668C3013B8F45990A41832F27F79"/>
	<generated-type-hash class="com.google.gwt.user.client.rpc.SerializableException_FieldSerializer" hash="1CB9B63ABEC9BE7E663F6C56AA395208"/>
	<generated-type-hash class="com.symantec.client.TestResult_FieldSerializer" hash="F2CAE3145B858CFD349F82E2BA676E77"/>
	<rebind-decision in="com.google.gwt.user.client.ui.impl.TextBoxImpl" out="com.google.gwt.user.client.ui.impl.TextBoxImpl"/>
	<rebind-decision in="com.symantec.client.TestEngine" out="com.symantec.client.TestEngine_Proxy"/>
	<rebind-decision in="com.google.gwt.user.client.ui.impl.FormPanelImpl" out="com.google.gwt.user.client.ui.impl.FormPanelImplSafari"/>
	<rebind-decision in="com.google.gwt.user.client.impl.HistoryImpl" out="com.google.gwt.user.client.impl.HistoryImplSafari"/>
	<rebind-decision in="com.google.gwt.user.client.impl.HTTPRequestImpl" out="com.google.gwt.user.client.impl.HTTPRequestImpl"/>
	<rebind-decision in="com.google.gwt.user.client.impl.DOMImpl" out="com.google.gwt.user.client.impl.DOMImplSafari"/>
	<rebind-decision in="com.symantec.client.PgnDriver" out="com.symantec.client.PgnDriver"/>
	<rebind-decision in="com.google.gwt.user.client.ui.impl.PopupImpl" out="com.google.gwt.user.client.ui.impl.PopupImpl"/>
</cache-entry>