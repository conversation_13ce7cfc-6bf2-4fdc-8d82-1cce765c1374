<html>
<head><script>
var $wnd = parent;
var $doc = $wnd.document;
var $moduleName = null;


window["provider$user.agent"] = function() {
  var ua = navigator.userAgent.toLowerCase();
  if (ua.indexOf('opera') != -1) {
    return 'opera';
  }
   else if (ua.indexOf('webkit') != -1) {
    return 'safari';
  }
   else if (ua.indexOf('msie 6.0') != -1 || ua.indexOf('msie 7.0') != -1) {
    return 'ie6';
  }
   else if (ua.indexOf('gecko') != -1) {
    var result = /rv:([0-9]+)\.([0-9]+)/.exec(ua);
    if (result && result.length == 3) {
      var version = parseInt(result[1]) * 10 + parseInt(result[2]);
      if (version >= 18)
        return 'gecko1_8';
    }
    return 'gecko';
  }
  return 'unknown';
}
;

window["values$user.agent"] = {
"gecko": 0, 
"gecko1_8": 1, 
"ie6": 2, 
"opera": 3, 
"safari": 4
};

window["prop$user.agent"] = function() {
  var v = window["provider$user.agent"]();
  var ok = window["values$user.agent"];
  if (v in ok)
    return v;
  var a = new Array(5);
  for (var k in ok)
    a[ok[k]] = k;
  $wnd.__gwt_onBadProperty("com.symantec.PgnDriver", "user.agent", a, v);
  if (arguments.length > 0) throw null; else return null;
};

function O(a,v) {
  var answer = O.answers;
  var i = -1;
  var n = a.length - 1;
  while (++i < n) {
    if (!(a[i] in answer)) {
      answer[a[i]] = [];
    }
    answer = answer[a[i]];
  }
  answer[a[n]] = v;
}
O.answers = [];


function selectScript() {
  try {
    var F;
    var I = ["true", (F=window["prop$user.agent"],F(1))];

    O(["true","opera"],"795397E1129F812D6DF52BF861C50BDA");
    O(["true","ie6"],"925861C177DB286FC0E1B12C848F84CE");
    O(["true","gecko1_8"],"C12B92AA8DA37CBE34A8ABB23CE85DD8");
    O(["true","gecko"],"C12B92AA8DA37CBE34A8ABB23CE85DD8");
    O(["true","safari"],"D0213FDF68B28BB315DDD9A5730C3468");

    var strongName = O.answers[I[0]][I[1]];
    var query = location.search;
    query = query.substring(0, query.indexOf('&'));
    var newUrl = strongName + '.cache.html' + query;
    location.replace(newUrl);
  } catch (e) {
    // intentionally silent on property failure
  }
}

function onLoad() {
  if (!$wnd.__gwt_isHosted) return;
  if (!$wnd.__gwt_isHosted()) {
    selectScript();
  }
  else {
    var mcb = $wnd.__gwt_tryGetModuleControlBlock(location.search);
    if (mcb) {
      $moduleName = mcb.getName();
      mcb.compilationLoaded(window);
    }
  }
}
</script></head>
<body onload='onLoad()'>
<font face='arial' size='-1'>This script is part of module</font> <code>com.symantec.PgnDriver</code>
</body>
</html>
