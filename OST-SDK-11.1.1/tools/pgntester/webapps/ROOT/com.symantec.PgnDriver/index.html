<html>
	<head>
	
		<!--                                           -->
		<!-- Any title is fine                         -->
		<!--                                           -->
		<title>NetBackup OpenStorage Plugin Tester</title>

		<!--                                           -->
		<!-- Use normal html, such as style            -->
		<!--                                           -->
		<link rel='stylesheet' href='PgnDriver.css'>

		<!--                                           -->
		<!-- The module reference below is the link    -->
		<!-- between html and your Web Toolkit module  -->		
		<!--                                           -->
		<meta name='gwt:module' content='com.symantec.PgnDriver'>
		
	</head>

	<!--                                           -->
	<!-- The body can have arbitrary html, or      -->
	<!-- you can leave the body empty if you want  -->
	<!-- to create a completely dynamic ui         -->
	<!--                                           -->
	<body>

		<!--                                            -->
		<!-- This script is required bootstrap stuff.   -->
		<!-- You can put it in the HEAD, but startup    -->
		<!-- is slightly faster if you include it here. -->
		<!--                                            -->
		<script language="javascript" src="gwt.js"></script>

		<!-- OPTIONAL: include this if you want history support -->
		<iframe id="__gwt_historyFrame" style="width:0;height:0;border:0"></iframe>

		<img src="images/symc_logo.jpg"></img>
		<h1>NetBackup<SMALL><SUP>TM</SUP></SMALL> OpenStorage Plugin Tester</h1>


		<table align=center>
			<tr>
				<td id="slot1"></td><td id="slot2"></td>
			</tr>
		</table>
		<!-- version 0.93 -->
	</body>
</html>
