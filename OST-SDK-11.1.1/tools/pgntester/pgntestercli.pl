#!/usr/bin/perl
#
# $Revision: 1.6 $
# $Date: 2010/02/09 07:34:11 $
# $Source_Device: QE/VxTAS/Test_Library/Adc/Change_Dataset.tst,v $
# $Author: hszhang $
#***************************************************************************
#* $VRTScprght:  Copyright 1993 - 2007 Symantec Software Corporation, 
#* All Rights Reserved 
#***************************************************************************
#ecpyrght
#*****************************************************************
# This material is proprietary of VERITAS Software Corporation.  *
# Possession and use of this material shall be strictly in       *
# accordance with a license agreement between user and VERITAS   *
# Sofware Corporation, and receipt or possession does not convey *
# any rights to divulge, reproduce, or allow others to use this  *
# material without specific written authorization of VERITAS     *
# Software Corporation.  Copyright 2000-2004, an unpublished     *
# work by VERITAS Software Corporation.  All Rights Reserved.    *
#*****************************************************************
# end_header


use strict;
use Getopt::Long;
use File::Spec;
use File::Basename;


#----------------------------------------------
#                  main
#----------------------------------------------
my $who = "main";

## variables
our $prog       = basename($0);
our $verbose    = 1;
my $help;
my $print;
my $printPlat;
my $printApi;
my $plat;
my $lib;
my $api;
my $sts;
my $storageServer;
my $lsu;
my $random;
my $time;
my $plat;
my $path;
my $cmd;
my $cmdout;
my $tcid        = 0;
my $tc;
my $result;

my %platforms =(solaris_SPARC64 =>"Solaris sparc64",
                solaris_x64 => "Solaris",
                linuxR_x64 => "RedHat",
                linuxS_x64 => "SuSE",
                x86 => "Win32",
                AMD64 => "WinX64",
                IA64 => "WinIA64",
                hp_ux64 => "HP-UX PA-RISC",
                hpia64 => "HP-UX IA64",
                rs6000 => "AIX");


my @testcaseOrder = ("claim",
                     "open_server",
                     "close_server",
                     "get_server_prop_byname",
                     "get_server_prop",
                     "get_lsu_prop_byname",
                     "open_lsu_list",
                     "list_lsu",
                     "close_lsu_list",
                     "lsulist",
                     "create_image",
                     "open_image",
                     "close_image",
                     "read_image",
                     "read_image_meta",
                     "write_image",
                     "write_image_meta",
                     "get_image_prop",
                     "delete_image",
                     "open_image_list",
                     "list_image",
                     "get_image_prop_byname",
					 "close_image_list",
					 "image",
					 "async_read_image",
					 "async_wait",
					 "async_write_image",
					 "close_evchannel",
					 "delete_event",
					 "get_event",
					 "open_evchannel",
					 "async_cancel",
					 "async_copy_image",
					 "copy_image",
					 "get_event_payload",
					 "named_async_cancel",
					 "named_async_copy_image",
					 "named_async_status",
					 "named_async_wait",
					 "get_server_config",
					 "set_server_config",
					 "begin_copy_image",
					 "end_copy_image",
					 "async_end_copy_image",
					 "named_async_end_copy_image",
					 "get_lsu_replication_prop",
					 "iocontrol"
					 );


## Get the Cmd Line options
Getopt::Long::GetOptions('l=s'               => \$lib,
                         'f=s'               => \$api,
                         'sts=s'             => \$sts,
                         'storage_server=s'  => \$storageServer,
                         'lsu=s'             => \$lsu,
                         'plat=s'            => \$plat,
                         'verbose=s'         => \$verbose,
                         'print'             => \$print,
                         'printplat'         => \$printPlat,
                         'printapi'          => \$printApi,
                         'help'              => \$help);

if ($help) {
   _help();
}

## print the platforms supported
if ($printPlat) {
    print "Platforms Supported\n";
    print "--------------------------------\n";

    foreach  (keys %platforms) {
        print "$platforms{$_} = $_\n";
    }

    exit 0;
}

## print the APIs
if ($printApi) {
    print "APIs\n";
    print "--------------------------------\n";

    foreach  (@testcaseOrder) {
        print "$_\n";
    }

    exit 0;
}

## validate the required cli options
if (!defined($lib)) {
   _logMsg("ERROR", "-l is required");
   _help();
}

if (!defined($sts)) {
   _logMsg("ERROR", "-sts is required");
   _help();
}

if (!defined($storageServer)) {
   _logMsg("ERROR", "-storage_server is required");
   _help();
}

if (!defined($lsu)) {
   _logMsg("ERROR", "-lsu is required");
   _help();
}

if (!defined($plat)) {
   _logMsg("ERROR", "-plat is required");
   _help();
}

## check to make sure the 
## platform is a valid one
if(!(grep /^$plat$/, (keys %platforms)))  {
    _logMsg("ERROR", "Platform [$plat] is not supported.");
    exit 0;
}


## create the path to the pgndriver
$path = File::Spec->catfile(("..","pgntester","webapps","ROOT","bin",$plat),"pgndriver");
_logMsg("INFO", "Running pgndriver [$path]");


## enclose the lib path in quotes for Windows
if($plat =~ /^x86|AMD64|IA64$/)  {
    $lib = "\"" . $lib . "\"";
}

my %testcases = (
    claim =>  [
        ["$path -l $lib -f claim -i $sts:$storageServer", "0"],
        ["$path -l $lib -f claim -i STSBasicDisk:$storageServer","2060009"],
        ["$path -l $lib -f claim -i AdvancedDisk:$storageServer","2060009"],
        ["$path -l $lib -f claim -i SharedDisk:$storageServer","2060009"],
        ["$path -l $lib -f claim -i nearstore:$storageServer","2060009"],
        ["$path -l $lib -f claim -i sampledisk:$storageServer","2060009"],
        ["$path -l $lib -f claim -t NULL","2060001"],
    ],
    open_server =>  [
        ["$path -l $lib -f open_server -i $sts:$storageServer","0"],
        ["$path -l $lib -f open_server -i $sts:_Not_A_vAlid_Server_Name","2060026"],
        ["$path -l $lib -f open_server -t NULL","2060001"],
    ],
    close_server =>  [
        ["$path -l $lib -f close_server -i $sts:$storageServer","0"],
        ["$path -l $lib -f close_server -i $sts:_Not_A_vAlid_Server_Name","2060026"],
        ["$path -l $lib -f close_server -t NULL","2060001"],
    ],
    get_server_prop_byname =>  [
        ["$path -l $lib -f get_server_prop_byname -i $sts:$storageServer","0"],
        ["$path -l $lib -f get_server_prop_byname -t NULL","2060001"],
        ["$path -l $lib -f get_server_prop_byname -i $sts:_Not_A_vAlid_Server_Name","2060026"],
    ],
    get_server_prop =>  [
        ["$path -l $lib -f get_server_prop -i $sts:$storageServer","0"],
        ["$path -l $lib -f get_server_prop -t NULL","2060001"],
    ],
    get_lsu_prop_byname =>  [
        ["$path -l $lib -f get_lsu_prop_byname -i $sts:$storageServer,$lsu","0"],
        ["$path -l $lib -f get_lsu_prop_byname -i $sts:$storageServer,$lsu,nclrf","0"],
        ["$path -l $lib -f get_lsu_prop_byname -i $sts:$storageServer,$lsu -t NULL","2060001"],
        ["$path -l $lib -f get_lsu_prop_byname -i $sts:$storageServer,_In-ValidLsu_1","2060013"],
    ],
    open_lsu_list =>  [
        ["$path -l $lib -f open_lsu_list -i $sts:$storageServer,$lsu","0"],
        ["$path -l $lib -f open_lsu_list -t NULL","2060001"],
        ["$path -l $lib -f open_lsu_list -i $sts:$storageServer,_In-ValidLsu_1","2060013"],
    ],
    list_lsu =>  [
        ["$path -l $lib -f list_lsu -t  NULL","2060001"],
    ],
    close_lsu_list =>  [
        ["$path -l $lib -f close_lsu_list -t NULL","2060001"],
    ],
    lsulist =>  [
        ["$path -l $lib -f lsulist -i $sts:$storageServer","0"],
    ],
    create_image =>  [
        ["$path -l $lib -f create_image -i $sts:$storageServer,$lsu,img00{random},{time}","0"],
        ["$path -l $lib -f create_image -t NULL -i $sts:$storageServer,$lsu,img00{random},{time}","2060001"],
    ],
    open_image =>  [
        ["$path -l $lib -f open_image -i $sts:$storageServer,$lsu,img00{random},{time}","0"],
        ["$path -l $lib -f open_image -t NULL -i $sts:$storageServer,$lsu,img00{random},{time}","2060001"],
    ],
    close_image =>  [
        ["$path -l $lib -f close_image -t NULL","2060001"],
    ],
    read_image =>  [
        ["$path -l $lib -f read_image -t NULL","2060001"],
        ["$path -l $lib -f read_image -t INVALID_BUFLEN","!0"],
        ["$path -l $lib -f read_image -t INVALID_OFFSET","!0"],
    ],
    read_image_meta =>  [
        ["$path -l $lib -f read_image_meta -t NULL","2060001"],
        ["$path -l $lib -f read_image_meta -t INVALID_BUFLEN","!0"],
        ["$path -l $lib -f read_image_meta -t INVALID_OFFSET","!0"],
    ],
    write_image =>  [
        ["$path -l $lib -f write_image -t NULL","2060001"],
        ["$path -l $lib -f write_image -t INVALID_BUFLEN","!0"],
        ["$path -l $lib -f write_image -t INVALID_OFFSET","!0"],
    ],
    write_image_meta =>  [
        ["$path -l $lib -f write_image_meta -t NULL","2060001"],
        ["$path -l $lib -f write_image_meta -t INVALID_BUFLEN","!0"],
        ["$path -l $lib -f write_image_meta -t INVALID_OFFSET","!0"],
    ],
    get_image_prop =>  [
        ["$path -l $lib -f get_image_prop -i $sts:$storageServer,$lsu,img00{random},{time}","0"],
        ["$path -l $lib -f get_image_prop -t NULL -i $sts:$storageServer,$lsu,img00{random},{time}","2060001"],
    ],
    delete_image =>  [
        ["$path -l $lib -f delete_image -t NULL","2060001"],
    ],
    open_image_list =>  [
        ["$path -l $lib -f open_image_list -t NULL","2060001"],
    ],
    list_image =>  [
        ["$path -l $lib -f list_image -t NULL","2060001"],
    ],
    get_image_prop_byname =>  [
        ["$path -l $lib -f get_image_prop_byname -i $sts:$storageServer,$lsu,img00{random},{time}","0"],
        ["$path -l $lib -f get_image_prop_byname -t NULL -i $sts:$storageServer,$lsu,img00{random},{time}","2060001"],
    ],
    close_image_list =>  [
        ["$path -l $lib -f close_image_list -t  NULL","2060001"],
    ],
    image =>  [
        ["$path -l $lib -f image -i $sts:$storageServer,$lsu,img00{random},{time}","0"],
    ],
    get_server_config =>  [
        ["$path -l $lib -f get_server_config -i $sts:$storageServer","0"],
        ["$path -l $lib -f get_server_config -t NULL","2060001"],
    ],
    set_server_config =>  [
        ["$path -l $lib -f set_server_config -i $sts:$storageServer","0"],
        ["$path -l $lib -f set_server_config -t NULL","2060001"],
    ],
    async_read_image =>  [
        ["$path -l $lib -f async_read_image -t NULL","2060001"],
        ["$path -l $lib -f async_read_image -t INVALID_BUFLEN","!0"],
        ["$path -l $lib -f async_read_image -t INVALID_OFFSET","!0"],
    ],
    async_wait =>  [
        ["$path -l $lib -f async_wait -t  NULL","2060001"],
    ],
    async_write_image =>  [
        ["$path -l $lib -f async_write_image -t NULL","2060001"],
        ["$path -l $lib -f async_write_image -t INVALID_BUFLEN","!0"],
        ["$path -l $lib -f async_write_image -t INVALID_OFFSET","!0"],
    ],
    async_cancel =>  [
        ["$path -l $lib -f async_cancel -t  NULL","2060001"],
    ],
    async_copy_image =>  [
        ["$path -l $lib -f async_copy_image -t  NULL","2060001"],
    ],
    copy_image =>  [
        ["$path -l $lib -f copy_image -t  NULL","2060001"],
    ],
    named_async_cancel =>  [
        ["$path -l $lib -f named_async_cancel -t  NULL","2060001"],
    ],
    named_async_copy_image =>  [
        ["$path -l $lib -f named_async_copy_image -t  NULL","2060001"],
    ],
    named_async_status =>  [
        ["$path -l $lib -f named_async_status -t  NULL","2060001"],
    ],
    named_async_wait =>  [
        ["$path -l $lib -f named_async_wait -t  NULL","2060001"],
    ],
    close_evchannel =>  [
        ["$path -l $lib -f close_evchannel -t  NULL","2060001"],
    ],
    delete_event =>  [
        ["$path -l $lib -f delete_event -t  NULL","2060001"],
    ],
    get_event =>  [
        ["$path -l $lib -f get_event -t  NULL","2060001"],
    ],
    open_evchannel =>  [
        ["$path -l $lib -f open_evchannel -i $sts:$storageServer","0"],
        ["$path -l $lib -f open_evchannel -t  NULL","2060001"],
    ],
    get_event_payload =>  [
        ["$path -l $lib -f get_event_payload -t  NULL","2060001"],
    ],
    begin_copy_image =>  [
        ["$path -l $lib -f begin_copy_image -t  NULL","2060001"],
    ],
    end_copy_image =>  [
        ["$path -l $lib -f end_copy_image -t  NULL","2060001"],
    ],
    async_end_copy_image =>  [
        ["$path -l $lib -f async_end_copy_image -t  NULL","2060001"],
    ],
    named_async_end_copy_image =>  [
        ["$path -l $lib -f named_async_end_copy_image -t  NULL","2060001"],
    ],
    get_lsu_replication_prop =>  [
        ["$path -l $lib -f get_lsu_replication_prop -t  NULL","2060001"],
    ],
    iocontrol =>  [
        ["$path -l $lib -f iocontrol -t  NULL","2060001"],
    ],
);


## print the testcases
if ($print) {
    _printTestCase(\@testcaseOrder,\%testcases);
    exit 0;
}


## make sure the api provided is valid
if(defined $api)  {
    if(!(grep /^$api$/, (keys %testcases)))  {
        _logMsg("ERROR", "API [$api] is not valid.");
        exit 0;
    }
}


##set up logging
$path = File::Spec->catdir(("..","pgntester","logs"));
mkdir $path;
$path = File::Spec->catfile($path,$prog.time());

print "[Logfile] $path\n";

open(LOGFILE, ">> $path")
      or die "${prog}:ERROR:Could not open file: $!\n";


##execute the selected api or 
##execute all apis
if($api)  {
    foreach $tc (@{$testcases{$api}}) {
        _subTestCaseVars(\@$tc[0]);
        $result = _runTestCase(@$tc[0], \$cmdout);
        $result = _getTestResult("${api}::${tcid}", $cmdout, @$tc[1]);
        $tcid+=1;
    }

}else  {
    foreach $api (@testcaseOrder) {
        $tcid = 0;

        foreach $tc (@{$testcases{$api}}) {
            _subTestCaseVars(\@$tc[0]);
            $result = _runTestCase(@$tc[0], \$cmdout);
            $result = _getTestResult("${api}::${tcid}", $cmdout, @$tc[1]);
            $tcid+=1;
        }
    }
}

close LOGFILE;


##End main



##
##  substitute testcase variables
##
sub _subTestCaseVars {
    my $who = "_parseTestCase";

    my ($tc) = @_;
    my $random = int(rand 2000) + 1;
    my $time = time();

    $$tc =~ s/\{random\}/$random/g;
    $$tc =~ s/\{time\}/$time/g;

    return 0;
}


##
##  execute the testcases and
##  return the output
##
sub _runTestCase {
    my $who = "_runTestCase";

    my ($cmd, $cmdout) = @_;

    _logMsg("INFO", "----------------------------------------");
    _logMsg("INFO", "Executing [$cmd]");

    if($plat =~ /^x86|AMD64|IA64$/)  {
        $$cmdout = `$cmd 2>&1`;
    }else  {
        $$cmdout = `$cmd 2>&1 1>/dev/null`;  #`$cmd 2>&1`;       #captures STDOUT and STDERR
    }

    _logMsg("INFO", "Cmdout [$$cmdout]");


    return 0;
}


##
##  execute the testcases and
##  return the output
##
sub _getTestResult {
    my $who = "_getTestResult";

    my ($tcid, $cmdout, $expect) = @_;
    my ($errCode, $err);

    my @lines = split /\n/, $cmdout;
    my @errCodes = ();

    foreach $cmdout (@lines) {
        next if ($cmdout =~ /^Function/);
        ($errCode, $err) = split /:/, $cmdout;
        $errCode =~ s/\s//g;            #remove any leading or trailing whitespaces
        push @errCodes, $errCode;
    }

    if((length @errCodes) == 0)  {
        _logMsg("ERROR", "[${tcid}] No err codes from command captured");
        return -1;
    }

    ## It is unpredicatable what NULL tests
    ## will return for the first iteration.  ignore.
    if($expect eq "2060001")  {
        $errCode = shift @errCodes;

    #    if($errCode ne "0")  {
    #        _logMsg("ERROR", "[${tcid}::Result] FAILED.  Expected[0]->Received[$errCode]");
    #    }
    }

    ##look for multiple err codes
    ##returned by the NULL tests.
    foreach $errCode (@errCodes) {
        if(($errCode ne $expect))  {
            if(($expect eq "!0") && ($errCode ne "0"))  {
                _logMsg("TESTRESULT", "[${tcid}] PASSED");
                next;
            }

            _logMsg("TESTRESULT", "[${tcid}] FAILED.  Expected[$expect]->Received[$errCode]");
        }else  {
            _logMsg("TESTRESULT", "[${tcid}] PASSED");
        }
    }

    return 0;
}


##
##  print the testcases
##
sub _printTestCase {
    my $who = "_printTestCase";

    my ($order, $testcases) = @_;
    my $tcid;

    foreach $api (@$order) {
        $tcid = 0;
        print "----------------------------------------\n";
        print "$api\n";
        print "----------------------------------------\n";

        foreach $tc (@{$testcases{$api}}) {
            $tcid+=1;
            print "[${api}::${tcid}]@${tc[0]}::@$tc[1]\n";
        }
    }
    
    return 0;
}

##
##  handle logging levels.
##  format messages.
##
sub _logMsg {
   my ($level, $msg) = @_;

   if($level !~ /^(INFO|WARNING|ERROR|DEBUG|TESTRESULT)$/)  {
      $level = "UNKNOWN";
   }

   print LOGFILE "${prog}::${level}::${msg}\n";

   if(($verbose < 4) && ($level eq "DEBUG"))  {
      return;
   }elsif(($verbose < 3) && ($level eq "INFO"))  {
      return;
   }elsif(($verbose < 2) && ($level eq "WARN"))  { 
      return;
   }elsif(($verbose < 1) && ($level =~ /ERROR|TESTRESULT/))  {
      return;
   }elsif($verbose == 0)  {
      return;
   }

   print "${prog}::${level}::${msg}\n";
   return;
}


##
##  print the usage
##
sub _usage {
   print "Usage: $prog [-l /path/to/libstspi<plugin>.so][-f claim]
        [-sts sts prefix][-storage_server storage_server name]
        [-lsu lsu name][-help][-print]\n";
}


sub _help {
   _usage();
   print "\nThis program will execute pgntester tesetcases

Options:
   -help                      print this help message and exit.
   -print                     Print all the testcases and exit.
   -printplat                 Print all the platforms that the
                                 -plat parameter accepts.
   -printapi                  Print all the APIs that the
                                 -f parameter accepts.
   -verbose <level>           The level of verbosity <1-3>. Default is 1.
   -l <path>                  The path to the plugin.  Required.
   -f <api>                   The API to test.  Default is all.
   -sts <sts>                 The OpenStorage Plugin prefix. Required.
   -storage_server  <server>  The OpenStorage storage server.  Required.
   -lsu <lsu>                 The OpenStorage LSU.  Required.
   -plat <platform>           The host platform. Required.
";

   exit;
}
