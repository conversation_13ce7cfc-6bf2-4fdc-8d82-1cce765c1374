#!/bin/bash
#bcpyrght
#***************************************************************************
#* $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $ *
#***************************************************************************
#ecpyrght


if [ "$1" = "help" ]
then
	echo "Usage: pgntester [PORT]";
	echo "Default PORT = 8080";
	exit;
fi

mkdir -p logs

if [ "x$1" = "x" ]
then
		  PORT=8080
else
		  PORT=$1
fi

echo "Starting pgntester on port $PORT"
echo "The log file for this execution instance is logs/`date +%m%d%Y%H%M`.log"

java -cp .:./lib/catalina.jar:./lib/tomcat-util.jar:./lib/catalina-optional.jar:./lib/servlet-api.jar:./lib/commons-logging-api.jar:./lib/commons-modeler.jar:./lib/naming-resources.jar:./lib/tomcat-coyote.jar:./lib/tomcat-http.jar:./lib/catalina-storeconfig.jar:./lib/naming-factory.jar:./lib/jasper-runtime.jar:./lib/jasper-compiler.jar:./lib/servlets-default.jar:./lib/jsp-api.jar:./lib/commons-el.jar:./lib/gwt-user.jar:./bin com.symantec.emtomcat.EmbeddedTomcat $PORT &> ./logs/`date +%m%d%Y%H%M`.log &

