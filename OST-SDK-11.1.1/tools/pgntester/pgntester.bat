@REM
@REM $Id: pgntester.bat,v 1.3 2006/12/08 20:08:33 $
@REM

@REM **************************************************************
@REM $VRTScprght: Copyright 1993 - 2009 Symantec Corporation, All Rights Reserved $
@REM **************************************************************

@REM
@REM This is the script to launch the OpenStorage plugin tester tool
@REM The tester is a web based UI that can be used to test 
@REM an implementation OpenStorage Interface implementation
@REM


@REM 
@REM Port to use.. the first option on the CLI if available.. 
@REM Otherwise the default which is 8080
@REM

@IF "%1" == "/help" (
	@ECHO.
	@ECHO Usage:
	@ECHO	     pgntester.bat [Port] [logFile]
	@ECHO	     Default Port = 8080
	@ECHO	     Default Log = pgntester.log
	@ECHO.
	goto done
)

@IF "%1"=="" (
	SET PORT=8080
) ELSE (
	SET PORT=%1
)


@IF "%2"=="" (
	SET LOG_FILE=pgntester.log
) ELSE (
	SET LOG_FILE=%2
)

@ECHO.
@ECHO	Starting pgntester on port %PORT%
@ECHO	The log file for this session is %LOG_FILE%
@ECHO.

@ECHO OFF
java -cp .;.\lib\catalina.jar;.\lib\tomcat-util.jar;.\lib\catalina-optional.jar;.\lib\servlet-api.jar;.\lib\commons-logging-api.jar;.\lib\commons-modeler.jar;.\lib\naming-resources.jar;.\lib\tomcat-coyote.jar;.\lib\tomcat-http.jar;.\lib\catalina-storeconfig.jar;.\lib\naming-factory.jar;.\lib\jasper-runtime.jar;.\lib\jasper-compiler.jar;.\lib\servlets-default.jar;.\lib\jsp-api.jar;.\lib\commons-el.jar;.\lib\gwt-user.jar;.\bin com.symantec.emtomcat.EmbeddedTomcat %PORT% > %LOG_FILE%

