pgndriver.cpp                                                                                       0000444 0010363 0000156 00000172017 11351577541 0015172 0                                                                                                    ustar 00hszhang                         minstaff                        0000555 0014074                                                                                                                                                                        /*
 *bcpyrght
 ***************************************************************************
 * $VRTScprght: Copyright 1993 - 2010 Symantec Corporation, All Rights Reserved $ *
 ***************************************************************************
 *ecpyrght
 */

#include <iostream>
#include <iomanip>
#include <string>
#include <sstream>
#include <map>

#include <sys/types.h>
#include <sys/stat.h>
#include <fcntl.h>
#ifndef _WIN32
#include <dlfcn.h>
#endif
#include <errno.h>

//#include <md5global.h>
//#include <md5.h>

using namespace std;

#include "signal.h"

#include "stspi.h"

#define OSTSDK_PGNTESTER_DPAID "OSTSDK_PGNTESTER"
#define PRIVATE_ERROR_BASE -255

#define LOG_ERROR(n,msg) \
	cerr << n << " : " << msg << endl;

#define INVOKE(x) (*(ep->v11_##x))	

#define REPORT_OP(x) cout << "Function: " << "stspi_" << x << endl;
#define REPORT_PARAM(x) cout<<"Parameter(s): "<<x<<endl;

#define REPORT(x) testID = __LINE__ - 1; \
	if(x == STS_EOK) \
	cerr << "0: " << endl; \
	else cerr << x << ": " << error_map[x] << endl;

#define REPORT_OUTPUT(x,ost) testID = __LINE__ - 1; \
	if(x == STS_EOK) \
	cerr << "0: " << ost.str() << endl; \
	else cerr << x << ": " << error_map[x] << endl;

#define ASSERT_SUCCESS(x,func) \
	if(x != STS_EOK) { \
		cerr << x << ": Prerequisite test failed ("<< func <<": "<< error_map[x] << ") " << endl; \
		exit(-1); }

#define ASSERT_NEG_SUCCESS(x,func) \
	if(x == STS_EOK) { \
		cerr << x << ": Prerequisite test failed ("<< func <<": "<< error_map[x] << ") " << endl; \
		exit(-1); }

/* an empty event handler */
void empty_evc_handler(sts_event_t*){};

void sd_log_func(
	const char *session_id, 
	const char *message,
	sts_severity_t severity)
{
#ifndef WIN32
	cout << "Plugin Log: " << (message == NULL ? "" : message) << endl;
#endif
}

/* Global variables */
string operation;
string test_type;
string sts_version;
int testID = 0;
int null_pos_flag = 0x0;



class ArgParser{
private:
	map<string,string> argMap;

public:
	bool isHelp;
	ArgParser(int ac, char **av):
		isHelp(false)
	{
		string key,value;

		while(ac > 1){
			ac--, av++;
			if((*av)[0] != '-') return;

			key = (*av)+1;
			ac--,av++;
			if(ac > 0) value = *(av);

			if(key == "h") isHelp = true;
			argMap[key] = value;
		}
	}

	string getOption(string key){
		return argMap[key];
	}
};


map<int,string> error_map;

void 
load_error_map(){
	for(int i=0; i<55; i++){
		error_map[sts_errtostring[i].error] = string(sts_errtostring[i].string);
	}
	error_map[PRIVATE_ERROR_BASE+SIGILL] = "Illegal instruction Fault";
	error_map[PRIVATE_ERROR_BASE+SIGSEGV] = "Segmentation Fault";
#ifndef WIN32
	error_map[PRIVATE_ERROR_BASE+SIGBUS] = "Bus Error";
#endif
	error_map[PRIVATE_ERROR_BASE+SIGABRT] = "Abort";
}

#ifdef WIN32
HINSTANCE lib_handle;
#else
void *lib_handle = NULL;
#endif

/*
 * This function performs following operations
 *	- Load the plugin shared library using dlopen or LoadLibrary.
 *	- Extract stspi_init function pointer
 *	- Invoke stspi_init function and get function pointers for other
 *	calls
 */

stspi_ep_v11_t *
load_entrypoints(string libpath)
{
	const char *error_msg;
	const char *lib = libpath.c_str();

	stsp_init_t init_func_ptr = NULL;
	
	cout << "loaded entrypoints" << endl;

#ifdef WIN32

	/* Open the plugin library */
	lib_handle = LoadLibrary(lib);

	if(lib_handle == NULL){
		error_msg = "LoadLibrary failed";
		LOG_ERROR(-1,error_msg);
		exit(1);
	} 

	/* Extract the init function pointer */
	init_func_ptr = (stsp_init_t) GetProcAddress(lib_handle,STSPI_INIT);

	if(!init_func_ptr){
		error_msg = "GetProcAddress for init_func_ptr failed";
		LOG_ERROR(-1,error_msg);
		FreeLibrary(lib_handle);
		exit(1);
	}


#else
	/* Open the plugin library */
	lib_handle = dlopen(lib,RTLD_NOW|RTLD_GLOBAL);

	if(!lib_handle){
		error_msg = dlerror();
		if(error_msg){
			LOG_ERROR(-1,error_msg);
			exit(1);
		}
	}

	/* Extract the init function pointer */

	init_func_ptr = (stsp_init_t) dlsym(lib_handle,STSPI_INIT);

	if(!init_func_ptr){
		error_msg = dlerror();
		if(error_msg){
			LOG_ERROR(-1,error_msg);
			dlclose(lib_handle);
			exit(1);
		}
	}
#endif
	int res = 0;
	stspi_api_t stspAPI;
	sts_uint64_t verout = 0;


	/*
	 * If this call becomes successful then stsAPI will contain
	 * pointers for all STS-P-I functions
	 * Version output added 0309 MJB
	 */
	res = (*init_func_ptr)(STS_VERSION,"",&stspAPI);

	if(res == STS_EOK){
	verout = stspAPI.spx_version;
		if (verout == 11 ) {
			cout << "plugin implements current version: ";
			cout << verout << endl; 
		} else {
			cout << "plugin implements older version: ";
			cout << verout << endl;
		}
		return stspAPI.spx_ep.v11_ep;
	} else {
		LOG_ERROR(-1,"INIT failed");
		return NULL;
	}
}

/*
 *	This is the signal handler that handles scenarios where plugin
 *	crashes for NULL values of certain parameters.
 *
 *	The two global variable operation and null_pos_flag tell what 
 *	function call and what pattern of inputs caused this crash.
 *	Using that information this function generates an error string
 *	that will tell the user where to add NULL check.
 */
extern "C"
void
program_error_signal_handler(int signum)
{
	REPORT(PRIVATE_ERROR_BASE+signum);
	if(test_type == "NULL"){
		ostringstream ost;
		if(operation == "claim"){
			ost << operation << "(" << "NULL" << ")";
		} else if(operation == "open_server"){
			ost << operation << "(" <<
					  	((null_pos_flag&0x10)?"NULL":"&sd") << "," <<
						((null_pos_flag&0x08)?"NULL":"sname") <<  "," <<  
						((null_pos_flag&0x04)?"NULL":"&cred") <<  "," << 
						((null_pos_flag&0x02)?"NULL":"iface") <<  "," << 
						((null_pos_flag&0x01)?"NULL":"&handle") << ")";  
		} else if(operation == "close_server"){
			ost << operation << "(" << "NULL" << ")";
		} else if(operation == "get_server_prop_byname"){
			ost << operation << "(" <<
						 ((null_pos_flag&0x04)?"NULL":"&sd") << "," <<
						 ((null_pos_flag&0x02)?"NULL":"sname") << "," <<
						 ((null_pos_flag&0x01)?"NULL":"&info") << ")";
		} else if(operation == "get_server_prop"){
			ost << operation << "(" <<
					 	((null_pos_flag&0x02)?"NULL":"handle") << "," << 
					 	((null_pos_flag&0x01)?"NULL":"&info") << ")";
		} else if(operation == "get_lsu_prop_byname") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x02)?"NULL":"&lsu") << "," <<
						 ((null_pos_flag&0x01)?"NULL":"&info") << ")";
		} else if(operation == "open_lsu_list") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x04)?"NULL":"shandle") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"&lsudef") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&lhandle") << ")";
		} else if(operation == "list_lsu") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x02)?"NULL":"lhandle") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&lsu") << ")";
		} else if(operation == "close_lsu_list") {
			ost << operation << "(" << "NULL" << ")";
		} else if(operation == "create_image") {
			ost << operation << "(" <<
									 ((null_pos_flag&0x04)?"NULL":"&lsu") << "," <<
									 ((null_pos_flag&0x02)?"NULL":"&img")<< "," <<
									 "0" << "," <<
									 ((null_pos_flag&0x01)?"NULL":"&ihandle") << ")";
		} else if(operation == "open_image") {
			ost << operation << "(" <<
									 ((null_pos_flag&0x04)?"NULL":"&lsu")<< "," << 
									 ((null_pos_flag&0x02)?"NULL":"&img")<< "," <<
									 "0"<< "," <<
									 ((null_pos_flag&0x01)?"NULL":"&ihandle") << ")";
		} else if(operation == "read_image") {
			ost << operation << "(" <<
									 ((null_pos_flag&0x04)?"NULL":"ihandle") << "," <<
									 ((null_pos_flag&0x02)?"NULL":"buf") << "," << 
									 "0" << "," << 
									 "0" << "," <<
									 ((null_pos_flag&0x01)?"NULL":"&bytesRead") << ")";
		} else if(operation == "write_image") {
			ost << operation << "(" <<
									 ((null_pos_flag&0x08)?"NULL":"ihandle") << "," <<
									 ((null_pos_flag&0x04)?"NULL":"&stat") << "," <<
									 ((null_pos_flag&0x02)?"NULL":"buf") << "," <<
									 "0" << "," <<
									 "0" << "," <<
									 ((null_pos_flag&0x01)?"NULL":"&bytesWritten") << ")";
		} else if(operation == "open_image_list") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x04)?"NULL":"&lhandle") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"type") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&ihandle") << ")";
		} else if(operation == "list_image") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x02)?"NULL":"ihandle") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&image") << ")";
		} else if(operation == "close_image_list") {
			ost << operation << "(" << "NULL" << ")";
		} else if(operation == "get_server_config") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x04)?"NULL":"handle") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"buf") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&maxlen") << ")";
		} else if(operation == "set_server_config") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x04)?"NULL":"handle") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"buf") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"msg_buf") << ")";
		} else if(operation == "async_read_image") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x04)?"NULL":"ihandle") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"buf") << "," << 
						 ((null_pos_flag&0x02)?"0":"buflen") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&opid") << ")";
		} else if(operation == "async_wait") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x04)?"NULL":"&sd") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"opid") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&aio_result") << ")";
		} else if(operation == "async_write_image") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x08)?"NULL":"ihandle") << "," << 
						 ((null_pos_flag&0x04)?"NULL":"&stat") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"buf") << "," << 
						 ((null_pos_flag&0x02)?"0":"buflen") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&opid") << ")";
		} else if(operation == "async_wait") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x02)?"NULL":"&sd") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"opid") << ")";
		} else if(operation == "async_copy_image") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x10)?"NULL":"&to_lsu") << "," << 
						 ((null_pos_flag&0x08)?"NULL":"&to_img") << "," << 
						 ((null_pos_flag&0x04)?"NULL":"&from_lsu") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"&from_img") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&opid") << ")";
		} else if(operation == "copy_image") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x08)?"NULL":"&to_lsu") << "," << 
						 ((null_pos_flag&0x04)?"NULL":"&to_img") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"&from_lsu") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&from_img") << ")";
		} else if(operation == "named_async_cancel") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x01)?"NULL":"server_handle") << ")";
		} else if(operation == "named_async_copy_image") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x08)?"NULL":"&to_lsu") << "," << 
						 ((null_pos_flag&0x04)?"NULL":"&to_img") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"&from_lsu") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&from_img") << ")";
		} else if(operation == "named_async_status") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x01)?"NULL":"server_handle") << ")";
		} else if(operation == "named_async_wait") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x02)?"NULL":"server_handle") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&aio_result") << ")";
		} else if(operation == "close_evchannel") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x01)?"NULL":"evc_handle") << ")";
		} else if(operation == "delete_event") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x02)?"NULL":"handle") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&event") << ")";
		} else if(operation == "get_event") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x02)?"NULL":"evc_handle") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&event") << ")";
		} else if(operation == "open_evchannel") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x40)?"NULL":"&sd") << "," << 
						 ((null_pos_flag&0x20)?"NULL":"sname") << "," << 
						 ((null_pos_flag&0x10)?"NULL":"&cred") << "," << 
						 ((null_pos_flag&0x08)?"NULL":"iface") << "," << 
						 ((null_pos_flag&0x04)?"NULL":"handler") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"&event") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&pevc_handle") << ")";
		} else if(operation == "get_event_payload") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x04)?"NULL":"svh") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"&plbuf") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&event") << ")";
		} else if(operation == "copy_image") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x08)?"NULL":"&to_lsu") << "," << 
						 ((null_pos_flag&0x04)?"NULL":"&to_img") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"&from_lsu") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&from_img") << ")";
		} else if(operation == "begin_copy_image") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x04)?"NULL":"&to_lsu") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"&to_img") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"image_handle") << ")";
		} else if(operation == "end_copy_image") {
			ost << operation << "(" << "NULL" << ")";
		} else if(operation == "async_end_copy_image") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x02)?"NULL":"image_handle") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&opid") << ")";
		} else if(operation == "named_async_end_copy_image") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x01)?"NULL":"image_handle") << ")";
		} else if(operation == "get_lsu_replication_prop") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x04)?"NULL":"&lsu") << "," << 
						 ((null_pos_flag&0x02)?"NULL":"&source") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"&target") << ")";
		} else if(operation == "iocontrol") {
			ost << operation << "(" <<
						 ((null_pos_flag&0x02)?"NULL":"image_handle") << "," << 
						 ((null_pos_flag&0x01)?"NULL":"args") << ")";
		}
			
		cout << error_map[PRIVATE_ERROR_BASE+signum] << 
			  ": Failed function call = " << ost.str() << endl;
	}


	exit(-1);
}

void
print_usage()
{
	cout << "pgndriver <-option option_value> " << endl <<
			  "Option	Value" << endl <<
			  "-l			path to plugin library" << endl <<
			  "-f			function name to test"	<< endl <<
			  "-t			type of test [REGULAR(default),NULL]" << endl <<
			  "-i			Input for the test "	<< endl <<
			  "-v			OST major Version.  11 is default."	<< endl <<
			  "-h			Help "	<< endl;
}

int main(int ac, char **av){
	
	int result;
	stspi_ep_v11_t *ep;

	ArgParser *ap = new ArgParser(ac,av);

	if(ap->isHelp) {
		print_usage();
		exit(0);
	}

	load_error_map();

	/*
	 * Register signal handlers for crash scenario signals 
	 */
#ifndef WIN32
	if(signal(SIGILL,program_error_signal_handler) == SIG_ERR){
		LOG_ERROR(-2,"Could not register signal handler for SIGILL");
	}else if(signal(SIGSEGV,program_error_signal_handler) == SIG_ERR){
		LOG_ERROR(-2,"Could not register signal handler for SIGSEGV");
	}else if(signal(SIGBUS,program_error_signal_handler) == SIG_ERR){
		LOG_ERROR(-2,"Could not register signal handler for SIGBUS");
	}else if(signal(SIGABRT,program_error_signal_handler) == SIG_ERR){
		LOG_ERROR(-2,"Could not register signal handler for SIGABRT");
	}
#endif

	/*
	 * Read the library path from command line, then load the library
	 * and STS-P-I function pointers
	 */
	string libpath = ap->getOption("l");
	if(libpath != ""){
		ep = load_entrypoints(libpath);
		if(ep == NULL){
			LOG_ERROR(-1,"EntryPoints could not be loaded");
			exit(-1);
		}
	}else  {
		print_usage();
		exit(0);
	}
	
	operation = ap->getOption("f");
	test_type = ap->getOption("t");
	sts_version = ap->getOption("v");


	if(sts_version.empty())  {
		sts_version = "11";
	}
	
	/*
	 *	The following is a long if-else structure for each of the operations
	 *
	 *	Each operations has two types of tests - NULL and regular.
	 *
	 * NULL tests -
	 *	Most of the parameters to STS-P-I functions are pointers. It is expected 
	 *	that the plugin does not dereference a NULL pointer leading to the crash 
	 *	of loading NBU process. Hence this special test is included in SDK. 
	 *	If a function call has N number of input parameters that are pointers, 
	 *	then there are 2^N possible combinations in which NULL parameters can be 
	 *	passed to that function. 
	 *	The pgndriver code does this by calling that function 2^N number of times, 
	 *	passing all possible combinations of NULL parameters. 
	 *	If the plugin is not doing NULL checks before dereferencing into 
	 *	these pointers, then it will crash with SIGSEGV or SIGABRT or similar crash.
	 *
	 */

	/*
	 *	The INVOKE macro:
	 *	I used this macro to make the code more readable. I didn't want to
	 *	see the dereferencing into "ep" structure and superfluous prefixes. Hence
	 *	I hid those irrelevant details behind an intuitive macro name.
	 */


	/* Testing stspi_claim */
	if(operation == "claim") {
			  
		if(test_type == "NULL"){
			REPORT_OP("claim");
			result = INVOKE(claim)(NULL);
			REPORT(result);
		} else {
			string servername = ap->getOption("i");
			REPORT_OP("claim");
			result = INVOKE(claim)(servername.c_str());
			REPORT(result);
		}

	/* Testing stspi_open_server */
	} else if(operation == "open_server") {

		if(test_type == "NULL"){
			sts_session_def_t sd;
			memset(&sd,0,sizeof(sts_session_def_t));
			sts_cred_t cred;
			memset(&cred,0,sizeof(sts_cred_t));
			stsp_server_handle_t handle; 
			sts_server_name_t sname = "invalidpfx:www.symantec.com"; //not-NULL yet invalid
			sts_interface_t iface = "interface"; //not-NULL yet invalid

			for(null_pos_flag=0x00; null_pos_flag<32; null_pos_flag++){
				REPORT_OP("open_server");
				result = INVOKE(open_server)(
								(null_pos_flag&0x10)?NULL:&sd, 
								(null_pos_flag&0x08)?NULL:sname, 
								(null_pos_flag&0x04)?NULL:&cred, 
								(null_pos_flag&0x02)?NULL:iface, 
								(null_pos_flag&0x01)?NULL:&handle); 
				REPORT(result);
			}
		} else {
			sts_session_def_t sd;
			sd.sd_id = "ID 0";
			sd.sd_log = sd_log_func;
			sts_cred_t cred;
			stsp_server_handle_t handle;
			sts_interface_t iface = "";

			string servername = ap->getOption("i");
			REPORT_OP("open_server");
			result = INVOKE(open_server)(&sd,servername.c_str(),&cred,iface,&handle);
			REPORT(result);
		}
		
	/* Testing stspi_close_server */
	} else if(operation == "close_server") {
		if(test_type == "NULL"){
			REPORT_OP("close_server");
			result = INVOKE(close_server)(NULL);
			REPORT(result);
		} else {
			sts_session_def_t sd;
			sd.sd_id = "ID 0";
			sd.sd_log = sd_log_func;
			sts_cred_t cred;
			stsp_server_handle_t handle;
			sts_interface_t iface = "";
			sts_server_name_t sname;
			string servername = ap->getOption("i");
			REPORT_OP("open_server");
			strcpy(sname,servername.c_str());
			
			result = INVOKE(open_server)(&sd,sname,&cred,iface,&handle);
			ASSERT_SUCCESS(result,"open_server");
			REPORT_OP("close_server");
			result = INVOKE(close_server)(handle);
			REPORT(result);
		}

	/* Testing stspi_get_server_prop_byname */
	} else if(operation == "get_server_prop_byname") {

		if(test_type == "NULL"){
			sts_session_def_t sd;
			sd.sd_id = "ID 0";
			sd.sd_log = sd_log_func;
			sts_server_name_t sname = "invalidpfx:www.symantec.com";
			sts_server_info_t info;

			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("get_server_prop_byname");
				result = INVOKE(get_server_prop_byname)(
									 (null_pos_flag&0x04)?NULL:&sd,
									 (null_pos_flag&0x02)?NULL:sname,
									 (null_pos_flag&0x01)?NULL:&info);
				REPORT(result);
			}
		} else {
			ostringstream ost;
			sts_session_def_t sd;
			sd.sd_id = "ID 0";
			sd.sd_log = sd_log_func;
			sts_server_info_t info;
			string servername = ap->getOption("i");
			sts_server_name_t sname;
			strcpy(sname, servername.c_str());
			REPORT_OP("get_server_prop_byname");
			result = INVOKE(get_server_prop_byname)(&sd,sname,&info);
			if(result == STS_EOK){
				ost << info.srv_server << endl;
			}
			REPORT_OUTPUT(result,ost);
		}

	/* Testing stspi_get_server_prop */
	} else if(operation == "get_server_prop") {

		if(test_type == "NULL"){
			stsp_server_handle_t handle = 0;
			sts_server_info_t info;

			/* 
			* start at 0x01.  starting at 0x00 will send an
			* uninitialized handle to the API.  This causes
			* a crash on Windows
			*/
			for(null_pos_flag=0x01; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("get_server_prop");
				result = INVOKE(get_server_prop)(
									 (null_pos_flag&0x02)?NULL:handle,
									 (null_pos_flag&0x01)?NULL:&info);
				REPORT(result);
			}
		} else {
			ostringstream ost;
			sts_session_def_t sd;
			sd.sd_id = "ID 0";
			sd.sd_log = sd_log_func;
			sts_cred_t cred;
			stsp_server_handle_t handle;
			sts_interface_t iface = "";
			sts_server_info_t info;

			string servername = ap->getOption("i");
			REPORT_OP("open_server");
			sts_server_name_t sname;
			strcpy(sname, servername.c_str());
			result = INVOKE(open_server)(&sd,sname,&cred,iface,&handle);
			ASSERT_SUCCESS(result,"open_server");
			REPORT_OP("get_server_prop");
			result = INVOKE(get_server_prop)(handle,&info);
			if(result == STS_EOK){
				ost << info.srv_server << endl;
			}
			REPORT_OUTPUT(result,ost);
		}

	/* Testing stspi_get_lsu_prop_byname */
	} else if(operation == "get_lsu_prop_byname") {
		ostringstream ost;
		string input = ap->getOption("i");
		string::size_type lsuPos = input.find(",");
		if(string::npos == lsuPos)
		{
			ost<<"Invalig argument "<<input<<endl;
			result = -1;
			REPORT_OUTPUT(result,ost);
		}
		string servername = input.substr(0,lsuPos);
		string lsuname;
		string::size_type nClearFilePos = input.find(",nclrf");
		if(string::npos == nClearFilePos)
		{
			lsuname = input.substr(lsuPos+1);
		}
		else
		{
			lsuname = input.substr(lsuPos+1, nClearFilePos-lsuPos-1);
		}
		sts_session_def_t sd;
		memset(&sd,0,sizeof(sts_session_def_t));
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;

		sts_cred_t cred;
		stsp_server_handle_t handle;
		sts_interface_t iface = "";
		REPORT_OP("open_server");
		sts_server_name_t sname;
		strcpy(sname, servername.c_str());
		result = INVOKE(open_server)(&sd,sname,&cred,iface,&handle);
		ASSERT_SUCCESS(result,"open_server");

		stsp_lsu_t lsu;
		sts_lsu_info_t linfo;
		lsu.sl_u.u_server_handle = handle;
		strncpy(lsu.sl_lsu_name.sln_name,lsuname.c_str(),STS_MAX_LSUNAME);

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("get_lsu_prop_byname");
				result = INVOKE(get_lsu_prop_byname)(
									 (null_pos_flag&0x02)?NULL:&lsu,
									 (null_pos_flag&0x01)?NULL:&linfo);
				REPORT(result);
			}
		} else {
			ostringstream ost;
			REPORT_OP("get_lsu_prop_byname");
			string param = "Server=";
			param+=servername;
			param+=", LSU=";
			param+=lsuname;
			REPORT_PARAM(param);
			result = INVOKE(get_lsu_prop_byname)(&lsu,&linfo);
			if(result == STS_EOK){
				ost << linfo.lsu_def.sld_name.sln_name << endl;
			}
			if(string::npos != nClearFilePos)
			{
				if(STS_SA_CLEARF & linfo.lsu_def.sld_saveas)
				{
					result = -1;
				}
			}
			REPORT_OUTPUT(result,ost);
		}

	/* Testing stspi_open_lsu_list */
	} else if(operation == "open_lsu_list") {

		if(test_type == "NULL"){
			/* lsudef will be NULL in typical NBU use cases, so it will
			 * always be passed NULL */
			stsp_server_handle_t shandle = 0;
			stsp_lsu_list_handle_t lhandle;

			/* 
			* start at 0x01.  starting at 0x00 will send an
			* uninitialized handle to the API.  This causes
			* a crash on Windows
			*/
			for(null_pos_flag=0x01; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("open_lsu_list");

				result = INVOKE(open_lsu_list)(
									 (null_pos_flag&0x02)?NULL:shandle,
									 NULL,
									 (null_pos_flag&0x01)?NULL:&lhandle);
				REPORT(result);
			}
		}
		else
		{
			string param = ap->getOption("i");
			ostringstream ost;
			string::size_type lsuPos = param.find(",");
			if(string::npos == lsuPos)
			{
				ost<<"Invalid parameter to "<<operation<<endl;
				result = -1;
				REPORT_OUTPUT(result, ost);
			}
			else
			{
				string servername = param.substr(0, lsuPos);

				sts_session_def_t sd;
				sd.sd_id = "ID 0";
				sd.sd_log = sd_log_func;
				sts_cred_t cred;
				stsp_server_handle_t handle;
				sts_interface_t iface = "";
				REPORT_OP("open_server");
				sts_server_name_t sname;
				strcpy(sname, servername.c_str());
				result = INVOKE(open_server)(&sd,sname,&cred,iface,&handle);
				ASSERT_SUCCESS(result,"open_server");

				stsp_lsu_list_handle_t lhandle;

				REPORT_OP("open_lsu_list");
				sts_lsu_def_t lsudef;
				memset(&lsudef, 0, sizeof(lsudef));
				strcpy(lsudef.sld_name.sln_name, param.substr(lsuPos+1).c_str());
				result = INVOKE(open_lsu_list)(handle,&lsudef,&lhandle);
				REPORT(result);
			}
		}
	/* Testing stspi_list_lsu */
	} else if(operation == "list_lsu") {

		if(test_type == "NULL"){
			stsp_lsu_list_handle_t lhandle = 0;
			sts_lsu_name_t lsu;

			/* 
			* start at 0x01.  starting at 0x00 will send an
			* uninitialized handle to the API.  This causes
			* a crash on Windows
			*/
			for(null_pos_flag=0x01; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("list_lsu");

				result = INVOKE(list_lsu)(
									 (null_pos_flag&0x02)?NULL:lhandle,
									 (null_pos_flag&0x01)?NULL:&lsu);
				REPORT(result);
			}
		}

	/* Testing stspi_close_lsu_list */
	} else if(operation == "close_lsu_list") {
		REPORT_OP("close_lsu_list");

		if(test_type == "NULL"){
			result = INVOKE(close_lsu_list)(NULL);
			REPORT(result);
		}

	/* Testing stspi_lsulist */
	} else if(operation == "lsulist") {

		string servername = ap->getOption("i");

		sts_session_def_t sd;
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;
		sts_cred_t cred;
		stsp_server_handle_t handle;
		sts_interface_t iface = "";
		REPORT_OP("open_server");
		sts_server_name_t sname;
		strcpy(sname, servername.c_str());
		result = INVOKE(open_server)(&sd,sname,&cred,iface,&handle);
		ASSERT_SUCCESS(result,"open_server");

		stsp_lsu_list_handle_t lhandle;

		REPORT_OP("open_lsu_list");
		result = INVOKE(open_lsu_list)(handle,NULL,&lhandle);
		ASSERT_SUCCESS(result,"open_lsu_list");

		ostringstream ost;
		sts_lsu_name_t lsu;
		result = 0;
		while(1){
			REPORT_OP("list_lsu");
			result = INVOKE(list_lsu)(lhandle,&lsu);
			if(result == STS_ENOENT){
				break;		  
			} else {
				ost << lsu.sln_name << ",";
			}
		}
		REPORT_OP("close_lsu_list");
		result = INVOKE(close_lsu_list)(lhandle);
		REPORT_OUTPUT(result,ost);

	/* Testing stspi_create_image */
	} else if(operation == "create_image") {

		string input = ap->getOption("i");

		int dp1 = 0, dp2 = 0, i=0;
		string ip_params[10];

		while(1){
			dp1 = dp2+1;
			dp2 = (int)input.find(",",dp1);
			ip_params[i] = input.substr((dp1==1)?0:dp1,dp2-((dp1==1)?0:dp1));
			if(dp2 == string::npos) break;
			i++;
		}

		sts_session_def_t sd;
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;
		sts_cred_t cred;
		stsp_server_handle_t handle;
		sts_interface_t iface = "";
		REPORT_OP("open_server");
		result = INVOKE(open_server)(&sd,ip_params[0].c_str(),&cred,iface,&handle);
		ASSERT_SUCCESS(result,"open_server");

		stsp_lsu_t lsu;
		lsu.sl_u.u_server_handle = handle;
		strncpy(lsu.sl_lsu_name.sln_name,ip_params[1].c_str(),STS_MAX_LSUNAME);

		sts_image_def_t imgd;
		memset((void *)&imgd, 0, sizeof(imgd));
		strncpy(imgd.img_basename,ip_params[2].c_str(),STS_MAX_IMAGENAME);
		strncpy(imgd.img_date,ip_params[3].c_str(),STS_MAX_DATE);
		imgd.img_saveas = STS_SA_IMAGE;
		imgd.img_flags = STS_IMG_FULL;
		strncpy(imgd.img_isid.is_dpaid, OSTSDK_PGNTESTER_DPAID, STS_MAX_DPAID-1);
		imgd.img_isid.is_dpaid[STS_MAX_DPAID] = '\0';

		stsp_image_handle_t ihandle;

		if(test_type == "NULL"){

			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("create_image");
				result = INVOKE(create_image)(
									 (null_pos_flag&0x04)?NULL:&lsu,
									 (null_pos_flag&0x02)?NULL:&imgd,
									 STS_O_WRITE,
									 (null_pos_flag&0x01)?NULL:&ihandle);
				REPORT(result);
			}
		} else {
			REPORT_OP("create_image");
			result = INVOKE(create_image)( &lsu, &imgd, STS_O_WRITE, &ihandle);
			REPORT(result);
			REPORT_OP("close_image");
			result = INVOKE(close_image)(ihandle,1,0);

			/* verify that the image was created by doing a READONLY open_image */
			REPORT_OP("open_image");
			result = INVOKE(open_image)( &lsu, &imgd, STS_O_READ, &ihandle);
			if(result != STS_EOK){
				cout << "open_image on created image failed, check if image was created" <<
					 endl;
			}
			REPORT_OP("close_image");
			result = INVOKE(close_image)(ihandle,1,0);
		}

	/* Testing stspi_open_image */
	} else if(operation == "open_image") {

		string input = ap->getOption("i");

		int dp1 = 0, dp2 = 0, i=0;
		string ip_params[10];

		while(1){
			dp1 = dp2+1;
			dp2 = (int)input.find(",",dp1);
			ip_params[i] = input.substr((dp1==1)?0:dp1,dp2-((dp1==1)?0:dp1));
			if(dp2 == string::npos) break;
			i++;
		}

		sts_session_def_t sd;
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;
		sts_cred_t cred;
		stsp_server_handle_t handle;
		sts_interface_t iface = "";
		REPORT_OP("open_server");
		result = INVOKE(open_server)(&sd,ip_params[0].c_str(),&cred,iface,&handle);
		ASSERT_SUCCESS(result,"open_server");

		stsp_lsu_t lsu;
		lsu.sl_u.u_server_handle = handle;
		strncpy(lsu.sl_lsu_name.sln_name,ip_params[1].c_str(),STS_MAX_LSUNAME);

		sts_image_def_t imgd;
		memset((void *)&imgd, 0, sizeof(imgd));
		strncpy(imgd.img_basename,ip_params[2].c_str(),STS_MAX_IMAGENAME);
		strncpy(imgd.img_date,ip_params[3].c_str(),STS_MAX_DATE);
		imgd.img_saveas = STS_SA_IMAGE;
		imgd.img_flags = STS_IMG_FULL;
		strncpy(imgd.img_isid.is_dpaid, OSTSDK_PGNTESTER_DPAID, STS_MAX_DPAID-1);
		imgd.img_isid.is_dpaid[STS_MAX_DPAID] = '\0';

		stsp_image_handle_t ihandle;
		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("open_image");
				result = INVOKE(open_image)(
									 (null_pos_flag&0x04)?NULL:&lsu,
									 (null_pos_flag&0x02)?NULL:&imgd,
									 STS_O_READ,
									 (null_pos_flag&0x01)?NULL:&ihandle);
				REPORT(result);
			}
		} else {
			REPORT_OP("create_image");
			result = INVOKE(create_image)( &lsu, &imgd, STS_O_WRITE, &ihandle);
			ASSERT_SUCCESS(result,"create_image");
			REPORT_OP("close_image");
			result = INVOKE(close_image)(ihandle,1,0);

			REPORT_OP("open_image");
			result = INVOKE(open_image)( &lsu, &imgd, STS_O_READ, &ihandle);
			REPORT(result);
			REPORT_OP("close_image");
			result = INVOKE(close_image)(ihandle,1,0);
		}

	/* Testing stspi_read_image */
	} else if(operation == "read_image") {

		stsp_image_handle_t ihandle = 0;
		void *buf = new char[STS_BLOCK_SIZE];
		sts_uint64_t buflen = STS_BLOCK_SIZE;
		sts_uint64_t bytesRead;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("read_image");
				result = INVOKE(read_image)(
									 (null_pos_flag&0x04)?NULL:ihandle,
									 (null_pos_flag&0x02)?NULL:buf,
									 (null_pos_flag&0x02)?0:buflen,
									 0,
									 (null_pos_flag&0x01)?NULL:&bytesRead);
				REPORT(result);
			}
		}else if (test_type == "INVALID_BUFLEN") {
			string str = ap->getOption("i");
			//int size= atoi(str.c_str());
			int size = STS_BLOCK_SIZE - 1;
			/* read image with buflen not multiple of STS_BLOCK_SIZE */
			REPORT_OP("read_image");
			result = INVOKE(read_image)(
							 ihandle,
							 buf,
							 size,
							 0,
							 &bytesRead);

			REPORT(result);
		}else if (test_type == "INVALID_OFFSET") {
			string str = ap->getOption("i");
			//int size= atoi(str.c_str());
			int size = STS_BLOCK_SIZE - 1;
			/* read image with buflen not multiple of STS_BLOCK_SIZE */
			REPORT_OP("read_image");
			result = INVOKE(read_image)(
							 ihandle,
							 buf,
							 buflen,
							 size,
							 &bytesRead);

			REPORT(result);
		}

	/* Testing stspi_read_image_meta */
	} else if(operation == "read_image_meta") {

		stsp_image_handle_t ihandle = 0;
		void *buf = new char[STS_BLOCK_SIZE];
		sts_uint64_t buflen = STS_BLOCK_SIZE;
		sts_uint64_t bytesRead;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("read_image_meta");
				result = INVOKE(read_image_meta)(
									 (null_pos_flag&0x04)?NULL:ihandle,
									 (null_pos_flag&0x02)?NULL:buf,
									 (null_pos_flag&0x02)?0:buflen,
									 0,
									 (null_pos_flag&0x01)?NULL:&bytesRead);
				REPORT(result);
			}
		}else if (test_type == "INVALID_BUFLEN") {
			string str = ap->getOption("i");
			//int size= atoi(str.c_str());
			int size = STS_BLOCK_SIZE - 1;
			/* read image with buflen not multiple of STS_BLOCK_SIZE */
			REPORT_OP("read_image_meta");
			result = INVOKE(read_image_meta)(
							 ihandle,
							 buf,
							 size,
							 0,
							 &bytesRead);

			REPORT(result);
		}else if (test_type == "INVALID_OFFSET") {
			string str = ap->getOption("i");
			//int size= atoi(str.c_str());
			int size = STS_BLOCK_SIZE - 1;
			/* read image with buflen not multiple of STS_BLOCK_SIZE */
			REPORT_OP("read_image_meta");
			result = INVOKE(read_image_meta)(
							 ihandle,
							 buf,
							 buflen,
							 size,
							 &bytesRead);

			REPORT(result);
		}

	/* Testing stspi_write_image */
	} else if(operation == "write_image") {

		stsp_image_handle_t ihandle = 0;
		sts_stat_t stat;
		void *buf = new char[STS_BLOCK_SIZE];
		sts_uint64_t buflen = STS_BLOCK_SIZE;
		sts_uint64_t bytesWritten;

		if(test_type == "NULL"){

			for(null_pos_flag=0x00; null_pos_flag<16; null_pos_flag++){
				REPORT_OP("write_image");
				result = INVOKE(write_image)(
									 (null_pos_flag&0x08)?NULL:ihandle,
									 (null_pos_flag&0x04)?NULL:&stat,
									 (null_pos_flag&0x02)?NULL:buf,
									 (null_pos_flag&0x02)?0:buflen,
									 0,
									 (null_pos_flag&0x01)?NULL:&bytesWritten);

				REPORT(result);
			}
		}else if (test_type == "INVALID_BUFLEN") {
			string str = ap->getOption("i");
			//int size= atoi(str.c_str());
			int size = STS_BLOCK_SIZE - 1;
			/* write image with buflen not multiple of STS_BLOCK_SIZE */
			REPORT_OP("write_image");
			result = INVOKE(write_image)(
							 ihandle,
							 &stat,
							 buf,
							 size,
							 0,
							 &bytesWritten);

			REPORT(result);
		}else if (test_type == "INVALID_OFFSET") {
			string str = ap->getOption("i");
			//int size= atoi(str.c_str());
			int size = STS_BLOCK_SIZE - 1;
			/* write image with buflen not multiple of STS_BLOCK_SIZE */
			REPORT_OP("write_image");
			result = INVOKE(write_image)(
							 ihandle,
							 &stat,
							 buf,
							 buflen,
							 size,
							 &bytesWritten);

			REPORT(result);
		}
	/* Testing stspi_write_image_meta_meta */
	} else if(operation == "write_image_meta") {

		stsp_image_handle_t ihandle = 0;
		void *buf = new char[STS_BLOCK_SIZE];
		sts_uint64_t buflen = STS_BLOCK_SIZE;
		sts_uint64_t bytesWritten;

		if(test_type == "NULL"){

			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("write_image_meta");
				result = INVOKE(write_image_meta)(
									 (null_pos_flag&0x04)?NULL:ihandle,
									 (null_pos_flag&0x02)?NULL:buf,
									 (null_pos_flag&0x02)?0:buflen,
									 0,
									 (null_pos_flag&0x01)?NULL:&bytesWritten);

				REPORT(result);
			}
		}else if (test_type == "INVALID_BUFLEN") {
			string str = ap->getOption("i");
			//int size= atoi(str.c_str());
			int size = STS_BLOCK_SIZE - 1;
			/* write image with buflen not multiple of STS_BLOCK_SIZE */
			REPORT_OP("write_image_meta");
			result = INVOKE(write_image_meta)(
							 ihandle,
							 buf,
							 size,
							 0,
							 &bytesWritten);

			REPORT(result);
		}else if (test_type == "INVALID_OFFSET") {
			string str = ap->getOption("i");
			//int size= atoi(str.c_str());
			int size = STS_BLOCK_SIZE - 1;
			/* write image with buflen not multiple of STS_BLOCK_SIZE */
			REPORT_OP("write_image_meta");
			result = INVOKE(write_image_meta)(
							 ihandle,
							 buf,
							 buflen,
							 size,
							 &bytesWritten);

			REPORT(result);
		}

	/* Testing stspi_get_image_prop */
	} else if(operation == "get_image_prop") {

		string input = ap->getOption("i");

		int dp1 = 0, dp2 = 0, i=0;
		string ip_params[10];

		while(1){
			dp1 = dp2+1;
			dp2 = (int)input.find(",",dp1);
			ip_params[i] = input.substr((dp1==1)?0:dp1,dp2-((dp1==1)?0:dp1));
			if(dp2 == string::npos) break;
			i++;
		}

		sts_session_def_t sd;
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;
		sts_cred_t cred;
		stsp_server_handle_t handle;
		sts_interface_t iface = "";

		REPORT_OP("open_server");
		result = INVOKE(open_server)(&sd,ip_params[0].c_str(),&cred,iface,&handle);
		ASSERT_SUCCESS(result,"open_server");

		stsp_lsu_t lsu;
		lsu.sl_u.u_server_handle = handle;
		strncpy(lsu.sl_lsu_name.sln_name,ip_params[1].c_str(),STS_MAX_LSUNAME);

		/* create_image with a random name */
		sts_image_def_t imgd;
		memset((void *)&imgd, 0, sizeof(imgd));
		strncpy(imgd.img_basename,ip_params[2].c_str(),STS_MAX_IMAGENAME);
		strncpy(imgd.img_date,ip_params[3].c_str(),STS_MAX_DATE);
		imgd.img_saveas = STS_SA_IMAGE;
		imgd.img_flags = STS_IMG_FULL;
		strncpy(imgd.img_isid.is_dpaid, OSTSDK_PGNTESTER_DPAID, STS_MAX_DPAID-1);
		imgd.img_isid.is_dpaid[STS_MAX_DPAID] = '\0';

		stsp_image_handle_t ihandle;

		REPORT_OP("create_image");
		result = INVOKE(create_image)( &lsu, &imgd, STS_O_WRITE, &ihandle);
		ASSERT_SUCCESS(result,"create_image");

		REPORT_OP("close_image");
		result = INVOKE(close_image)(ihandle,1,0);
		ASSERT_SUCCESS(result,"close_image");

		REPORT_OP("open_image");
		result = INVOKE(open_image)( &lsu, &imgd, STS_O_READ, &ihandle);
		ASSERT_SUCCESS(result,"open_image");


		sts_image_info_t info;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("get_image_prop");
				result = INVOKE(get_image_prop)(
									 (null_pos_flag&0x02)?NULL:ihandle,
									 (null_pos_flag&0x01)?NULL:&info);
				REPORT(result);
			}
		} else {
			ostringstream ost;
			REPORT_OP("get_image_prop");
			result = INVOKE(get_image_prop)(ihandle, &info);
			if(result == STS_EOK){
				ost <<"image.img_basname::"<<info.imo_def.img_basename << endl;
			}
			REPORT_OUTPUT(result,ost);
		}

		REPORT_OP("close_image");
		result = INVOKE(close_image)(ihandle, 1, 0);
		ASSERT_SUCCESS(result,"close_image");


	/* Testing stspi_close_image */
	} else if(operation == "close_image") {

		if(test_type == "NULL"){
			REPORT_OP("close_image");
			result = INVOKE(close_image)(NULL,0,0);
			REPORT(result);
		}
	/* Testing stspi_delete_image */
	} else if (operation == "delete_image") {
		if(test_type == "NULL"){
			stsp_lsu_t lsu;
			sts_image_def_t idef;

			REPORT_OP("delete_image");
			for(null_pos_flag=0x00; null_pos_flag<4; null_pos_flag++){
				result = INVOKE(delete_image)(
									 (null_pos_flag&0x02)?NULL:&lsu,
									 (null_pos_flag&0x01)?NULL:&idef,
									 0);
				REPORT(result);
			}
		}

	/* Testing all image APIs */
	} else if(operation == "open_image_list") {

		if(test_type == "NULL"){
			/* imagedef will be NULL in typical NBU use cases, so it will
			 * always be passed NULL */
			stsp_lsu_t lhandle;
			stsp_image_list_handle_t ihandle;
			int type = STS_FULL_ONLY;

			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("open_image_list");
				result = INVOKE(open_image_list)(
									 (null_pos_flag&0x04)?NULL:&lhandle,
									 (null_pos_flag&0x02)?NULL:type,
									 (null_pos_flag&0x01)?NULL:&ihandle);
				REPORT(result);
			}
		}

	/* Testing stspi_list_image */
	} else if(operation == "list_image") {

		if(test_type == "NULL"){
			stsp_image_list_handle_t ihandle = 0;
			sts_image_def_t image;

			/* 
			* start at 0x01.  starting at 0x00 will send an
			* uninitialized handle to the API.  This causes
			* a crash on Windows
			*/
			for(null_pos_flag=0x01; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("list_image");
				result = INVOKE(list_image)(
									 (null_pos_flag&0x02)?NULL:ihandle,
									 (null_pos_flag&0x01)?NULL:&image);
				REPORT(result);
			}
		}

	/* Testing stspi_get_image_prop_byname */
	} else if(operation == "get_image_prop_byname") {

		string input = ap->getOption("i");

		int dp1 = 0, dp2 = 0, i=0;
		string ip_params[10];

		while(1){
			dp1 = dp2+1;
			dp2 = (int)input.find(",",dp1);
			ip_params[i] = input.substr((dp1==1)?0:dp1,dp2-((dp1==1)?0:dp1));
			if(dp2 == string::npos) break;
			i++;
		}

		sts_session_def_t sd;
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;
		sts_cred_t cred;
		stsp_server_handle_t handle;
		sts_interface_t iface = "";

		REPORT_OP("open_server");
		result = INVOKE(open_server)(&sd,ip_params[0].c_str(),&cred,iface,&handle);
		ASSERT_SUCCESS(result,"open_server");

		stsp_lsu_t lsu;
		lsu.sl_u.u_server_handle = handle;
		strncpy(lsu.sl_lsu_name.sln_name,ip_params[1].c_str(),STS_MAX_LSUNAME);

		/* create_image with a random name */
		sts_image_def_t imgd;
		memset((void *)&imgd, 0, sizeof(imgd));
		strncpy(imgd.img_basename,ip_params[2].c_str(),STS_MAX_IMAGENAME);
		strncpy(imgd.img_date,ip_params[3].c_str(),STS_MAX_DATE);
		imgd.img_saveas = STS_SA_IMAGE;
		imgd.img_flags = STS_IMG_FULL;
		strncpy(imgd.img_isid.is_dpaid, OSTSDK_PGNTESTER_DPAID, STS_MAX_DPAID-1);
		imgd.img_isid.is_dpaid[STS_MAX_DPAID] = '\0';
		stsp_image_handle_t ihandle;

		REPORT_OP("create_image");
		result = INVOKE(create_image)( &lsu, &imgd, STS_O_WRITE, &ihandle);
		ASSERT_SUCCESS(result,"create_image");

		REPORT_OP("close_image");
		result = INVOKE(close_image)(ihandle,1,0);
		ASSERT_SUCCESS(result,"close_image");

		stsp_image_list_handle_t img_list_handle;
		REPORT_OP("open_image_list");
		result = INVOKE(open_image_list)(&lsu,0 ,&img_list_handle);
		ASSERT_SUCCESS(result,"open_image_list");

		sts_image_def_t image;
		REPORT_OP("list_image");
		result = INVOKE(list_image)(img_list_handle, &image);
		ASSERT_SUCCESS(result,"list_image");


		sts_image_info_t info;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("get_image_prop_byname");
				result = INVOKE(get_image_prop_byname)(
									 (null_pos_flag&0x04)?NULL:&lsu,
									 (null_pos_flag&0x02)?NULL:&image,
									 (null_pos_flag&0x01)?NULL:&info);
				REPORT(result);
			}
		} else {
			ostringstream ost;
			REPORT_OP("get_image_prop_byname");
			result = INVOKE(get_image_prop_byname)(&lsu, &image, &info);
			if(result == STS_EOK){
				ost <<"image.img_basname::"<<info.imo_def.img_basename << endl;
			}
			REPORT_OUTPUT(result,ost);
		}

		REPORT_OP("close_image_list");
		result = INVOKE(close_image_list)(img_list_handle);
		ASSERT_SUCCESS(result,"close_image_list");

	/* Testing stspi_close_image_list */
	} else if(operation == "close_image_list") {

		if(test_type == "NULL"){
			REPORT_OP("close_image_list");
			result = INVOKE(close_image_list)(NULL);
			REPORT(result);
		}

	/* Testing stspi_lsulist */
	} else if(operation == "image") {

		string input = ap->getOption("i");

		int dp1 = 0, dp2 = 0, i=0;
		string ip_params[10];

		while(1){
			dp1 = dp2+1;
			dp2 = (int)input.find(",",dp1);
			ip_params[i] = input.substr((dp1==1)?0:dp1,dp2-((dp1==1)?0:dp1));
			if(dp2 == string::npos) break;
			i++;
		}

		sts_session_def_t sd;
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;
		sts_cred_t cred;
		stsp_server_handle_t handle;
		sts_interface_t iface = "";
		REPORT_OP("open_server");
		result = INVOKE(open_server)(&sd,ip_params[0].c_str(),&cred,iface,&handle);
		ASSERT_SUCCESS(result,"open_server");

		stsp_lsu_t lsu;
		lsu.sl_u.u_server_handle = handle;
		strncpy(lsu.sl_lsu_name.sln_name,ip_params[1].c_str(),STS_MAX_LSUNAME);

		/* create_image with a random name */
		sts_image_def_t imgd;
		memset((void *)&imgd, 0, sizeof(imgd));
		strncpy(imgd.img_basename,ip_params[2].c_str(),STS_MAX_IMAGENAME);
		strncpy(imgd.img_date,ip_params[3].c_str(),STS_MAX_DATE);
		imgd.img_saveas = STS_SA_IMAGE;
		imgd.img_flags = STS_IMG_FULL;
		strncpy(imgd.img_isid.is_dpaid, OSTSDK_PGNTESTER_DPAID, STS_MAX_DPAID-1);
		imgd.img_isid.is_dpaid[STS_MAX_DPAID] = '\0';

		stsp_image_handle_t ihandle;

		REPORT_OP("create_image");
		result = INVOKE(create_image)( &lsu, &imgd, STS_O_WRITE, &ihandle);
		ASSERT_SUCCESS(result,"create_image");

		/* write contents of some file into the image */
#define BUF_SIZE 1024
#define SCRATCH_SIZE 64
		sts_stat_t stat;
		unsigned char buf[BUF_SIZE];
		sts_uint64_t offset = 0;
		sts_uint64_t bytesWritten = 0;
		sts_uint64_t bytesRead = 0;
		int bytes_read = 0;
		//int fd;
		//unsigned char md5_str[1024];
		sts_uint64_t total_bytes_written=0,total_bytes_read=0;

		string data = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
		data.resize(STS_BLOCK_SIZE, '0');
		char scratch[STS_BLOCK_SIZE];
		memcpy(scratch,data.c_str(), STS_BLOCK_SIZE); 

		for(int i=0; i<30; i++){	/* Writing 10MB of data with STS_BLOCK_SIZE */
			REPORT_OP("write_image");
			result = INVOKE(write_image)( ihandle, &stat, scratch, 
								 STS_BLOCK_SIZE, offset, &bytesWritten);
			ASSERT_SUCCESS(result,"write_image");
			offset += bytesWritten;
		}

		total_bytes_written = offset;

		sts_image_info_t info;

		/* get the image prop */
		REPORT_OP("get_image_prop");
		result = INVOKE(get_image_prop)(ihandle,&info);
		ASSERT_SUCCESS(result,"get_image_prop");

		/* close the image */
		REPORT_OP("close_image");
		result = INVOKE(close_image)(ihandle,1,0);
		ASSERT_SUCCESS(result,"close_image");

		/* open the image for READ */
		REPORT_OP("open_image");
		result = INVOKE(open_image)( &lsu, &imgd, STS_O_READ, &ihandle);
		ASSERT_SUCCESS(result,"open_image");

		//MD5_CTX md5_context;
		//MD5Init(&md5_context);

		offset = 0;
		/* read the image */
		while(1){
			REPORT_OP("read_image");
			result = INVOKE(read_image)(ihandle,buf,BUF_SIZE,offset,&bytesRead);
			ASSERT_SUCCESS(result,"read_image");
			//MD5Update(&md5_context,buf,BUF_SIZE);
			offset += bytesRead;
			if(bytesRead < BUF_SIZE) break;
		}

		total_bytes_read = offset;

		//MD5Final(md5_str,&md5_context);

		/* compare contents of original file and contents of read image */
		if(total_bytes_written == total_bytes_read){ //TODO: change by md5 comparision
			REPORT(STS_EOK);
		}else{
			cout << "Mismatch in written and read contents " <<
					  total_bytes_written-total_bytes_read << endl;
		}
	/* stspi_get_server_config */
	} else if(operation == "get_server_config") {
		stsp_server_handle_t handle = 0;
		char *buf = new char[STS_BLOCK_SIZE];
		sts_uint32_t buflen = STS_BLOCK_SIZE;
		sts_uint32_t maxlen = 0;

		if(test_type == "NULL"){
			for(null_pos_flag=0x01; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("get_server_config");
				result = INVOKE(get_server_config)(
									 (null_pos_flag&0x04)?NULL:handle,
									 (null_pos_flag&0x02)?NULL:buf,
									 buflen,
									 (null_pos_flag&0x01)?NULL:&maxlen);
				REPORT(result);
			}
		} else {
			ostringstream ost;
			sts_session_def_t sd;
			sd.sd_id = "ID 0";
			sd.sd_log = sd_log_func;
			sts_cred_t cred;
			stsp_server_handle_t handle;
			sts_interface_t iface = "";
			sts_server_info_t info;

			string servername = ap->getOption("i");
			REPORT_OP("open_server");
			sts_server_name_t sname;
			strcpy(sname, servername.c_str());
			result = INVOKE(open_server)(&sd,sname,&cred,iface,&handle);
			ASSERT_SUCCESS(result,"open_server");
			REPORT_OP("get_server_config");
			result = INVOKE(get_server_config)(handle,
					buf, buflen, &maxlen);
			if(result == STS_EOK){
				ost << info.srv_server << endl;
			}
			REPORT_OUTPUT(result,ost);
		}
		
	/* stspi_set_server_config */
	} else if(operation == "set_server_config") {
		stsp_server_handle_t handle = 0;
		char *buf = new char[STS_BLOCK_SIZE];
		char *msg_buf = new char[STS_BLOCK_SIZE];
		sts_uint32_t buflen = STS_BLOCK_SIZE;

		if(test_type == "NULL"){
			for(null_pos_flag=0x01; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("set_server_config");
				result = INVOKE(set_server_config)(
									 (null_pos_flag&0x04)?NULL:handle,
									 (null_pos_flag&0x02)?NULL:buf,
									 (null_pos_flag&0x01)?NULL:msg_buf,
									 buflen);
				REPORT(result);
			}
		} else {
			ostringstream ost;
			sts_session_def_t sd;
			sd.sd_id = "ID 0";
			sd.sd_log = sd_log_func;
			sts_cred_t cred;
			stsp_server_handle_t handle;
			sts_interface_t iface = "";
			sts_server_info_t info;

			string servername = ap->getOption("i");
			REPORT_OP("open_server");
			sts_server_name_t sname;
			strcpy(sname, servername.c_str());
			result = INVOKE(open_server)(&sd,sname,&cred,iface,&handle);
			ASSERT_SUCCESS(result,"open_server");
			REPORT_OP("set_server_config");
			result = INVOKE(set_server_config)(handle,
					buf, msg_buf, buflen);
			if(result == STS_EOK){
				ost << info.srv_server << endl;
			}
			REPORT_OUTPUT(result,ost);
		}

	/* stspi_async_read_image */
	} else if(operation == "async_read_image") {
		
		stsp_image_handle_t ihandle = 0;
		void *buf = new char[STS_BLOCK_SIZE];
		sts_uint64_t buflen = STS_BLOCK_SIZE;
		stsp_opid_t opid;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("async_read_image");
				result = INVOKE(async_read_image)(
									 (null_pos_flag&0x04)?NULL:ihandle,
									 (null_pos_flag&0x02)?NULL:buf,
									 (null_pos_flag&0x02)?0:buflen,
									 0,
									 (null_pos_flag&0x01)?NULL:&opid);
				REPORT(result);
			}
		}else if (test_type == "INVALID_BUFLEN") {
			int size = STS_BLOCK_SIZE - 1;
			/* read image with buflen not multiple of STS_BLOCK_SIZE */
			REPORT_OP("async_read_image");
			result = INVOKE(async_read_image)(
							 ihandle,
							 buf,
							 size,
							 0,
							 &opid);

			REPORT(result);
		}else if (test_type == "INVALID_OFFSET") {
			int size = STS_BLOCK_SIZE - 1;
			/* read image with offset not multiple of STS_BLOCK_SIZE */
			REPORT_OP("async_read_image");
			result = INVOKE(async_read_image)(
							 ihandle,
							 buf,
							 buflen,
							 size,
							 &opid);

			REPORT(result);
		}
	/* stspi_async_wait */
	} else if(operation == "async_wait") {
		sts_session_def_t sd;
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;
		
		stsp_opid_t opid = (stsp_opid_t)"";
		int blockflag = 0;
		sts_aioresult_t aio_result;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("async_wait");
				result = INVOKE(async_wait)(
									 (null_pos_flag&0x04)?NULL:&sd,
									 (null_pos_flag&0x02)?NULL:opid,
									 blockflag,
									 (null_pos_flag&0x01)?NULL:&aio_result);
				REPORT(result);
			}
		}
	/* Testing stspi_async_write_image */
	} else if(operation == "async_write_image") {

		stsp_image_handle_t ihandle = 0;
		sts_stat_t stat;
		void *buf = new char[STS_BLOCK_SIZE];
		sts_uint64_t buflen = STS_BLOCK_SIZE;
		stsp_opid_t opid = (stsp_opid_t)"";

		if(test_type == "NULL"){

			for(null_pos_flag=0x00; null_pos_flag<16; null_pos_flag++){
				REPORT_OP("async_write_image");
				result = INVOKE(async_write_image)(
									 (null_pos_flag&0x08)?NULL:ihandle,
									 (null_pos_flag&0x04)?NULL:&stat,
									 (null_pos_flag&0x02)?NULL:buf,
									 (null_pos_flag&0x02)?0:buflen,
									 0,
									 (null_pos_flag&0x01)?NULL:&opid);

				REPORT(result);
			}
		}else if (test_type == "INVALID_BUFLEN") {
			/* write image with buflen not multiple of STS_BLOCK_SIZE */
			int size = STS_BLOCK_SIZE - 1;
			REPORT_OP("async_write_image");
			result = INVOKE(async_write_image)(
							 ihandle,
							 &stat,
							 buf,
							 size,
							 0,
							 &opid);

			REPORT(result);
		}else if (test_type == "INVALID_OFFSET") {
			int size = STS_BLOCK_SIZE - 1;
			/* write image with buflen not multiple of STS_BLOCK_SIZE */
			REPORT_OP("async_write_image");
			result = INVOKE(async_write_image)(
							 ihandle,
							 &stat,
							 buf,
							 buflen,
							 size,
							 &opid);

			REPORT(result);
		}

	/* stspi_async_cancel */
	} else if(operation == "async_cancel") {
		sts_session_def_t sd;
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;
		
		stsp_opid_t opid = (stsp_opid_t)"";

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("async_cancel");
				result = INVOKE(async_cancel)(
									 (null_pos_flag&0x02)?NULL:&sd,
									 (null_pos_flag&0x01)?NULL:opid);
				REPORT(result);
			}
		}
	/* stspi_async_copy_image */
	} else if(operation == "async_copy_image") {
		stsp_lsu_t to_lsu;
		sts_image_def_t to_img;
		stsp_lsu_t from_lsu;
		sts_image_def_t from_img;
		
		sts_opname_t imageset;
		strcpy(imageset.op_name, "inValiD imageSEt");

		int eventflag = 0;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<16; null_pos_flag++){
				REPORT_OP("async_copy_image");
				result = INVOKE(async_copy_image)(
									 (null_pos_flag&0x08)?NULL:&to_lsu,
									 (null_pos_flag&0x04)?NULL:&to_img,
									 (null_pos_flag&0x02)?NULL:&from_lsu,
									 (null_pos_flag&0x01)?NULL:&from_img,
									 NULL,
									 imageset,
									 eventflag);
				REPORT(result);
			}
		}
		
	/* stspi_copy_image */
	} else if(operation == "copy_image") {
		stsp_lsu_t to_lsu;
		sts_image_def_t to_img;
		stsp_lsu_t from_lsu;
		sts_image_def_t from_img;

		sts_opname_t imageset;
		strcpy(imageset.op_name, "inValiD imageSEt");

		int eventflag = 0;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<16; null_pos_flag++){
				REPORT_OP("copy_image");
				result = INVOKE(copy_image)(
									 (null_pos_flag&0x08)?NULL:&to_lsu,
									 (null_pos_flag&0x04)?NULL:&to_img,
									 (null_pos_flag&0x02)?NULL:&from_lsu,
									 (null_pos_flag&0x01)?NULL:&from_img,
									 imageset,
									 eventflag);
				REPORT(result);
			}
		}
	/* stspi_named_async_cancel */
	} else if(operation == "named_async_cancel") {
		stsp_server_handle_t server_handle = 0;  
		sts_opname_t opname;
		strcpy(opname.op_name, "inValiD opNAmE");

		if(test_type == "NULL"){
			for(null_pos_flag=0x01; null_pos_flag<2; null_pos_flag++){
				REPORT_OP("named_async_cancel");
				result = INVOKE(named_async_cancel)(
									 (null_pos_flag&0x01)?NULL:server_handle,
									 opname);
				REPORT(result);
			}
		}
	/* stspi_named_async_copy_image */
	} else if(operation == "named_async_copy_image") {
		stsp_lsu_t to_lsu;
		sts_image_def_t to_img;
		stsp_lsu_t from_lsu;
		sts_image_def_t from_img;
		sts_opname_t opname;
		strcpy(opname.op_name, "inValiD opNAmE");

		sts_opname_t imageset;
		strcpy(imageset.op_name, "inValiD imageSEt");

		int eventflag = 0;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<16; null_pos_flag++){
				REPORT_OP("named_async_copy_image");
				result = INVOKE(named_async_copy_image)(
									 (null_pos_flag&0x08)?NULL:&to_lsu,
									 (null_pos_flag&0x04)?NULL:&to_img,
									 (null_pos_flag&0x02)?NULL:&from_lsu,
									 (null_pos_flag&0x01)?NULL:&from_img,
									 opname,
									 imageset,
									 eventflag);
				REPORT(result);
			}
		}

	/* stspi_named_async_status */
	} else if(operation == "named_async_status") {
		stsp_server_handle_t server_handle= 0;  
		sts_opname_t opname;
		strcpy(opname.op_name, "inValiD opNAmE");

		if(test_type == "NULL"){
			for(null_pos_flag=0x01; null_pos_flag<2; null_pos_flag++){
				REPORT_OP("named_async_status");
				result = INVOKE(named_async_status)(
									 (null_pos_flag&0x01)?NULL:server_handle,
									 opname);
				REPORT(result);
			}
		}
	/* stspi_named_async_wait */
	} else if(operation == "named_async_wait") {
		stsp_server_handle_t server_handle = 0;  
		sts_opname_t opname;
		strcpy(opname.op_name, "inValiD opNAmE");
		int blockflag = 0;
		sts_aioresult_t aio_result;

		if(test_type == "NULL"){
			for(null_pos_flag=0x01; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("named_async_wait");
				result = INVOKE(named_async_wait)(
									 (null_pos_flag&0x02)?NULL:server_handle,
									 opname,
									 blockflag,
									 (null_pos_flag&0x01)?NULL:&aio_result);
				REPORT(result);
			}
		}
	/* stspi_close_evchannel */
	} else if(operation == "close_evchannel") {
		stsp_evc_handle_t evc_handle = (stsp_evc_handle_t)"";

		if(test_type == "NULL"){
				REPORT_OP("close_evchannel");
				result = INVOKE(close_evchannel)( NULL );
				REPORT(result);
		}
	/* stspi_delete_event */
	} else if(operation == "delete_event") {
		stsp_server_handle_t handle = 0;
		sts_event_t event;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("delete_event");
				result = INVOKE(delete_event)(
									 (null_pos_flag&0x02)?NULL:handle,
									 (null_pos_flag&0x01)?NULL:&event);
				REPORT(result);
			}
		}
	/* stspi_get_event */
	} else if(operation == "get_event") {
		sts_event_t event;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<2; null_pos_flag++){
				REPORT_OP("get_event");
				result = INVOKE(get_event)(
									 NULL,
									 (null_pos_flag&0x01)?NULL:&event);
				REPORT(result);
			}
		}
	/* stspi_open_evchannel */
	} else if(operation == "open_evchannel") {
		sts_session_def_t sd; 
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;
		
		sts_cred_t cred;
		sts_interface_t iface = "";
		
		sts_evhandler_t handler = empty_evc_handler;
		sts_event_t event; 
		int flags = STS_EVF_DELETE_ON_READ; 
		sts_evseqno_t evseqno = 0; 
		stsp_evc_handle_t pevc_handle;

		if(test_type == "NULL"){
			sts_server_name_t sname = "";
			
			for(null_pos_flag=0x00; null_pos_flag<128; null_pos_flag++){
				REPORT_OP("open_evchannel");
				result = INVOKE(open_evchannel)(
									 (null_pos_flag&0x40)?NULL:&sd,
									 (null_pos_flag&0x20)?NULL:sname,
									 (null_pos_flag&0x10)?NULL:&cred,
									 (null_pos_flag&0x08)?NULL:iface,
									 (null_pos_flag&0x04)?NULL:handler,
									 (null_pos_flag&0x02)?NULL:&event,
									 flags,
									 evseqno,
									 (null_pos_flag&0x01)?NULL:&pevc_handle);
				REPORT(result);
			}
		} else {
			string server = ap->getOption("i");
			sts_server_name_t sname;
			strcpy(sname, server.c_str());
			REPORT_OP("open_evchannel");
			result = INVOKE(open_evchannel)(&sd,sname,&cred,iface,handler,&event, flags, evseqno, &pevc_handle);
			REPORT(result);
		}
	/* stspi_get_event_payload */
	} else if(operation == "get_event_payload") {
		stsp_server_handle_t svh = 0;
		char plbuf[STS_BLOCK_SIZE]; 
		sts_event_t event;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("get_event_payload");
				result = INVOKE(get_event_payload)(
									 (null_pos_flag&0x04)?NULL:svh,
									 (null_pos_flag&0x02)?NULL:&plbuf,
									 (null_pos_flag&0x01)?NULL:&event);
				REPORT(result);
			}
		}

		/* not supported in v10
		   stsp_flush_v9_t
		   stsp_copy_extent_v9_t
		   stsp_delete_files_v8_t
		   stsp_get_image_group_v8_t
		   stsp_get_image_group_byname_v10_t
		   stsp_include_in_image_v10_t
		   stsp_ioctl_v9_t
		   stsp_open_target_server_v9_t
		   stsp_open_image_v10_t
		   stsp_open_image_group_list_v10_t
		   stsp_terminate_v7_t
		   stsp_find_lsu_v9_t
		   stsp_label_lsu_v9_t
		*/

	/* stspi_begin_copy_image */
	} else if(operation == "begin_copy_image") {
		stsp_lsu_t to_lsu;
		sts_image_def_t to_img;
		stsp_image_handle_t image_handle = 0;

		sts_opname_t imageset;
		strcpy(imageset.op_name, "inValiD imageSEt");

		int eventflag = 0;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("begin_copy_image");
				result = INVOKE(begin_copy_image)(
									 (null_pos_flag&0x04)?NULL:&to_lsu,
									 (null_pos_flag&0x02)?NULL:&to_img,
									 (null_pos_flag&0x01)?NULL:image_handle,
									 imageset,
									 eventflag);
				REPORT(result);
			}
		}
	/* stspi_end_copy_image */
	} else if(operation == "end_copy_image") {
		if(test_type == "NULL"){
			REPORT_OP("end_copy_image");
			result = INVOKE(end_copy_image)( NULL);
			REPORT(result);
		}
	/* stspi_async_end_copy_image */
	} else if(operation == "async_end_copy_image") {
		stsp_image_handle_t image_handle = 0;
		stsp_opid_t opid;

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("async_end_copy_image");
				result = INVOKE(async_end_copy_image)(
									 (null_pos_flag&0x02)?NULL:image_handle,
									 (null_pos_flag&0x01)?NULL:&opid);
				REPORT(result);
			}
		}
	/* stspi_named_async_end_copy_image */
	} else if(operation == "named_async_end_copy_image") {
		stsp_image_handle_t image_handle = 0;
		sts_opname_t opname;
		strcpy(opname.op_name, "inValiD OPnaMe");

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<2; null_pos_flag++){
				REPORT_OP("named_async_end_copy_image");
				result = INVOKE(named_async_end_copy_image)(
									 (null_pos_flag&0x01)?NULL:image_handle,
									 opname);
				REPORT(result);
			}
		}
	/* stspi_get_lsu_replication_prop */
	} else if(operation == "get_lsu_replication_prop") {
		stsp_lsu_t lsu;
		sts_uint32_t nsource = 0;
		sts_lsu_spec_t source;
		sts_uint32_t ntarget = 0;
		sts_lsu_spec_t target;
		
		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<8; null_pos_flag++){
				REPORT_OP("get_lsu_replication_prop");
				result = INVOKE(get_lsu_replication_prop)(
									 (null_pos_flag&0x04)?NULL:&lsu,
									 nsource,
									 (null_pos_flag&0x02)?NULL:&source,
									 ntarget,
									 (null_pos_flag&0x01)?NULL:&target);
				REPORT(result);
			}
		}
	/* stspi_iocontrol */
	} else if(operation == "iocontrol") {
		stsp_server_handle_t handle = 0;
		int cmd = 0;
		char* args = new char[BUF_SIZE];
		int ioflag = 0;
		sts_uint32_t len = BUF_SIZE;
		
		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("iocontrol");
				result = INVOKE(iocontrol)(
									 (null_pos_flag&0x02)?NULL:handle,
									 cmd,
									 (null_pos_flag&0x01)?NULL:args,
									 ioflag,
									 len);
				REPORT(result);
			}
		}



	} else  {
		LOG_ERROR(-1,"Invalid operation") 
	}

#ifdef WIN32
	FreeLibrary(lib_handle);
#else
	dlclose(lib_handle);
#endif

}

							 &opid);

			REPORT(result);
		}

	/* stspi_async_cancel */
	} else if(operation == "async_cancel") {
		sts_session_def_t sd;
		sd.sd_id = "ID 0";
		sd.sd_log = sd_log_func;
		
		stsp_opid_t opid = (stsp_opid_t)"";

		if(test_type == "NULL"){
			for(null_pos_flag=0x00; null_pos_flag<4; null_pos_flag++){
				REPORT_OP("async_cancel");
				result = INVOKE(async_cancel)(
									 (null_pos_flag&0x02)?NULL:&sd,
									 (null_pos_flag&0x01)?NULL:opid);
				REPORT(result);
			}
		}
	/* s                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                